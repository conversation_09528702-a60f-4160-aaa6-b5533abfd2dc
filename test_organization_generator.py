#!/usr/bin/env python3
"""
Test script to generate 100 organizations using the same backend process
and store the details in a CSV file.

This script replicates the organization analysis process from organization.py
without modifying any existing code.
"""

import os
import sys
import json
import csv
import time
import random
from datetime import datetime
from typing import Dict, List, Any, Optional
import requests
from bs4 import BeautifulSoup
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to Python path to import modules
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

try:
    from src.openengage.agents.brand_analyzer_updated import WebsiteBrandAnalyzerTool
    from src.openengage.agents.archetype_analyzer import analyze_brand_archetypes
except ImportError as e:
    print(f"Warning: Could not import brand analysis tools: {e}")
    print("Brand analysis will be skipped for this test.")

class OrganizationTestGenerator:
    """Test generator for creating 100 organizations with full analysis."""
    
    def __init__(self):
        """Initialize the test generator."""
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.results = []
        self.csv_filename = f"test_organizations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # Sample URLs for testing (mix of different business types)
        self.test_urls = [
            "https://www.allstate.com",
            "https://www.motorola.com",
            "https://www.gsk.com",
            "https://www.wipro.com",
            "https://www.amdocs.com",
            "https://www.gds.ey.com",
            "https://www.adobe.com",
            "https://www.shell.com",
            "https://www.tothenew.com",
            "https://www.tnqtech.com",
            "https://www.gadgetsoftware.com",
            "https://www.pmam.com",
            "https://www.harbingergroup.com",
            "https://www.gyanai.com",
            "https://www.verisk.com",
            "https://www.accenture.com",
            "https://www.adityabirla.com",
            "https://www.westpharma.com",
            "https://www.bt.com",
            "https://www.ideas2it.com",
            "https://www.meritgroup.co.uk",
            "https://www.fibre2fashion.com",
            "https://www.jocata.com",
            "https://www.ntpc.co.in",
            "https://www.honeywell.com",
            "https://www.tenthplanet.in",
            "https://www.cloud.com",
            "https://www.soch.com",
            "https://www.opentext.com",
            "https://www.sony.com",
            "https://www.cxo.net",
            "https://www.cloud.org",
            "https://www.live.com",
            "https://www.hm.com",
            "https://www.jamaav.com",
            "https://www.itester.com",
            "https://www.ab-inbev.com",
            "https://www.imgc.com",
            "https://www.barracuda.com",
            "https://www.teikametrics.com",
            "https://www.servicenow.com",
            "https://www.merckgroup.com",
            "https://www.cba.com.au",
            "https://www.amadeus.com",
            "https://www.akamai.com",
            "https://www.unisys.com",
            "https://www.toasttab.com",
            "https://www.infor.com",
            "https://www.pfizer.com",
            "https://www.kickdrumtech.com",
            "https://www.iiitb.net",
            "https://www.merck.com",
            "https://www.target.com",
            "https://www.vinayakamissions.com",
            "https://www.syf.com",
            "https://www.facillima.com",
            "https://www.pattern.com",
            "https://www.cat.com",
            "https://www.chubb.com",
            "https://www.tranetechnologies.com",
            "https://www.ford.com",
            "https://www.innovizeai.com",
            "https://www.comline.uk.com",
            "https://www.colruytgroup.com",
            "https://www.ericsson.com",
            "https://www.analyticsvidhya.com",
            "https://www.cdmsmith.com",
            "https://www.bcg.com",
            "https://www.ece.iitr.ac.in",
            "https://www.tspolice.gov.in",
            "https://www.juicelabs.ai",
            "https://www.dell.com",
            "https://www.gruve.ai",
            "https://www.roche.com",
            "https://www.rakuten.com",
            "https://www.first-insight.com",
            "https://www.jeevlifeworks.com",
            "https://www.ethostech.com.au",
            "https://www.grofers.com",
            "https://www.zomato.com",
            "https://www.astrazeneca.com",
            "https://www.aexp.com",
            "https://www.paypal.com",
            "https://www.ramarson.com",
            "https://www.inceptivetechnologies.com",
            "https://www.tyson.com",
            "https://www.thomsonreuters.com",
            "https://www.tata.com",
            "https://www.prescienceds.com",
            "https://www.brand-scapes.com",
            "https://www.mphasis.com",
            "https://www.accorian.com",
            "https://www.coforge.com",
            "https://www.syneoshealth.com",
            "https://www.student.tgm.ac.at",
            "https://www.analytx4t.com",
            "https://www.intelliswift.com",
            "https://www.gim.ac.in",
            "https://www.cisco.com"
        ]
        
        # Extend to 100 URLs by cycling through the list
        while len(self.test_urls) < 100:
            self.test_urls.extend(self.test_urls[:min(50, 100 - len(self.test_urls))])
        
        self.test_urls = self.test_urls[:100]  # Ensure exactly 100 URLs
        
    def scrape_website(self, url: str) -> str:
        """Scrape website content using the same method as the backend."""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
                
            # Get text content
            text = soup.get_text(separator='\n', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            content = '\n'.join(lines)
            
            return content
            
        except Exception as e:
            print(f"Error scraping {url}: {str(e)}")
            return ""
    
    def analyze_organization(self, content: str) -> Dict[str, Any]:
        """Analyze organization content using the same method as the backend."""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an organization analyzer. Extract the following details from the provided content:
                        - Business Domain of the organization (Eg:- Edtech, Healthcare etc.)
                        - What We Do
                        - About Us
                        - Class (one of the following: EdTech, E-commerce, Insurance, Banking, Quick-Commerce, TravelTech, Creator Economy Platform, B2B). Food is considered in Quick Commerce and Example of Creator Economy Platform is Substack, Patreon etc.
                        
                        Return the response in JSON format with keys: Domain, WhatWeDo, AboutUs, Class"""
                    },
                    {
                        "role": "user", 
                        "content": f"Analyze this organization content:\n\n{content[:4000]}"
                    }
                ],
                temperature=0.3
            )
            
            result = response.choices[0].message.content
            return json.loads(result)
            
        except Exception as e:
            print(f"Error analyzing organization: {str(e)}")
            return {
                "Domain": "Unknown",
                "WhatWeDo": "Analysis failed",
                "AboutUs": "Analysis failed", 
                "Class": "Unknown"
            }
    
    def analyze_brand(self, url: str) -> Dict[str, Any]:
        """Analyze brand using WebsiteBrandAnalyzerTool if available."""
        try:
            brand_analyzer = WebsiteBrandAnalyzerTool()
            brand_guidelines = brand_analyzer._run(url)
            return brand_guidelines
        except Exception as e:
            print(f"Error analyzing brand for {url}: {str(e)}")
            return {
                "primary_color": "#000000",
                "secondary_color": "#FFFFFF", 
                "font": "Arial",
                "font_size": "16px",
                "cta_type": "Button",
                "mission": "Not available",
                "vision": "Not available",
                "tone_of_voice": "professional"
            }
    
    def analyze_archetypes(self, brand_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze brand archetypes if available."""
        try:
            return analyze_brand_archetypes(brand_data)
        except Exception as e:
            print(f"Error analyzing archetypes: {str(e)}")
            return {
                "primary_archetype": "Unknown",
                "primary_reasoning": "Analysis not available",
                "archetype_scores": {}
            }

    def process_organization(self, url: str, index: int) -> Dict[str, Any]:
        """Process a single organization through the complete analysis pipeline."""
        print(f"\n[{index+1}/100] Processing: {url}")

        result = {
            "index": index + 1,
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None
        }

        try:
            # Step 1: Scrape website content
            print(f"  🔍 Scraping website...")
            content = self.scrape_website(url)
            if not content:
                result["error"] = "Failed to scrape content"
                return result

            # Step 2: Analyze organization
            print(f"  🏢 Analyzing organization...")
            org_data = self.analyze_organization(content)
            result.update(org_data)

            # Step 3: Analyze brand (with error handling)
            print(f"  🎨 Analyzing brand...")
            try:
                brand_data = self.analyze_brand(url)
                result.update({
                    "primary_color": brand_data.get("primary_color", "#000000"),
                    "secondary_color": brand_data.get("secondary_color", "#FFFFFF"),
                    "font": brand_data.get("font", "Arial"),
                    "font_size": brand_data.get("font_size", "16px"),
                    "cta_type": brand_data.get("cta_type", "Button"),
                    "mission": brand_data.get("mission", "Not available"),
                    "vision": brand_data.get("vision", "Not available"),
                    "tone_of_voice": brand_data.get("tone_of_voice", "professional")
                })

                # Step 4: Analyze archetypes
                print(f"  🎭 Analyzing archetypes...")
                archetype_data = self.analyze_archetypes(brand_data)
                result.update({
                    "primary_archetype": archetype_data.get("primary_archetype", "Unknown"),
                    "primary_reasoning": archetype_data.get("primary_reasoning", "Not available")
                })

            except Exception as brand_error:
                print(f"  ⚠️  Brand analysis failed: {brand_error}")
                result.update({
                    "primary_color": "#000000",
                    "secondary_color": "#FFFFFF",
                    "font": "Arial",
                    "font_size": "16px",
                    "cta_type": "Button",
                    "mission": "Analysis failed",
                    "vision": "Analysis failed",
                    "tone_of_voice": "professional",
                    "primary_archetype": "Unknown",
                    "primary_reasoning": "Brand analysis failed"
                })

            result["success"] = True
            print(f"  ✅ Completed successfully")

        except Exception as e:
            result["error"] = str(e)
            print(f"  ❌ Failed: {e}")

        return result

    def save_to_csv(self):
        """Save all results to a CSV file."""
        if not self.results:
            print("No results to save.")
            return

        # Define CSV headers
        headers = [
            "Index", "URL", "Timestamp", "Success", "Error",
            "Domain", "WhatWeDo", "AboutUs", "Class",
            "Primary_Color", "Secondary_Color", "Font", "Font_Size", "CTA_Type",
            "Mission", "Vision", "Tone_of_Voice", "Primary_Archetype", "Primary_Reasoning"
        ]

        print(f"\n💾 Saving results to {self.csv_filename}...")

        with open(self.csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

            for result in self.results:
                # Map result keys to CSV headers
                csv_row = {
                    "Index": result.get("index", ""),
                    "URL": result.get("url", ""),
                    "Timestamp": result.get("timestamp", ""),
                    "Success": result.get("success", False),
                    "Error": result.get("error", ""),
                    "Domain": result.get("Domain", ""),
                    "WhatWeDo": result.get("WhatWeDo", ""),
                    "AboutUs": result.get("AboutUs", ""),
                    "Class": result.get("Class", ""),
                    "Primary_Color": result.get("primary_color", ""),
                    "Secondary_Color": result.get("secondary_color", ""),
                    "Font": result.get("font", ""),
                    "Font_Size": result.get("font_size", ""),
                    "CTA_Type": result.get("cta_type", ""),
                    "Mission": result.get("mission", ""),
                    "Vision": result.get("vision", ""),
                    "Tone_of_Voice": result.get("tone_of_voice", ""),
                    "Primary_Archetype": result.get("primary_archetype", ""),
                    "Primary_Reasoning": result.get("primary_reasoning", "")
                }
                writer.writerow(csv_row)

        print(f"✅ Results saved to {self.csv_filename}")

    def generate_summary_report(self):
        """Generate a summary report of the analysis."""
        if not self.results:
            return

        successful = [r for r in self.results if r.get("success", False)]
        failed = [r for r in self.results if not r.get("success", False)]

        print(f"\n📊 SUMMARY REPORT")
        print(f"=" * 50)
        print(f"Total Organizations Processed: {len(self.results)}")
        print(f"Successful Analyses: {len(successful)}")
        print(f"Failed Analyses: {len(failed)}")
        print(f"Success Rate: {len(successful)/len(self.results)*100:.1f}%")

        if successful:
            # Analyze business classes
            classes = [r.get("Class", "Unknown") for r in successful]
            class_counts = {}
            for cls in classes:
                class_counts[cls] = class_counts.get(cls, 0) + 1

            print(f"\n📈 Business Class Distribution:")
            for cls, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {cls}: {count} ({count/len(successful)*100:.1f}%)")

        if failed:
            print(f"\n❌ Failed Organizations:")
            for result in failed[:5]:  # Show first 5 failures
                print(f"  {result.get('url', 'Unknown')}: {result.get('error', 'Unknown error')}")
            if len(failed) > 5:
                print(f"  ... and {len(failed) - 5} more")

    def run(self):
        """Run the complete test generation process."""
        print("🚀 Starting Organization Test Generator")
        print(f"📋 Processing {len(self.test_urls)} organizations...")
        print(f"💾 Results will be saved to: {self.csv_filename}")

        start_time = time.time()

        for i, url in enumerate(self.test_urls):
            result = self.process_organization(url, i)
            self.results.append(result)

            # Add a small delay to be respectful to websites
            time.sleep(random.uniform(1, 3))

            # Save progress every 10 organizations
            if (i + 1) % 10 == 0:
                self.save_to_csv()
                print(f"\n💾 Progress saved ({i + 1}/100 completed)")

        # Final save and summary
        self.save_to_csv()
        self.generate_summary_report()

        end_time = time.time()
        duration = end_time - start_time
        print(f"\n⏱️  Total execution time: {duration/60:.1f} minutes")
        print(f"📁 Results saved to: {self.csv_filename}")


def main():
    """Main function to run the test generator."""
    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable is required")
        print("Please set your OpenAI API key in the .env file")
        return

    try:
        generator = OrganizationTestGenerator()
        generator.run()
    except KeyboardInterrupt:
        print("\n⏹️  Process interrupted by user")
        if hasattr(generator, 'results') and generator.results:
            generator.save_to_csv()
            print("💾 Partial results saved")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
