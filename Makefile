.PHONY: install test lint clean build docker-build docker-run

install:
	pip install -e ".[dev]"

test:
	pytest tests/ --cov=openengage --cov-report=xml --cov-report=html

lint:
	flake8 src/openengage
	black src/openengage tests
	isort src/openengage tests

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build:
	python setup.py sdist bdist_wheel

docker-build:
	docker-compose build

docker-run:
	docker-compose up

.PHONY: help
help:
	@echo "make install    - Install the package in development mode"
	@echo "make test      - Run tests with coverage"
	@echo "make lint      - Run code quality checks"
	@echo "make clean     - Remove build artifacts"
	@echo "make build     - Build distribution packages"
	@echo "make docker-build - Build Docker image"
	@echo "make docker-run   - Run with Docker Compose"
