# Organization Analyzer Script

This script uses **only** the `OrganizationAnalyzerTool` from `src/openengage/agents.py` to analyze 100 organizations and store the results (Domain, WhatWeDo, AboutUs, Class) in a CSV file.

## Overview

This script is a focused implementation that:
- Uses the existing `OrganizationAnalyzerTool` and `WebScrapingTool` from `agents.py`
- Processes 100 diverse organization URLs
- Extracts exactly 4 fields: Domain, WhatWeDo, AboutUs, Class
- Saves results to a timestamped CSV file
- Provides comprehensive progress tracking and error handling

## Key Features

### ✅ **Uses Existing Tools**
- **OrganizationAnalyzerTool**: Directly from `src/openengage/agents.py`
- **WebScrapingTool**: For content extraction
- **No modifications** to existing codebase

### 📊 **Focused Output**
- **Domain**: Business domain/industry
- **WhatWeDo**: What the organization does
- **AboutUs**: About the organization
- **Class**: Business classification (EdTech, E-commerce, etc.)

### 🛠️ **Robust Processing**
- Error handling for failed scraping/analysis
- Progress tracking with incremental saves
- Respectful rate limiting between requests
- Test mode for quick validation

## Prerequisites

1. **Environment Setup**:
   ```bash
   pip install openai requests beautifulsoup4 python-dotenv crewai langchain
   ```

2. **API Keys**:
   - OpenAI API key (required)
   - Set in `.env` file: `OPENAI_API_KEY=your_key_here`

3. **Project Structure**:
   - Script must be in the project root directory
   - Requires access to `src/openengage/agents.py`

## Usage

### **Test Mode (Recommended First)**
```bash
python organization_analyzer_script.py test
```
- Processes only 5 organizations
- Creates `organization_analysis_SAMPLE_*.csv`
- Quick validation that everything works

### **Full Mode**
```bash
python organization_analyzer_script.py
```
- Processes all 100 organizations
- Creates `organization_analysis_*.csv`
- Takes approximately 30-60 minutes

### **Help**
```bash
python organization_analyzer_script.py --help
```

## Output

### CSV Structure
The generated CSV file contains these columns:
- `Index`: Organization number (1-100)
- `URL`: Organization website URL
- `Timestamp`: When analysis was performed
- `Success`: Whether analysis completed successfully
- `Error`: Error message if analysis failed
- `Domain`: Business domain extracted by OrganizationAnalyzerTool
- `WhatWeDo`: What the organization does
- `AboutUs`: About the organization
- `Class`: Business classification

### Sample Output
```csv
Index,URL,Timestamp,Success,Error,Domain,WhatWeDo,AboutUs,Class
1,https://www.shopify.com,2024-11-29T14:30:22,True,,E-commerce,E-commerce platform for businesses,Leading commerce platform,E-commerce
2,https://www.coursera.org,2024-11-29T14:30:45,True,,Education,Online learning platform,Educational technology company,EdTech
```

## Organization List

The script includes 100 diverse organizations across industries:

### **Technology & SaaS**
- Shopify, Stripe, GitHub, Figma, Slack, Zoom, Dropbox, Canva, Notion

### **Education**
- Coursera, Udemy, Khan Academy

### **Travel & Transportation**
- Airbnb, Uber, Booking.com, Expedia, Southwest, Delta, United, American Airlines

### **Entertainment & Media**
- Netflix, Spotify, Twitch, Discord, YouTube, TikTok

### **E-commerce & Retail**
- Amazon, eBay, Etsy, Target, Walmart, Costco, Best Buy

### **Social Media**
- LinkedIn, Twitter, Instagram, Facebook, Pinterest, Reddit

### **International Airlines**
- British Airways, Lufthansa, Emirates, Air France, KLM, Turkish Airlines

### **And many more...**

## Example Console Output

```
🚀 Starting Organization Analysis using OrganizationAnalyzerTool
📋 Processing 100 organizations...
💾 Results will be saved to: organization_analysis_20241129_143022.csv
🛠️  Using: OrganizationAnalyzerTool from agents.py

[1/100] Processing: https://www.shopify.com
  🔍 Scraping website...
  🏢 Analyzing organization...
  ✅ Completed successfully
     Domain: E-commerce
     Class: E-commerce

[2/100] Processing: https://www.coursera.org
  🔍 Scraping website...
  🏢 Analyzing organization...
  ✅ Completed successfully
     Domain: Education
     Class: EdTech

💾 Progress saved (10/100 completed)

...

📊 SUMMARY REPORT
==================================================
Total Organizations Processed: 100
Successful Analyses: 95
Failed Analyses: 5
Success Rate: 95.0%

📈 Business Class Distribution:
  E-commerce: 35 (36.8%)
  TravelTech: 20 (21.1%)
  EdTech: 15 (15.8%)
  B2B: 12 (12.6%)
  Creator Economy Platform: 8 (8.4%)
  Banking: 5 (5.3%)

🏢 Top Business Domains:
  Technology: 25
  E-commerce: 20
  Travel: 18
  Entertainment: 12
  Finance: 10

⏱️  Total execution time: 45.2 minutes
📁 Results saved to: organization_analysis_20241129_143022.csv
```

## How It Works

### 1. **Website Scraping**
```python
content = self.web_scraper._execute(url)
```
Uses `WebScrapingTool` from `agents.py` to extract clean text content.

### 2. **Organization Analysis**
```python
result = self.org_analyzer._execute(content)
```
Uses `OrganizationAnalyzerTool` from `agents.py` which:
- Sends content to OpenAI GPT-4
- Extracts structured organization data
- Returns JSON with Domain, WhatWeDo, AboutUs, Class

### 3. **Data Processing**
- Validates and cleans the extracted data
- Handles errors gracefully with fallback values
- Saves incrementally to prevent data loss

## Error Handling

The script handles various error scenarios:

### **Network Issues**
- Timeout errors during scraping
- Connection failures
- Rate limiting responses

### **Content Issues**
- Empty or minimal website content
- Blocked scraping attempts
- Invalid HTML structure

### **Analysis Issues**
- OpenAI API errors
- JSON parsing failures
- Missing or invalid data fields

### **Graceful Degradation**
- Failed organizations are logged but don't stop processing
- Partial results are saved if interrupted
- Detailed error messages in CSV for debugging

## Performance

### **Expected Timing**
- **Test Mode (5 orgs)**: 2-5 minutes
- **Full Mode (100 orgs)**: 30-60 minutes
- **Rate Limiting**: 1-3 second delays between requests

### **API Usage**
- Uses OpenAI API for each organization analysis
- Approximately 100 API calls for full run
- Monitor your OpenAI usage/costs

### **Resource Usage**
- Minimal memory footprint
- Network-dependent (requires stable internet)
- CPU usage is light (mostly I/O bound)

## Troubleshooting

### **Import Errors**
```
❌ Error importing tools from agents.py
```
**Solution**: Ensure script is in project root and `src/openengage/agents.py` exists

### **API Key Missing**
```
❌ Error: OPENAI_API_KEY environment variable is required
```
**Solution**: Set your OpenAI API key in `.env` file

### **High Failure Rate**
If many organizations fail:
- Check internet connection
- Verify OpenAI API key is valid and has credits
- Some websites may block automated scraping (this is normal)

### **Interrupting the Process**
- Press `Ctrl+C` to stop
- Partial results are automatically saved
- Resume by modifying the URL list to continue from where you left off

## Integration Notes

This script:
- ✅ **Does not modify** any existing code
- ✅ **Uses existing tools** from `agents.py`
- ✅ **Generates independent** CSV output
- ✅ **Follows same analysis** process as main application

The generated CSV can be used for:
- Data analysis and visualization
- Testing and validation
- Performance benchmarking
- Research and development

## Comparison with Main Test Script

| Feature | organization_analyzer_script.py | test_organization_generator.py |
|---------|--------------------------------|--------------------------------|
| **Tools Used** | OrganizationAnalyzerTool only | Custom implementation |
| **Analysis Scope** | Domain, WhatWeDo, AboutUs, Class | Full brand + archetype analysis |
| **Dependencies** | Existing agents.py tools | Custom brand analyzer tools |
| **Output Focus** | Core organization data | Comprehensive brand analysis |
| **Execution Time** | Faster (simpler analysis) | Slower (comprehensive analysis) |
| **Use Case** | Quick organization classification | Full brand research |
