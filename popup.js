// Enhanced popup implementation with both banner and form variants
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, fetching popup configuration');
    
    // Fetch popup configuration
    fetch('popupdata.json?' + new Date().getTime())  // Add cache-busting parameter
        .then(response => {
            console.log('Fetch response status:', response.status);
            return response.json();
        })
        .then(config => {
            // Log the raw config to verify we're receiving the correct values
            console.log('Loaded popup config:', JSON.stringify(config));
            console.log('Delay value from config:', config.popup_delay_seconds, 'type:', typeof config.popup_delay_seconds);
            console.log('POPUP TYPE from config:', config.popup_type, 'type:', typeof config.popup_type);
            console.log('Form placeholder from config:', config.popup_form_placeholder);
            
            // Create popup only after config is loaded
            createPopup(config);
        })
        .catch(error => {
            console.error('Failed to load popup config:', error);
            // Create popup with defaults if config loading fails
            createPopup({
                popup_title: "Welcome to OpenEngage",
                popup_text: "AI-powered marketing automation for personalized campaigns",
                popup_button_text: "Learn More",
                popup_button_redirect_url: "https://openengage.ai",
                popup_delay_seconds: 2,
                popup_type: "banner" // Default to banner type
            });
        });
});

// Global variable to store popup element reference for dynamic updates
let popupWrapperElement = null;

function createPopup(config) {
    // Configuration with defaults
    const redirectUrl = config.popup_button_redirect_url || 'https://openengage.ai';
    const bgImage = config.popup_background_image || 'popup_bg.jpg';
    const title = config.popup_title || 'Welcome';
    const text = config.popup_text || '';
    const buttonText = config.popup_button_text || 'Learn More';
    const primaryColor = config.popup_primary_color || '#2674ED';
    const buttonColor = config.popup_button_color || '#F9C823';
    const opacity = config.popup_image_opacity || 0.75;
    const popupType = config.popup_type || 'banner'; // Default to banner type if not specified
    const formPlaceholder = config.popup_form_placeholder || 'Enter your email or phone number';
    
    // FIX: Parse the delay seconds value more carefully
    // This is the most critical part to fix the delay issue
    let delaySeconds = 2; // Default fallback
    
    try {
        // First check if the value exists and is not null
        if (config.popup_delay_seconds !== undefined && config.popup_delay_seconds !== null) {
            // Force conversion to number
            const parsedValue = Number(config.popup_delay_seconds);
            
            // Validate it's a proper number
            if (!isNaN(parsedValue)) {
                delaySeconds = parsedValue;
                console.log('Successfully parsed delay value:', delaySeconds);
            } else {
                console.warn('Invalid popup_delay_seconds value:', config.popup_delay_seconds);
            }
        } else {
            console.log('No popup_delay_seconds in config, using default:', delaySeconds);
        }
    } catch (error) {
        console.error('Error parsing delay seconds:', error);
    }
    
    console.log('Final delay value to be used:', delaySeconds);
    console.log('Popup type:', popupType);
    
    // Create popup styles
    const style = document.createElement('style');
    style.textContent = `
        #popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }
        
        #popup-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 500px;
        }
        
        #popup-clickable-link {
            display: block;
            position: relative;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            text-decoration: none;
            color: inherit;
        }
        
        #popup-clickable-link:hover {
            cursor: pointer;
        }
        
        #popup-content {
            position: relative;
            padding: 40px;
            text-align: center;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        #popup-bg-image {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('${bgImage}');
            background-size: cover;
            background-position: center;
            z-index: 1;
        }
        
        #popup-bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, ${opacity});
            z-index: 2;
        }
        
        #popup-inner-content {
            position: relative;
            z-index: 3;
        }
        
        #popup-title {
            color: ${primaryColor};
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        #popup-text {
            color: #333;
            margin-bottom: 20px;
            font-size: 16px;
            white-space: pre-line;
        }
        
        #popup-button {
            background-color: ${buttonColor};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        #popup-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #333;
            cursor: pointer;
            z-index: 10000;
        }
        
        /* Form specific styles */
        #popup-form {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        #popup-form-input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        #popup-form-submit {
            background-color: ${buttonColor};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
    `;
    document.head.appendChild(style);
    
    // Create popup HTML structure based on the type
    let popupHTML = '';
    
    // Add explicit type checking and logging
    console.log('Deciding which popup type to create...');
    console.log('popupType variable:', popupType);
    console.log('popupType === "form":', popupType === 'form');
    console.log('Strict equality check:', popupType === 'form');
    console.log('Type of popupType:', typeof popupType);
    
    if (popupType === 'form') {
        console.log('Creating FORM variant popup');
        // Create form variant
        popupHTML = `
            <div id="popup-overlay">
                <div id="popup-container">
                    <div id="popup-content">
                        <!-- Background layers -->
                        <div id="popup-bg-image"></div>
                        <div id="popup-bg-overlay"></div>
                        
                        <!-- Actual content -->
                        <div id="popup-inner-content">
                            <h2 id="popup-title">${title}</h2>
                            <p id="popup-text">${text}</p>
                            <form id="popup-form">
                                <input type="text" id="popup-form-input" placeholder="${formPlaceholder}" required>
                                <button type="submit" id="popup-form-submit">${buttonText}</button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Close button -->
                    <div id="popup-close">&times;</div>
                </div>
            </div>
        `;
    } else {
        console.log('Creating BANNER variant popup');
        // Create banner variant (original)
        popupHTML = `
            <div id="popup-overlay">
                <div id="popup-container">
                    <!-- The link that wraps everything except the close button -->
                    <a id="popup-clickable-link" href="${redirectUrl}" target="_blank">
                        <div id="popup-content">
                            <!-- Background layers -->
                            <div id="popup-bg-image"></div>
                            <div id="popup-bg-overlay"></div>
                            
                            <!-- Actual content -->
                            <div id="popup-inner-content">
                                <h2 id="popup-title">${title}</h2>
                                <p id="popup-text">${text}</p>
                                <button id="popup-button">${buttonText}</button>
                            </div>
                        </div>
                    </a>
                    
                    <!-- Close button (outside the link) -->
                    <div id="popup-close">&times;</div>
                </div>
            </div>
        `;
    }
    
    // Add popup to document
    document.body.insertAdjacentHTML('beforeend', popupHTML);
    
    // Store the popup wrapper element reference for dynamic updates
    popupWrapperElement = document.getElementById('popup-overlay');
    
    // Add close button functionality
    document.getElementById('popup-close').addEventListener('click', function(e) {
        e.stopPropagation(); // Prevent event bubbling
        document.getElementById('popup-overlay').style.display = 'none';
    });
    
    // Expose methods for dynamic updates
    window.updatePopupBackground = function(imagePath) {
        console.log('Updating popup background image in main popup script:', imagePath);
        if (popupWrapperElement) {
            // Find the popup inner element that contains the background image
            const popupInner = popupWrapperElement.querySelector('#popup-bg-image');
            if (popupInner) {
                // Update the background image with the new path
                popupInner.style.backgroundImage = `url('${imagePath}')`;
                console.log('Popup background updated successfully');
            } else {
                console.warn('Could not find popup inner element');
            }
        } else {
            console.warn('Popup wrapper element not found, cannot update background');
        }
    };
    
    // Add specific handlers based on popup type
    if (popupType === 'form') {
        // Add form submission handler
        document.getElementById('popup-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get the input value
            const inputValue = document.getElementById('popup-form-input').value;
            
            if (inputValue) {
                console.log('Form submitted with value:', inputValue);
                
                // Create submission data
                const submissionData = {
                    contact_info: inputValue,
                    redirect_url: redirectUrl
                };
                
                // Show loading state on button
                const submitButton = document.getElementById('popup-form-submit');
                const originalButtonText = submitButton.textContent;
                submitButton.textContent = 'Submitting...';
                submitButton.disabled = true;
                
                console.log('Submitting form data:', submissionData);
                
                // Send data to the server before redirecting
                // Using try/catch with async/await for better error handling
                (async () => {
                    try {
                        console.log('Sending to: http://localhost:5001/save_popup_lead');
                        
                        const response = await fetch('http://localhost:5001/save_popup_lead', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify(submissionData),
                            mode: 'cors' // Explicitly request CORS
                        });
                        
                        console.log('Raw response:', response);
                        
                        // Check if the response is ok (status in the range 200-299)
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        
                        const data = await response.json();
                        console.log('Success response data:', data);
                        
                        // Wait briefly to show success state
                        submitButton.textContent = 'Success!';
                        submitButton.style.backgroundColor = '#4CAF50';
                        
                        // Redirect to the target URL (product page)
                        console.log('Redirecting to:', redirectUrl);
                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 1000);
                    } catch (error) {
                        console.error('Error details:', error);
                        
                        // Try alternative direct file save approach as fallback
                        try {
                            console.log('Attempting fallback direct save');
                            // Log the event to console for debugging
                            console.log('Form submission data that failed to save:', JSON.stringify(submissionData));
                            
                            // Show error state but still redirect after a delay
                            submitButton.textContent = 'Redirecting...';
                            submitButton.style.backgroundColor = '#ff9800';
                            
                            // Still redirect to avoid user being stuck
                            setTimeout(() => {
                                console.log('Fallback redirect to:', redirectUrl);
                                window.location.href = redirectUrl;
                            }, 1500);
                        } catch (fallbackError) {
                            console.error('Fallback also failed:', fallbackError);
                            // Last resort - just redirect
                            window.location.href = redirectUrl;
                        }
                    }
                })();
            }
        });
    } else {
        // Add additional click handler to ensure link works for banner type
        document.getElementById('popup-clickable-link').addEventListener('click', function() {
            window.location.href = redirectUrl;
        });
    }
    
    // Show popup after configurable delay
    const delayMs = delaySeconds * 1000; // Convert seconds to milliseconds
    console.log(`Popup will display after ${delaySeconds} seconds (${delayMs}ms)`);
    
    setTimeout(function() {
        console.log('Actually showing popup now');
        document.getElementById('popup-overlay').style.display = 'block';
    }, delayMs);
}
