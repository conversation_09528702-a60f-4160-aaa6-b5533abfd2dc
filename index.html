<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenEngage</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .card {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #2980b9;
        }
    </style>
    <!-- Script imports -->
    <!-- The inline popup JavaScript has been replaced by popup.js -->
</head>
<body>
    <div class="container">
        <h1>OpenEngage Dashboard</h1>
        <div class="card">
            <h2>User Segments</h2>
            <div id="userSegments">
                <script type="text/javascript" src="https://ssl.gstatic.com/trends_nrtr/4031_RC01/embed_loader.js"></script> <script type="text/javascript"> trends.embed.renderExploreWidget("GEO_MAP", {"comparisonItem":[{"keyword":"Generative AI","geo":"","time":"today 12-m"}],"category":0,"property":""}, {"exploreQuery":"q=Generative%20AI&hl=en&date=today 12-m","guestPath":"https://trends.google.com:443/trends/embed/"}); </script>    <meta charset="UTF-8">
                <!-- User segments will be populated by JavaScript -->
            </div>
        </div>
        <div class="card">
            <h2>Trigger Points</h2>
            <div id="triggerPoints">
                <!-- Trigger points will be populated by JavaScript -->
            </div>
        </div>
        <div class="card">
            <h2>Campaign Performance</h2>
            <div id="campaignMetrics">
                <!-- Campaign metrics will be populated by JavaScript -->
            </div>
        </div>
    </div>
    <!-- Script imports -->
    <script src="BrahmaScript.js"></script>
    <script src="popup.js"></script>
</body>
</html>
