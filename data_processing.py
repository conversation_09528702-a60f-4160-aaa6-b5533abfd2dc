import gspread
from oauth2client.service_account import ServiceAccountCredentials

class UserData:
    def __init__(self):
        pass

    def import_from_sheets(self, sheet_name, worksheet_name, method='records'):
        scope = ['https://spreadsheets.google.com/feeds','https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name('config/client_secret.json', scope)
        client = gspread.authorize(creds)

        sheet = client.open(sheet_name)
        worksheet=sheet.worksheet(worksheet_name)

        if method=='values':
            list_of_hashes = worksheet.get_all_values()     
        else:
            list_of_hashes = worksheet.get_all_records()

        return pd.DataFrame(list_of_hashes)


    def get_user_data_brahma(self,query,database):
        
        AWS_ACCESS_KEY = os.get_env("AWS_ACCESS_KEY")
        AWS_SECRET_KEY = os.get_env("AWS_SECRET_KEY")
        AWS_REGION = os.get_env("AWS_REGION")

        athena_client = boto3.client(
            "athena",
            aws_access_key_id=AWS_ACCESS_KEY,
            aws_secret_access_key=AWS_SECRET_KEY,
            region_name=AWS_REGION,
        )
        print("Brahma connection established.")

        query_response = athena_client.start_query_execution(
            QueryString=query,
            QueryExecutionContext={"Database": database},
            ResultConfiguration={
                "OutputLocation": "s3://clickstream-athena-query/",
            #     "EncryptionConfiguration": {"EncryptionOption": "SSE_S3"},
            },
        )
        print("Query sent to Brahma")


        while True:
            try:
                # This function only loads the first 1000 rows
                s= athena_client.get_query_results(
                    QueryExecutionId=query_response["QueryExecutionId"]
                )
                break
            except Exception as err:
                if "not yet finished" in str(err):
                    time.sleep(0.001)
                else:
                    raise err

        S3_BUCKET_NAME = "clickstream-athena-query"
        # S3_OUTPUT_DIRECTORY = "output"
        temp_file_location: str = "athenaqueryresult.csv"
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=AWS_ACCESS_KEY,
            aws_secret_access_key=AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        s3_client.download_file(
            S3_BUCKET_NAME,
            f"{query_response['QueryExecutionId']}.csv",
            temp_file_location,
        )
        print("Data obtained from Brahma")
        df_query=pd.read_csv("athenaqueryresult.csv")
        os.remove("athenaqueryresult.csv")
        df_query=df_query.drop_duplicates()

        return df_query


    def get_signup_data(self):
        identity_connection=pcg.connect(os.get_env("SIGNUP_DATA_CONNECTION_LINK"))
        identity_cursor=identity_connection.cursor()
        identity_cursor.execute("select date_joined,first_name,email,signup_source_name,signup_source_url from users_user;")
        rows=identity_cursor.fetchall()
        identity_connection.close()
        signup_data=pd.DataFrame(rows)
        signup_data.columns=["date_joined","first_name","email","signup_source_name","signup_source_url"]
        signup_data["email"]=signup_data["email"].str.lower().str.strip()
        signup_data["date_joined"]=pd.to_datetime(signup_data["date_joined"],format='mixed').dt.tz_localize(None)
        signup_data=signup_data.sort_values(by="date_joined")
        signup_data=signup_data.drop_duplicates(subset=["email"])
        print("Signup data fetched.")
        return signup_data

    def get_bb_buyers(self):
        bb_pur=pd.read_csv("/home/<USER>/drishyamData/Mail_Generator/Data/BBBuyers.csv")
        sales=self.import_from_sheets("AV Sales","Data")
        bb_sales=sales[sales["Product"].isin(["BB plus"])][["Date","Email","Product"]].drop_duplicates()
        bb_sales = bb_sales.assign(Email=bb_sales['Email'].str.split(',')).explode('Email')
        bb_sales.drop_duplicates(subset=["Email","Product"])
        bb_sales.columns=["Date","Email_ID","Product"]
        bb_pur=pd.concat([bb_pur,bb_sales])
        bb_pur=bb_pur.drop_duplicates(subset=["Email_ID"])
        bb_pur.to_csv("/home/<USER>/drishyamData/Mail_Generator/Data/BBBuyers.csv",index=False)
        print("BB+ buyers list fetched")
        return bb_pur
   # Both Gen AI Pinnacle Plus and Pinnacle buyers
    def get_genai_buyers(self):
        genai_pur=pd.read_csv("/home/<USER>/drishyamData/Mail_Generator/Data/GenAI Buyers.csv")
        sales=self.import_from_sheets("AV Sales","Data")
        genai_sales=sales[sales["Product"].str.contains('pinnacle', case=False, na=False)][["Date","Email","Product"]].drop_duplicates()
        genai_sales = genai_sales.assign(Email=genai_sales['Email'].str.split(',')).explode('Email')
        genai_sales.drop_duplicates(subset=["Email","Product"])
        genai_sales.columns=["date_placed","email","Title"]
        genai_pur=pd.concat([genai_pur,genai_sales])
        genai_pur=genai_pur.drop_duplicates(subset=["email"])
        genai_pur.to_csv("/home/<USER>/drishyamData/Mail_Generator/Data/GenAI Buyers.csv",index=False)
        print("GenAI buyers list fetched")
        return genai_pur

    def get_agenticai_buyers(self):
        agenticai_pur=pd.read_csv("/home/<USER>/drishyamData/Mail_Generator/Data/AgenticAI Buyers.csv")
        sales=self.import_from_sheets("AV Sales","Data")
        agenticai_sales=sales[sales["Product"].isin(["Agentic AI"])][["Date","Email","Product"]].drop_duplicates()
        agenticai_sales = agenticai_sales.assign(Email=agenticai_sales['Email'].str.split(',')).explode('Email')
        agenticai_sales.drop_duplicates(subset=["Email","Product"])
        agenticai_sales.columns=["Date","Email_ID","Product"]
        agenticai_pur=pd.concat([agenticai_pur,agenticai_sales])
        agenticai_pur=agenticai_pur.drop_duplicates(subset=["Email_ID","Product"])
        agenticai_pur.to_csv("/home/<USER>/drishyamData/Mail_Generator/Data/AgenticAI Buyers.csv",index=False)
        return agenticai_pur

    def merge_leads_data(self):
        nmp_connection=pcg.connect(os.get_env(LEADS_DATA_CONNECTION_LINK))
        nmp_cursor=nmp_connection.cursor()
        nmp_cursor.execute("select id,created_at,fullname,email,country_code,phone,utm_source,utm_medium,utm_campaign,utm_term,utm_content,additional_data,form_type_id from newmarketingpages_contactform;")
        rows=nmp_cursor.fetchall()
        leads_data=pd.DataFrame(rows)
        leads_data.columns=["id","created_at","name","email","country_code","phone","utm_source","utm_medium","utm_campaign","utm_term","utm_content","additional_data","form_type_id"]
        
        nmp_cursor=nmp_connection.cursor()
        nmp_cursor.execute("select id,form_name,platform_id from newmarketingpages_formtype;")
        rows=nmp_cursor.fetchall()
        form_data=pd.DataFrame(rows)
        form_data.columns=["form_type_id","form_name","platform_id"]
        
        nmp_cursor=nmp_connection.cursor()
        nmp_cursor.execute("select id,platform_name from newmarketingpages_platform;")
        rows=nmp_cursor.fetchall()
        platform_data=pd.DataFrame(rows)
        platform_data.columns=["platform_id","platform_name"]
        
        nmp_connection.close()
        
        leads_data=leads_data.merge(form_data.merge(platform_data,on="platform_id",how ="left"), on ="form_type_id", how="left")
        
        bb=leads_data[(leads_data["platform_name"].astype(str).str.contains("blackbelt"))]
        genai=leads_data[(leads_data["platform_name"].astype(str).str.contains("pinnacle"))]
        agenticai=leads_data[(leads_data["platform_name"].astype(str).str.contains("agenticai"))]
        
        bb=bb[["id","created_at","name","email","country_code","phone","utm_source","utm_medium","utm_campaign","utm_term","utm_content","additional_data"]]
        genai=genai[["id","created_at","name","email","country_code","phone","utm_source","utm_medium","utm_campaign","utm_term","utm_content","additional_data"]]
        agenticai=agenticai[["id","created_at","name","email","country_code","phone","utm_source","utm_medium","utm_campaign","utm_term","utm_content","additional_data"]]
        
        bb_leads=pd.read_csv("/home/<USER>/drishyamData/Mail_Generator/Data/BB_Leads.csv")
        bb_leads=pd.concat([bb_leads,bb])
        bb_leads=bb_leads.drop_duplicates(subset=["id","created_at","email"])
        bb_leads.to_csv("/home/<USER>/drishyamData/Mail_Generator/Data/BB_Leads.csv",index=False)
        genai_leads=pd.read_csv("/home/<USER>/drishyamData/Mail_Generator/Data/Pinnacle_Leads.csv")
        genai_leads=pd.concat([genai_leads,genai])
        genai_leads=genai_leads.drop_duplicates(subset=["id","created_at","email"])
        genai_leads.to_csv("/home/<USER>/drishyamData/Mail_Generator/Data/Pinnacle_Leads.csv",index=False)
        agenticai_leads=pd.read_csv("/home/<USER>/drishyamData/Mail_Generator/Data/AgenticAI_Leads.csv")
        agenticai_leads=pd.concat([agenticai_leads,agenticai])
        agenticai_leads=agenticai_leads.drop_duplicates(subset=["id","created_at","email"])
        agenticai_leads.to_csv("/home/<USER>/drishyamData/Mail_Generator/Data/AgenticAI_Leads.csv",index=False)
        print("Leads data fetched")

    def get_all_unsubscribers(self):
        unsubscribe_connection=pcg.connect(os.get_env(UNSUBSCRIBERS_DATA_CONNECTION_LINK))
        unsubscribe_cursor=unsubscribe_connection.cursor()
        unsubscribe_cursor.execute("select created_at,email,promotional from newnotificationmanager_unsubscribeemail;")
        rows=unsubscribe_cursor.fetchall()
        unsubscribe_connection.close()
    
        unsubscribe_data=pd.DataFrame(rows)
        unsubscribe_data.columns=["created_at","email","Promotional"]
        unsubscribe_data=unsubscribe_data[unsubscribe_data["Promotional"]]
        return unsubscribe_data
