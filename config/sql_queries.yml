# SQL Queries for Email Marketing Insights Agent
# This file contains all SQL queries used in the insights agent

database_setup:
  create_table: |
    CREATE TABLE IF NOT EXISTS campaign_data (
        campaign_id INTEGER PRIMARY KEY AUTOINCREMENT,
        send_date TEXT NOT NULL,
        template_id TEXT,
        subject_line TEXT NOT NULL,
        pre_header_text TEXT,
        email_body TEXT NOT NULL,
        emails_sent INTEGER NOT NULL,
        emails_unsubscribed INTEGER NOT NULL,
        emails_clicked INTEGER NOT NULL,
        emails_opened INTEGER NOT NULL,
        sender_info TEXT NOT NULL
    );

  create_view: |
    CREATE VIEW IF NOT EXISTS campaign_performance AS
    SELECT
        campaign_id,
        send_date,
        template_id,
        subject_line,
        pre_header_text,
        email_body,
        emails_sent,
        emails_unsubscribed,
        emails_clicked,
        emails_opened,
        sender_info,
        CASE
            WHEN emails_sent > 0 THEN (CAST(emails_opened AS REAL) * 100.0 / emails_sent)
            ELSE 0
        END AS open_rate,
        CASE
            WHEN emails_sent > 0 THEN (CAST(emails_clicked AS REAL) * 100.0 / emails_sent)
            ELSE 0
        END AS pcr_ctr,
        CASE
            WHEN emails_opened > 0 THEN (CAST(emails_clicked AS REAL) * 100.0 / emails_opened)
            ELSE 0
        END AS ctor,
        CASE
            WHEN emails_sent > 0 THEN (CAST(emails_unsubscribed AS REAL) * 100.0 / emails_sent)
            ELSE 0
        END AS unsubscribe_rate
    FROM campaign_data;

  insert_sample_data: |
    INSERT INTO campaign_data (
        send_date, template_id, subject_line, pre_header_text, email_body, 
        emails_sent, emails_unsubscribed, emails_clicked, emails_opened, sender_info
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

verification:
  select_sample: "SELECT * FROM campaign_data LIMIT 3;"
  count_rows: "SELECT COUNT(*) FROM campaign_data;"

# Sample data for campaign_data table
sample_data:
  - - '2024-07-01'
    - 'SUMMER24_SALE'
    - '☀️ Summer Sale is ON! Up to 50% Off!'
    - "Don't miss out on hot deals."
    - 'Body for summer sale...'
    - 10000
    - 50
    - 800
    - 2000
    - 'Sales Team <<EMAIL>>'
  - - '2024-07-01'
    - 'NEWS_JULY_W1'
    - 'Weekly Newsletter: Top Stories Inside'
    - 'Your weekly dose of insights.'
    - 'Body for weekly newsletter...'
    - 5000
    - 10
    - 250
    - 1500
    - 'News Team <<EMAIL>>'
  - - '2024-07-02'
    - 'PRODUCT_UPDATE'
    - '🚀 New Feature Alert!'
    - "Check out what's new in our app."
    - 'Body for product update...'
    - 7500
    - 20
    - 900
    - 2500
    - 'Product Team <<EMAIL>>'
  - - '2024-07-03'
    - 'WELCOME_NEW'
    - 'Welcome to Our Community!'
    - 'Glad to have you with us.'
    - 'Body for welcome email...'
    - 100
    - 1
    - 15
    - 60
    - 'Community Team <<EMAIL>>'
  - - '2024-07-03'
    - 'REMINDER_WEBINAR'
    - 'Reminder: Webinar Tomorrow!'
    - 'Join us for an insightful session.'
    - 'Body for webinar reminder...'
    - 2000
    - 5
    - 300
    - 1000
    - 'Events Team <<EMAIL>>'
  - - '2024-07-04'
    - 'HOLIDAY_GREETING'
    - 'Happy Independence Day!'
    - 'Wishing you a joyful holiday.'
    - 'Body for holiday greeting...'
    - 12000
    - 30
    - 600
    - 3000
    - 'Marketing Team <<EMAIL>>'
  - - '2024-07-04'
    - 'FLASH_DEAL_01'
    - '⚡️ Flash Deal: Limited Time Only!'
    - "Grab it before it's gone!"
    - 'Body for flash deal...'
    - 3000
    - 100
    - 750
    - 1500
    - 'Sales Team <<EMAIL>>'
  - - '2024-07-05'
    - 'BLOG_DIGEST'
    - 'Latest on the Blog: Expert Tips'
    - 'Catch up on our newest articles.'
    - 'Body for blog digest...'
    - 6000
    - 15
    - 180
    - 1200
    - 'Content Team <<EMAIL>>'
  - - '2024-07-05'
    - 'ABANDON_CART'
    - 'Did you forget something?'
    - 'Your items are waiting for you!'
    - 'Body for abandon cart...'
    - 500
    - 2
    - 50
    - 200
    - 'Sales Team <<EMAIL>>'
