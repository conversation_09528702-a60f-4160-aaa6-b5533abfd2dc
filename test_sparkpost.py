"""
Test script for SparkPost API key validation.
"""
import sys
from sparkpost import SparkPost
from sparkpost.exceptions import SparkPostAPIException

def test_api_key(api_key):
    """Test if a SparkPost API key is valid."""
    try:
        # Initialize the client
        sp = SparkPost(api_key)
        print("SparkPost client initialized successfully.")
        
        # Try to make a simple API call
        try:
            # Get sending domains (a simple API call that doesn't send emails)
            response = sp.sending_domains.list()
            print("API call successful!")
            print(f"Found {len(response)} sending domains.")
            return True
        except SparkPostAPIException as e:
            print(f"API Error: {e}")
            return False
    except Exception as e:
        print(f"Error initializing SparkPost client: {e}")
        return False

if __name__ == "__main__":
    # Get API key from command line argument or use a default test key
    api_key = sys.argv[1] if len(sys.argv) > 1 else "test_key"
    
    print(f"Testing SparkPost API key: {api_key[:4]}{'*' * (len(api_key) - 8)}{api_key[-4:]}")
    result = test_api_key(api_key)
    
    if result:
        print("API key is valid!")
    else:
        print("API key is invalid or there was an error.")
