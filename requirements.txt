aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
alembic==1.15.1
altair==5.5.0
annotated-types==0.7.0
anthropic==0.40.0
anyio==4.9.0
appdirs==1.4.4
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
auth0-python==4.8.1
azure-common==1.1.28
azure-core==1.32.0
azure-search-documents==11.5.2
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.3
black==25.1.0
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.5.23
click==8.1.8
cohere==5.14.0
coloredlogs==15.0.1
coverage==7.7.1
crewai==0.108.0
cryptography==44.0.2
dataclasses-json==0.6.7
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
distro==1.9.0
docker==7.1.0
docstring_parser==0.16
durationpy==0.9
embedchain==0.1.127
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.115.11
fastavro==1.10.0
filelock==3.18.0
flatbuffers==25.2.10
frozenlist==1.5.0
fsspec==2025.3.0
gitdb==4.0.12
GitPython==3.1.44
google-api-core==2.24.2
google-auth==2.38.0
google-cloud-aiplatform==1.85.0
google-cloud-bigquery==3.30.0
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.2
google-cloud-storage==2.19.0
google-crc32c==1.7.0
google-resumable-media==2.7.2
googleapis-common-protos==1.69.2
gptcache==0.1.44
graphviz==0.20.3
greenlet==3.1.1
grpc-google-iam-v1==0.14.2
grpcio==1.71.0
grpcio-status==1.71.0
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.29.3
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
instructor==1.7.7
ipython==9.0.2
ipython_pygments_lexers==1.1.1
isodate==0.7.2
isort==6.0.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.8.2
joblib==1.4.2
json5==0.10.0
json_repair==0.40.0
jsonpatch==1.33
jsonpickle==4.0.2
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kubernetes==32.0.1
lancedb==0.21.1
langchain==0.3.21
langchain-cohere==0.3.5
langchain-community==0.3.20
langchain-core==0.3.47
langchain-experimental==0.3.4
langchain-openai==0.2.14
langchain-text-splitters==0.3.7
langsmith==0.1.147
litellm==1.60.2
Mako==1.3.9
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mem0ai==0.1.73
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.2.0
mypy==1.15.0
mypy-extensions==1.0.0
narwhals==1.31.0
networkx==3.4.2
nodeenv==1.9.1
numpy==2.2.4
oauthlib==3.2.2
onnxruntime==1.19.2
openai==1.69.0
openpyxl==3.1.5
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-grpc==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-instrumentation==0.52b1
opentelemetry-instrumentation-asgi==0.52b1
opentelemetry-instrumentation-fastapi==0.52b1
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
opentelemetry-util-http==0.52b1
orjson==3.10.15
overrides==7.7.0
packaging==23.2
pandas==2.2.3
parso==0.8.4
pathspec==0.12.1
pdfminer.six==20231228
pdfplumber==0.11.5
pexpect==4.9.0
pillow==11.1.0
platformdirs==4.3.7
plotly==6.0.1
pluggy==1.5.0
portalocker==2.10.1
posthog==3.21.0
prompt_toolkit==3.0.50
propcache==0.3.0
proto-plus==1.26.1
protobuf==5.29.4
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydeck==0.9.1
Pygments==2.19.1
PyJWT==2.10.1
pylance==0.24.1
pypdf==5.4.0
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyright==1.1.397
pysbd==0.3.4
pytest==8.3.5
pytest-cov==6.0.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-json-logger==3.3.0
pytube==15.0.0
pytz==2024.2
pyvis==0.3.2
PyYAML==6.0.2
qdrant-client==1.13.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.23.1
rsa==4.9
schema==0.7.7
scikit-learn==1.6.1
scipy==1.15.2
setuptools==77.0.3
shapely==2.0.7
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sounddevice==0.5.1
soupsieve==2.6
sparkpost==1.3.10
SQLAlchemy==2.0.39
stack-data==0.6.3
starlette==0.46.1
streamlit==1.44.0
streamlit-javascript==0.1.5
streamlit-plotly-events==0.0.6
sympy==1.13.3
tabulate==0.9.0
tenacity==9.0.0
threadpoolctl==3.6.0
tiktoken==0.7.0
tokenizers==0.20.3
toml==0.10.2
tomli==2.2.1
tomli_w==1.2.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
typer==0.15.2
types-requests==2.32.0.20250306
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uv==0.6.9
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
PyPDF2==3.0.1
#guardrails-ai==0.6.5
pycryptodome==3.22.0
# 'crewai[tools]'
boto3
gspread
oauth2client