# Organization Test Generator

This script replicates the organization analysis process from `src/openengage/ui/organization.py` to generate test data for 100 organizations and store the results in a CSV file.

## Overview

The test generator performs the same backend analysis process as the main application:

1. **Website Scraping**: Extracts text content from organization websites
2. **Organization Analysis**: Uses OpenAI GPT-4 to analyze content and extract:
   - Business Domain (e.g., EdTech, Healthcare, E-commerce)
   - What We Do description
   - About Us information
   - Business Class classification
3. **Brand Analysis**: Extracts brand guidelines including:
   - Brand colors (primary, secondary)
   - Typography (font, size)
   - CTA styling
   - Mission and vision statements
   - Tone of voice
4. **Archetype Analysis**: Analyzes brand personality using 12 brand archetypes

## Prerequisites

1. **Environment Setup**:
   ```bash
   pip install openai requests beautifulsoup4 python-dotenv
   ```

2. **API Keys**:
   - OpenAI API key (required)
   - Set in `.env` file: `OPENAI_API_KEY=your_key_here`

3. **Dependencies**:
   - The script attempts to import brand analysis tools from the main codebase
   - If imports fail, brand analysis will be skipped with default values

## Usage

### Basic Usage
```bash
python test_organization_generator.py
```

### What It Does

1. **Processes 100 Organizations**: Uses a predefined list of diverse company URLs
2. **Real-time Progress**: Shows progress for each organization being processed
3. **Error Handling**: Continues processing even if individual organizations fail
4. **Incremental Saves**: Saves progress every 10 organizations
5. **Comprehensive Output**: Generates detailed CSV with all analysis results

### Output Files

The script generates:
- **CSV File**: `test_organizations_YYYYMMDD_HHMMSS.csv` with all organization data
- **Console Output**: Real-time progress and summary statistics

### CSV Columns

The generated CSV includes:
- `Index`: Organization number (1-100)
- `URL`: Organization website URL
- `Timestamp`: When the analysis was performed
- `Success`: Whether the analysis completed successfully
- `Error`: Error message if analysis failed
- `Domain`: Business domain/industry
- `WhatWeDo`: What the organization does
- `AboutUs`: About the organization
- `Class`: Business classification (EdTech, E-commerce, etc.)
- `Primary_Color`: Primary brand color
- `Secondary_Color`: Secondary brand color
- `Font`: Primary font family
- `Font_Size`: Font size
- `CTA_Type`: Call-to-action type
- `Mission`: Company mission statement
- `Vision`: Company vision statement
- `Tone_of_Voice`: Brand tone of voice
- `Primary_Archetype`: Primary brand archetype
- `Primary_Reasoning`: Reasoning for archetype selection

## Sample Organizations

The script includes a diverse set of 100 organizations across different industries:
- **Technology**: Shopify, Stripe, GitHub, Figma
- **Education**: Coursera, Udemy, Khan Academy
- **Travel**: Airbnb, Uber, Booking.com, Expedia
- **Entertainment**: Netflix, Spotify, Twitch, Discord
- **E-commerce**: Amazon, eBay, Etsy, Target
- **Social Media**: LinkedIn, Twitter, Instagram, TikTok
- **Airlines**: Southwest, Delta, United, American
- **And many more...**

## Features

### Error Handling
- Graceful handling of network timeouts
- Continues processing if individual organizations fail
- Saves partial results if interrupted

### Rate Limiting
- Includes random delays (1-3 seconds) between requests
- Respectful to target websites

### Progress Tracking
- Real-time console output showing current progress
- Incremental saves every 10 organizations
- Final summary report with statistics

### Summary Report
The script generates a comprehensive summary including:
- Total organizations processed
- Success/failure rates
- Business class distribution
- Failed organization details

## Example Output

```
🚀 Starting Organization Test Generator
📋 Processing 100 organizations...
💾 Results will be saved to: test_organizations_20241129_143022.csv

[1/100] Processing: https://www.shopify.com
  🔍 Scraping website...
  🏢 Analyzing organization...
  🎨 Analyzing brand...
  🎭 Analyzing archetypes...
  ✅ Completed successfully

[2/100] Processing: https://www.coursera.org
  🔍 Scraping website...
  🏢 Analyzing organization...
  🎨 Analyzing brand...
  🎭 Analyzing archetypes...
  ✅ Completed successfully

...

📊 SUMMARY REPORT
==================================================
Total Organizations Processed: 100
Successful Analyses: 95
Failed Analyses: 5
Success Rate: 95.0%

📈 Business Class Distribution:
  E-commerce: 35 (36.8%)
  TravelTech: 20 (21.1%)
  EdTech: 15 (15.8%)
  B2B: 12 (12.6%)
  Creator Economy Platform: 8 (8.4%)
  Banking: 5 (5.3%)

⏱️  Total execution time: 45.2 minutes
📁 Results saved to: test_organizations_20241129_143022.csv
```

## Notes

- **Execution Time**: Expect 30-60 minutes for 100 organizations
- **API Costs**: Uses OpenAI API for analysis (monitor usage)
- **Network Dependencies**: Requires stable internet connection
- **Respectful Scraping**: Includes delays to avoid overwhelming target sites

## Troubleshooting

### Common Issues

1. **Missing API Key**:
   ```
   ❌ Error: OPENAI_API_KEY environment variable is required
   ```
   Solution: Set your OpenAI API key in the `.env` file

2. **Import Errors**:
   ```
   Warning: Could not import brand analysis tools
   ```
   Solution: Brand analysis will use default values, script continues normally

3. **Network Timeouts**:
   - Individual failures are logged but don't stop the process
   - Check the Error column in the CSV for specific failure reasons

### Interrupting the Process

If you need to stop the script:
- Press `Ctrl+C` to interrupt
- Partial results will be automatically saved
- You can resume by modifying the URL list to start from where you left off

## Integration with Main Application

This script is completely separate from the main application and:
- ✅ Does not modify any existing code
- ✅ Does not interfere with existing data files
- ✅ Uses the same analysis methods as the main application
- ✅ Generates independent test data for analysis

The generated CSV can be used for:
- Testing and validation
- Performance analysis
- Data science experiments
- Benchmarking organization analysis accuracy
