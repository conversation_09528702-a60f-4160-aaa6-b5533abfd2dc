#!/bin/bash

# git branch
# git stash
# git checkout branch_name
# git pull

echo "========================logging into ECR(creds expire after 12 hours)==============="
aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 416391586328.dkr.ecr.ap-south-1.amazonaws.com

echo "========================deleting regcred secrets from namespace if any=============="
kubectl delete secret regcred --namespace $1

echo "========================creating the secrets in namespace $1========================"
kubectl create secret generic regcred  --from-file=.dockerconfigjson=/home/<USER>/.docker/config.json  --type=kubernetes.io/dockerconfigjson --namespace $1

echo "========================Building base image=========================================="
docker build -t $1-web:latest -f k8s/dockerfiles/Dockerfile-base .

# echo "========================Building web application====================================="
# docker build -t $1-web:latest -f k8s/dockerfiles/Dockerfile-web .

# echo "========================Building celery-worker======================================="
# docker build -t $1-celery:latest -f k8s/dockerfiles/Dockerfile-celery .

# echo "========================Building celerybeat=========================================="
# docker build -t $1-celerybeat:latest -f k8s/dockerfiles/Dockerfile-celerybeat .

echo "========================Tagging and pushing the application containers(web)to ecr registry==================="
docker tag $1-web:latest 416391586328.dkr.ecr.ap-south-1.amazonaws.com/$1-web:latest
docker push 416391586328.dkr.ecr.ap-south-1.amazonaws.com/$1-web:latest

# echo "========================Tagging and pushing the application containers(celery)to ecr registry==================="
# docker tag $1-celery:latest 416391586328.dkr.ecr.ap-south-1.amazonaws.com/$1-celery:latest
# docker push 416391586328.dkr.ecr.ap-south-1.amazonaws.com/$1-celery:latest

# echo "========================Tagging and pushing the application containers(celerybeat)to ecr registry==================="
# docker tag $1-celerybeat:latest 416391586328.dkr.ecr.ap-south-1.amazonaws.com/$1-celerybeat:latest
# docker push 416391586328.dkr.ecr.ap-south-1.amazonaws.com/$1-celerybeat:latest

echo "========================Upgrading helm for web================================================"
helm upgrade --install web k8s/web/ --namespace $1
# echo "========================Upgrading helm for celery================================================"
# helm upgrade --install celery k8s/celery/ --namespace $1
# echo "========================Upgrading helm for celerybeat================================================"
# helm upgrade --install celerybeat k8s/celerybeat/ --namespace $1

echo "=======================helm upgraded(waiting for pod to restart sleep 5 sec)========================"
sleep 5
echo "=======================checking your rollout status of deployment(web) untill success max_tries(10 times)====================="

name='deployment "web" successfully rolled out'
count=0
while [ "$name" != "$name2" ] && [ "$count" -lt 10 ]
do
        echo "checking status $count time"
	echo "if it hangs on before 9 counts it means your pod has crashedLoopbackoff don't worry your site is still live"
	echo "Please inform your developer about this if it hangs if count is increasing don't worry everything is fine"
        export name2=$(kubectl rollout status deployment/web --namespace $1)
        count=`expr $count + 1`
	sleep 2
done

echo "================status of rollout for (web, celery, celerybeat)============================"
kubectl rollout status deployment/web --namespace $1
# kubectl rollout status deployment/celery --namespace $1
# kubectl rollout status deployment/celerybeat --namespace $1

echo "showing your all pods see here :)"
kubectl get pods --namespace $1

# echo "====================exporting any pod-name to run migrations and collecstatic========"
# export POD_NAME=$(kubectl --namespace $1 get pod --field-selector status.phase=Running -l app.kubernetes.io/name=web -o jsonpath="{.items[0].metadata.name}")
# echo $POD_NAME

# echo "==============================Applying migrations,Running compress and collectstatic=========================================="
# kubectl --namespace $1 exec -it $POD_NAME -- bash /app/falcon.sh

echo "=============================================================================================================================="
echo "===================================================final pod statuses========================================================"
echo "=============================================================================================================================="
sleep 3
kubectl get pods --namespace $1
echo "=============================================================================================================================="
echo "===================================================final svc statuses========================================================"
echo "=============================================================================================================================="
kubectl get svc --namespace $1
echo "=====================================Kubernetes is on fire!!=================================================================="
THUMBS_UP='\U1F44D';
echo -e $THUMBS_UP
echo "=============================================================================================================================="
