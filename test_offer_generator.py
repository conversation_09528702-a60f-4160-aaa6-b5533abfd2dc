import sqlite3
direct_conn = sqlite3.connect("email_marketing.db")
direct_cursor = direct_conn.cursor()
print("[DEBUG] Direct SQLite connection established")

# Check for tables directly
direct_cursor.execute("SELECT subject_line, CASE WHEN emails_sent > 0 THEN CAST(emails_opened AS REAL) * 100.0 / emails_sent ELSE 0 END AS open_rate FROM campaign_data WHERE emails_sent > 0 ORDER BY open_rate DESC LIMIT 1;")
tables = direct_cursor.fetchall()
print(f"[DEBUG] Tables found via direct connection: {tables}")

for i in tables:
    print(i)