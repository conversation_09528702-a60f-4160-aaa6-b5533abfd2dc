[theme]
# Primary accent color for interactive elements (Green)
primaryColor = "#02C688"

# Background color for the main content area (White)
backgroundColor = "#FFFFFF"

# Background color used for the sidebar and most interactive widgets (Light Gray)
secondaryBackgroundColor = "#F0F2F6"

# Color used for almost all text (Black)
textColor = "#000000"

# Font family for all text in the app, except code blocks (Sans Serif)
font = "sans-serif"

# Color used for text in interactive widgets (like buttons) (Cyan)
textColorSecondary = "#02B9C9"

# Color used for borders and dividers (Purple)
borderColor = "#8D06FE"