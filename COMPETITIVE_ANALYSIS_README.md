# Competitive Analysis Testing Scripts

This repository contains scripts for testing the competitive analysis functionality of the OpenEngage platform.

## Prerequisites

Before running these scripts, you need to have the following:

1. Python 3.7+ installed
2. Required Python packages:
   - pandas
   - requests
   - beautifulsoup4
   - python-dotenv
   - openai
   - (optional) crewai and crewai_tools

3. API Keys:
   - SERPER_API_KEY - for web search functionality
   - OPENAI_API_KEY - for AI-powered analysis

## Setup

1. Clone this repository
2. Install the required packages:
   ```
   pip install pandas requests beautifulsoup4 python-dotenv openai
   ```
3. (Optional) Install CrewAI packages:
   ```
   pip install crewai crewai_tools
   ```
4. Create a `.env` file in the root directory with your API keys:
   ```
   SERPER_API_KEY=your_serper_api_key
   OPENAI_API_KEY=your_openai_api_key
   ```

## Available Scripts

### 1. Simple Competitive Analysis Script

This is a simplified version that focuses on core functionality and is easier to run.

```
python simple_competitive_analysis.py
```

This script will:
- Search for competitors based on the product type
- Validate competitors by comparing them with your product
- Analyze validated competitors to extract key information
- Display a comparison table and save results to a CSV file

### 2. Comprehensive Testing Script

This is a more comprehensive script that includes CrewAI integration and more detailed logging.

```
python test_competitive_analysis_script.py
```

This script provides:
- Detailed logging of each step
- CrewAI integration (if available)
- More robust error handling
- Comprehensive analysis of competitors

### 3. Jupyter Notebook

For interactive testing and debugging, you can use the Jupyter notebook:

```
jupyter notebook test_competitive_analysis.ipynb
```

## Customizing Product Details

You can customize the product details in any of the scripts by modifying the `product_details` dictionary:

```python
product_details = {
    "Product_Name": "Your Product Name",
    "Product_URL": "https://your-product-url.com",
    "Company_Name": "Your Company",
    "Type_of_Product": "Your Product Type",
    "Product_Features": [
        "Feature 1",
        "Feature 2",
        "Feature 3"
    ],
    "Product_Summary": "A detailed description of your product..."
}
```

## Integration with OpenEngage

These scripts can be used to test and debug the competitive analysis functionality before integrating it with the OpenEngage platform. The core functions can be directly incorporated into the OpenEngage codebase.

## Troubleshooting

If you encounter issues:

1. Check that your API keys are correct and have sufficient credits
2. Ensure all required packages are installed
3. Check the console output for detailed error messages
4. Try running the simplified script first to isolate issues

## Output

The scripts will generate:
1. Console output with detailed information about each step
2. A CSV file with the competitive analysis results
3. A pandas DataFrame with the comparison table
