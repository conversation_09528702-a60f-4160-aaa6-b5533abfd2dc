"""
OpenEngage Demo Launcher
Launches both the demo blog server and OpenEngage application
"""
import os
import sys
import subprocess
import threading
import time
import webbrowser

def run_command(command, cwd=None):
    """Run a command in a new process"""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=True,
        cwd=cwd,
        text=True
    )
    return process

def start_demo_blog_server():
    """Start the demo blog web server"""
    print("Starting demo blog server...")
    demo_blog_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "demo_blog")
    server_process = run_command("python -m http.server 8000", cwd=demo_blog_dir)
    print(f"Demo blog server running at http://localhost:8000")
    return server_process

def start_openengage():
    """Start the OpenEngage application"""
    print("Starting OpenEngage application...")
    openengage_dir = os.path.dirname(os.path.abspath(__file__))
    openengage_process = run_command("streamlit run src/openengage/app.py", cwd=openengage_dir)
    print(f"OpenEngage running at http://localhost:8501")
    return openengage_process

def monitor_process(process, name):
    """Monitor a process and print its output"""
    while True:
        output = process.stdout.readline()
        if output:
            print(f"[{name}] {output.strip()}")
        elif process.poll() is not None:
            break

def main():
    """Main entry point"""
    try:
        # Start the demo blog server
        demo_server = start_demo_blog_server()
        
        # Start OpenEngage
        openengage = start_openengage()
        
        # Create monitoring threads
        demo_thread = threading.Thread(target=monitor_process, args=(demo_server, "DEMO BLOG"))
        openengage_thread = threading.Thread(target=monitor_process, args=(openengage, "OPENENGAGE"))
        
        # Start threads
        demo_thread.daemon = True
        openengage_thread.daemon = True
        demo_thread.start()
        openengage_thread.start()
        
        # Wait a moment before opening browser windows
        time.sleep(3)
        
        # Open the demo blog in a browser
        webbrowser.open("http://localhost:8000")
        
        # Open OpenEngage in a browser
        webbrowser.open("http://localhost:8501")
        
        print("\nBoth servers are running!")
        print("Demo Blog: http://localhost:8000")
        print("OpenEngage: http://localhost:8501")
        print("\nPress Ctrl+C to stop all services")
        
        # Keep the main thread alive until Ctrl+C
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nShutting down all services...")
        # Cleanup will happen automatically when the script exits
        sys.exit(0)

if __name__ == "__main__":
    main()
