from crewai import Crew, Task, Agent
# Import directly from the agents.py file in the current directory
from agents import AgentFactory, ProductAnalyzerTool, WebScrapingTool, OrganizationAnalyzerTool
# Import the brand analyzer from our new agents package
from agents.brand_analyzer import WebsiteBrandAnalyzerTool
from pydantic import BaseModel, Field
import json
import os
import re
import logging
import datetime as dt
from dotenv import load_dotenv
from typing import Dict, Any, Optional
import yaml
from pathlib import Path
from datetime import datetime
from utils.file_utils import load_product_details
import PyPDF2
import mimetypes
import sys

# Add parent directory to path to allow imports regardless of how the script is run
current_dir = os.path.dirname(os.path.abspath(__file__))+"/guardrails"
sys.path.insert(0, current_dir)

# Direct import from the guardrails directory
from guardrails_email import validate_email

# Load environment variables
load_dotenv()

# Setup logging for EmailCampaignOutput debugging
def setup_email_campaign_logging():
    """Setup comprehensive logging for EmailCampaignOutput debugging."""
    # Create logs directory if it doesn't exist
    logs_dir = 'data/logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)

    # Create a unique log file name with timestamp
    timestamp = dt.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"email_campaign_output_{timestamp}.log"
    log_filepath = os.path.join(logs_dir, log_filename)

    # Configure logger
    logger = logging.getLogger('EmailCampaignOutput')
    logger.setLevel(logging.DEBUG)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler for detailed logging
    file_handler = logging.FileHandler(log_filepath, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)

    # Create console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create detailed formatter for file
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Create simple formatter for console
    console_formatter = logging.Formatter(
        '%(levelname)s - %(funcName)s - %(message)s'
    )

    file_handler.setFormatter(file_formatter)
    console_handler.setFormatter(console_formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # Log the initialization
    logger.info(f"EmailCampaignOutput logging initialized. Log file: {log_filepath}")

    return logger

# Initialize logging
email_campaign_logger = setup_email_campaign_logging()

class ScrapingInput(BaseModel):
    url: str = Field(description="The URL of the website to scrape")
    
class EmailCampaignOutput(BaseModel):
    """Model for structured email campaign output.
    
    Note: This preserves the exact format required:
    Subject: [subject]
    Pre-header: [pre-header]
    [email body with paragraphs separated by \n\n]
    """
    subject: str = Field(description="The email subject line")
    pre_header: str = Field(description="Brief pre-header text that complements the subject line")
    body: str = Field(description="Email body content with paragraphs separated by \n\n")
    
    def __str__(self) -> str:
        """Convert the model to the expected string format."""
        return f"Email Content:\nSubject: {self.subject}\nPre-header: {self.pre_header}\n\n{self.body}"
    
    @classmethod
    def parse_raw_response(cls, raw_response: str) -> "EmailCampaignOutput":
        """Parse a raw response string into an EmailCampaignOutput object.

        Handles multiple response formats:
        1. Original format: Subject: ... Pre-header: ... [body]
        2. New format 1: Email Content: Subject: ... Pre-header: ... Body: ...
        3. New format 2: Email Content: Subject: ... Pre-header: ... Email Body: ...
        4. New format 3: Email Content: Subject: ... Pre-header: ... [body content directly without label]
        """
        email_campaign_logger.info("Starting parse_raw_response")
        email_campaign_logger.debug(f"Raw response type: {type(raw_response)}")
        email_campaign_logger.debug(f"Raw response length: {len(str(raw_response))}")
        email_campaign_logger.debug(f"Raw response content: {repr(str(raw_response)[:500])}{'...' if len(str(raw_response)) > 500 else ''}")

        # Ensure we're working with a string
        response = str(raw_response)
        email_campaign_logger.debug(f"Converted to string, length: {len(response)}")

        # Extract subject, pre-header and body
        subject = ""
        pre_header = ""
        body = ""

        email_campaign_logger.debug("Initialized empty subject, pre_header, and body")

        # Remove "Email Content:" prefix if present
        if "Email Content:" in response:
            email_campaign_logger.info("Found 'Email Content:' prefix, removing it")
            response = response.split("Email Content:", 1)[1].strip()
            email_campaign_logger.debug(f"Response after removing prefix: {repr(response[:200])}{'...' if len(response) > 200 else ''}")

        # Check for new format with explicit "Body:" or "Email Body:" labels
        if ("Body:" in response and response.count("Body:") == 1) or ("Email Body:" in response and response.count("Email Body:") == 1):
            email_campaign_logger.info("Detected format with explicit Body/Email Body labels")
            # Handle new format: Subject: ... Pre-header: ... Body: ... or Email Body: ...

            # Extract subject
            if "Subject:" in response:
                email_campaign_logger.debug("Extracting subject from response")
                subject_parts = response.split("Subject:", 1)[1].strip()
                email_campaign_logger.debug(f"Subject parts: {repr(subject_parts[:100])}{'...' if len(subject_parts) > 100 else ''}")

                if "Pre-header:" in subject_parts:
                    subject = subject_parts.split("Pre-header:", 1)[0].strip()
                    email_campaign_logger.debug(f"Subject extracted (with pre-header): {repr(subject)}")
                else:
                    # If no pre-header, look for Body: or Email Body:
                    if "Body:" in subject_parts:
                        subject = subject_parts.split("Body:", 1)[0].strip()
                        email_campaign_logger.debug(f"Subject extracted (with Body:): {repr(subject)}")
                    elif "Email Body:" in subject_parts:
                        subject = subject_parts.split("Email Body:", 1)[0].strip()
                        email_campaign_logger.debug(f"Subject extracted (with Email Body:): {repr(subject)}")
                    else:
                        subject = subject_parts.strip()
                        email_campaign_logger.debug(f"Subject extracted (no other labels): {repr(subject)}")
            else:
                email_campaign_logger.warning("No 'Subject:' found in response")

            # Extract pre-header
            if "Pre-header:" in response:
                email_campaign_logger.debug("Extracting pre-header from response")
                pre_header_parts = response.split("Pre-header:", 1)[1].strip()
                email_campaign_logger.debug(f"Pre-header parts: {repr(pre_header_parts[:100])}{'...' if len(pre_header_parts) > 100 else ''}")

                if "Email Body:" in pre_header_parts:
                    pre_header = pre_header_parts.split("Email Body:", 1)[0].strip()
                    email_campaign_logger.debug(f"Pre-header extracted (with Email Body:): {repr(pre_header)}")
                elif "Body:" in pre_header_parts:
                    pre_header = pre_header_parts.split("Body:", 1)[0].strip()
                    email_campaign_logger.debug(f"Pre-header extracted (with Body:): {repr(pre_header)}")
                else:
                    pre_header = pre_header_parts.strip()
                    email_campaign_logger.debug(f"Pre-header extracted (no body label): {repr(pre_header)}")
            else:
                email_campaign_logger.warning("No 'Pre-header:' found in response")

            # Extract body
            if "Email Body:" in response:
                email_campaign_logger.debug("Extracting body using 'Email Body:' label")
                body = response.split("Email Body:", 1)[1].strip()
                email_campaign_logger.debug(f"Body extracted: {repr(body[:200])}{'...' if len(body) > 200 else ''}")
            elif "Body:" in response:
                email_campaign_logger.debug("Extracting body using 'Body:' label")
                body = response.split("Body:", 1)[1].strip()
                email_campaign_logger.debug(f"Body extracted: {repr(body[:200])}{'...' if len(body) > 200 else ''}")
            else:
                email_campaign_logger.warning("No body label found in response")

        elif "Subject:" in response and "Pre-header:" in response:
            email_campaign_logger.info("Detected format without explicit body labels (Subject + Pre-header)")
            # Handle new format without explicit body label: Subject: ... Pre-header: ... [body content directly]

            # Extract subject
            email_campaign_logger.debug("Extracting subject from Subject/Pre-header format")
            subject_parts = response.split("Subject:", 1)[1].strip()
            if "Pre-header:" in subject_parts:
                subject = subject_parts.split("Pre-header:", 1)[0].strip()
                email_campaign_logger.debug(f"Subject extracted: {repr(subject)}")
            else:
                subject = subject_parts.strip()
                email_campaign_logger.debug(f"Subject extracted (no pre-header separation): {repr(subject)}")

            # Extract pre-header and body
            if "Pre-header:" in response:
                email_campaign_logger.debug("Extracting pre-header and body using word-based separation")
                pre_header_parts = response.split("Pre-header:", 1)[1].strip().replace("\\n", "\n").replace("\n", " \n")
                email_campaign_logger.debug(f"Pre-header parts: {repr(pre_header_parts[:200])}{'...' if len(pre_header_parts) > 200 else ''}")

                # Split by first space after pre-header to separate pre-header from body
                # Look for the end of the pre-header (usually ends with punctuation or before "Dear")
                words = pre_header_parts.split(" ")
                email_campaign_logger.debug(f"Split into {len(words)} words")
                pre_header_end_idx = 0

                # Find where pre-header likely ends - look for common email body starters
                for i, word in enumerate(words):
                    if word.lower() in ['dear', 'hello', 'hi', 'greetings', 'hey']:
                        email_campaign_logger.debug(f"Found greeting word '{word}' at index {i}")
                        pre_header_end_idx = i
                        break
                    # If we find a sentence ending followed by a greeting, that's likely the end of pre-header
                    if word.endswith(('!', '.', '?')) and i < len(words) - 1:
                        next_word = words[i + 1].lower()
                        if next_word in ['dear', 'hello', 'hi', 'greetings', 'hey']:
                            email_campaign_logger.debug(f"Found sentence ending '{word}' followed by greeting '{next_word}' at index {i}")
                            pre_header_end_idx = i + 1
                            break

                email_campaign_logger.debug(f"Pre-header end index determined: {pre_header_end_idx}")

                if pre_header_end_idx > 0:
                    pre_header = ' '.join(words[:pre_header_end_idx])
                    body = ' '.join(words[pre_header_end_idx:])
                    email_campaign_logger.debug(f"Pre-header extracted (greeting-based): {repr(pre_header)}")
                    email_campaign_logger.debug(f"Body extracted (greeting-based): {repr(body[:200])}{'...' if len(body) > 200 else ''}")
                else:
                    email_campaign_logger.debug("No greeting found, using sentence-based fallback")
                    # Fallback: assume first sentence is pre-header
                    sentences = pre_header_parts.split('. ', 1)
                    if len(sentences) > 1:
                        pre_header = sentences[0] + '.'
                        body = sentences[1]
                        email_campaign_logger.debug(f"Pre-header extracted (sentence-based): {repr(pre_header)}")
                        email_campaign_logger.debug(f"Body extracted (sentence-based): {repr(body[:200])}{'...' if len(body) > 200 else ''}")
                    else:
                        email_campaign_logger.debug("No sentence separation found, using word-count fallback")
                        # If no clear separation, treat first part as pre-header
                        words = pre_header_parts.split()
                        if len(words) > 10:  # If long, split roughly in half
                            mid_point = min(10, len(words) // 2)
                            pre_header = ' '.join(words[:mid_point])
                            body = ' '.join(words[mid_point:])
                            email_campaign_logger.debug(f"Pre-header extracted (word-count): {repr(pre_header)}")
                            email_campaign_logger.debug(f"Body extracted (word-count): {repr(body[:200])}{'...' if len(body) > 200 else ''}")
                        else:
                            pre_header = pre_header_parts
                            body = ""
                            email_campaign_logger.debug(f"Short content, all as pre-header: {repr(pre_header)}")
                            email_campaign_logger.warning("No body content extracted")

        else:
            email_campaign_logger.info("Using original format parsing (fallback)")
            # Handle original format: Subject: ... Pre-header: ... [body without explicit label]

            # Extract subject
            if "Subject:" in response:
                email_campaign_logger.debug("Extracting subject from original format")
                parts = response.split("Subject:", 1)
                response = parts[1].strip()
                email_campaign_logger.debug(f"Response after subject extraction: {repr(response[:100])}{'...' if len(response) > 100 else ''}")

                # Split by next field or use the whole text
                if "Pre-header:" in response:
                    subject_parts = response.split("Pre-header:", 1)
                    subject = subject_parts[0].strip()
                    response = "Pre-header:" + subject_parts[1]
                    email_campaign_logger.debug(f"Subject extracted (with pre-header): {repr(subject)}")
                else:
                    lines = response.split('\n', 1)
                    subject = lines[0].strip()
                    response = lines[1] if len(lines) > 1 else ""
                    email_campaign_logger.debug(f"Subject extracted (line-based): {repr(subject)}")
            else:
                email_campaign_logger.warning("No 'Subject:' found in original format")

            # Extract pre-header
            if "Pre-header:" in response:
                email_campaign_logger.debug("Extracting pre-header from original format")
                parts = response.split("Pre-header:", 1)
                response = parts[1].strip()
                email_campaign_logger.debug(f"Response after pre-header extraction: {repr(response[:100])}{'...' if len(response) > 100 else ''}")

                # Get pre-header text until next line break
                lines = response.split('\n', 1)
                pre_header = lines[0].strip()
                body = lines[1].strip() if len(lines) > 1 else ""
                email_campaign_logger.debug(f"Pre-header extracted: {repr(pre_header)}")
                email_campaign_logger.debug(f"Body extracted: {repr(body[:200])}{'...' if len(body) > 200 else ''}")
            else:
                email_campaign_logger.debug("No 'Pre-header:' found, treating remaining as body")
                body = response.strip()
                email_campaign_logger.debug(f"Body extracted (no pre-header): {repr(body[:200])}{'...' if len(body) > 200 else ''}")
        subject=subject.strip()
        pre_header=pre_header.strip()
        body=body.strip().replace(" \n","\n")
        # Log final extracted values before cleaning
        email_campaign_logger.info("Final extracted values before cleaning:")
        email_campaign_logger.info(f"Subject: {repr(subject)}")
        email_campaign_logger.info(f"Pre-header: {repr(pre_header)}")
        email_campaign_logger.info(f"Body: {repr(body[:200])}{'...' if len(body) > 200 else ''}")

        # Check for escaped newlines and log them
        if "\\n" in subject:
            email_campaign_logger.warning(f"Found escaped newlines in subject: {repr(subject)}")
        if "\\n" in pre_header:
            email_campaign_logger.warning(f"Found escaped newlines in pre-header: {repr(pre_header)}")
        if "\\n" in body:
            email_campaign_logger.warning(f"Found escaped newlines in body (first 200 chars): {repr(body[:200])}")

        email_campaign_logger.info("parse_raw_response completed successfully")
        return cls(subject=subject, pre_header=pre_header, body=body)

class WebScrapingTool(WebScrapingTool):
    """Tool for scraping content from a given URL."""

    name: str = "Web Scraper"
    description: str = "Scrape content from a given URL"

    def _execute(self, url: str) -> str:
        """Execute the web scraping for the given URL."""
        import requests
        from bs4 import BeautifulSoup

        try:
            # Send request with headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # Parse content
            soup = BeautifulSoup(response.text, 'html.parser')

            # Remove unwanted elements
            for element in soup(['script', 'style']):
                element.decompose()

            return soup.get_text()

        except Exception as e:
            raise Exception(f"Failed to scrape website: {str(e)}")

    def _run(self, url: str) -> str:
        return self._execute(url)

class OpenEngageCrew:
    """Main class for managing the OpenEngage marketing automation system"""

    def __init__(self):
        """Initialize the OpenEngage crew with necessary agents"""
        self.agent_factory = AgentFactory()
        self.master_agent = self.agent_factory.create_master_agent()
        self.campaign_agent = self.agent_factory.create_campaign_agent()
        self.analyst_agent = self.agent_factory.create_analyst_agent()

        # Create personalized email agent for end-to-end generation
        self.personalized_agent = self.agent_factory.create_personalized_email_agent()

        # Initialize tools
        self.scraper = WebScrapingTool()
        self.org_analyzer = OrganizationAnalyzerTool()
        self.product_analyzer = ProductAnalyzerTool()

        # Create the crew
        self.crew = Crew(
            agents=[self.master_agent, self.campaign_agent, self.analyst_agent, self.personalized_agent],
            tasks=[],
            verbose=True
        )

        # Initialize product data
        self.product_data = None
        self.organization_data = None

        # Load prompts from YAML file
        self.prompts = self._load_prompts()

    def _load_prompts(self):
        """Load prompt templates from YAML file."""
        prompts_path = Path(__file__).parent.parent.parent / 'config' / 'prompts.yml'
        with open(prompts_path, 'r') as f:
            return yaml.safe_load(f)

    def process_user_request(self, user_input: str) -> str:
        """Process a user request and return the appropriate response"""
        # Check if this is a simple greeting
        greeting_pattern = r'^(hi|hello|hey|greetings|howdy|hi there|hello there)[\s!.]*$'
        is_greeting = bool(re.match(greeting_pattern, user_input.lower()))

        # If it's a simple greeting, provide a friendly response
        if is_greeting:
            from datetime import datetime
            current_date = datetime.now().strftime("%A, %B %d, %Y")

            try:
                greeting_task = Task(
                    description=f"User greeted with: '{user_input}'. Respond with a friendly greeting. Today's date is {current_date}.",
                    agent=self.master_agent,
                    expected_output="A friendly greeting response",
                    context=[{
                        "description": f"You are helping users with OpenEngage. Today's date is {current_date}. The user has just greeted you. Respond with a friendly greeting without mentioning URLs unless specifically asked.",
                        "expected_output": "A friendly greeting response"
                    }]
                )

                self.crew.tasks = [greeting_task]
                response = self.crew.kickoff()
                return str(response)
            except Exception as e:
                raise

        # Check if the input looks like a URL
        url_pattern = r'https?://[^\s,]+'
        urls = re.findall(url_pattern, user_input)
        is_url_input = bool(urls)

        if not is_url_input:
            try:
                common_task = Task(
                        description=f"User message: '{user_input}'. Respond appropriately based on the context that we already have organization data.",
                        agent=self.master_agent,
                        expected_output="A helpful response based on the current context",
                        context=[{
                            "description": "You are helping users with OpenEngage. Organization data is already provided.",
                            "expected_output": "A helpful response based on the current context"
                        }]
                    )

                self.crew.tasks = [common_task]
                response = self.crew.kickoff()
                return str(response)
            except Exception as e:
                raise

        # First try to analyze as organization if not already done
        if not self.organization_data and hasattr(self, 'organization_url'):
            try:
                # Use the _run method instead of analyze_organization
                content = self.scraper._run(self.organization_url)
                org_analyzer_result = self.org_analyzer._run(content)
                self.organization_data = json.loads(org_analyzer_result)
            except Exception as e:
                return f"Failed to analyze organization: {str(e)}"
        # If organization is already analyzed, then analyze product(s)
        elif self.organization_data:
            # If the input contains URLs, process them as product URLs
            if is_url_input:
                # Initialize products list if not already done
                if not hasattr(self, 'products_list'):
                    self.products_list = []

                # Process each URL and add to products list
                analyzed_products = []
                for url in urls:
                    try:
                        # First scrape the content
                        content = self.scraper._run(url)
                        # Then analyze the product
                        product_data = json.loads(self.product_analyzer._run(content))
                        # Add URL to the product data
                        product_data["Product_URL"] = url
                        # Add Company_URL from organization data if available
                        if hasattr(self, 'organization_url'):
                            product_data["Company_URL"] = self.organization_url
                            product_data["organization_url"] = self.organization_url  # Add organization_url field
                        elif self.organization_data and "url" in self.organization_data:
                            product_data["Company_URL"] = self.organization_data["url"]
                            product_data["organization_url"] = self.organization_data["url"]  # Add organization_url field
                        # Add to products list
                        self.products_list.append(product_data)
                        analyzed_products.append(product_data)
                    except Exception as e:
                        return f"Failed to analyze product at {url}: {str(e)}"

                # Set the last analyzed product as the current product
                if analyzed_products:
                    self.product_data = analyzed_products[-1]

                # Save all analyzed products to product_details.json
                self.save_product_data()

                # Return success message with product count
                product_count = len(analyzed_products)
                response = f"I've analyzed {product_count} product(s):\n\n"
                for i, product in enumerate(analyzed_products, 1):
                    response += f"Product {i}: {product['Product_Name']} 📦 URL: {product['Product_URL']} 🏢 Company: {product['Company_Name']} 📝 Type: {product['Type_of_Product']}\n\n"
                response += "You can select and edit product details on the right panel."
                return response
            else:
                # For non-URL inputs, handle as a normal chat message
                if self.product_data:
                    # For non-URL inputs when we already have product data
                    task = Task(
                        description=f"User message: '{user_input}'. Respond appropriately based on the context that we already have organization and product data.",
                        agent=self.master_agent,
                        expected_output="A helpful response based on the current context",
                        context=[{
                            "description": "You are helping users with OpenEngage. Both organization and product data are already provided.",
                            "expected_output": "A helpful response based on the current context"
                        }]
                    )
                    self.crew.tasks = [task]
                    response = self.crew.kickoff()
                    return str(response)
                else:
                    # For non-URL inputs when we need product URL, respond as a normal chat
                    task = Task(
                        description=f"User message: '{user_input}'. Respond helpfully to the user's query. If appropriate, remind them that they can provide product URLs (can be comma-separated for multiple products) to analyze their products.",
                        agent=self.master_agent,
                        expected_output="A helpful response to the user's query",
                        context=[{
                            "description": "You are helping users with OpenEngage. The user is in the product setup phase and can provide product URLs to analyze, but you should also respond helpfully to any other questions they might have.",
                            "expected_output": "A helpful response to the user's query"
                        }]
                    )
                    self.crew.tasks = [task]
                    response = self.crew.kickoff()
                    return str(response)

        # For non-URL inputs, provide helpful guidance through the master agent
        else:
            if not self.organization_data:
                if is_url_input:
                    # If the input looks like a URL, treat it as an organization URL
                    try:
                        # First scrape the content
                        content = self.scraper._run(urls[0])
                        # Then analyze the organization
                        self.organization_data = json.loads(self.org_analyzer._run(content))
                        # Add URL to the organization data
                        self.organization_data["url"] = urls[0]
                        return f"I've analyzed your organization. Business Domain: {self.organization_data.get('Domain', 'Unknown')}. You can now provide product URLs to analyze."
                    except Exception as e:
                        return f"Failed to analyze organization at {urls[0]}: {str(e)}"
                else:
                    # If the input doesn't look like a URL, respond as a normal chat
                    from datetime import datetime
                    current_date = datetime.now().strftime("%A, %B %d, %Y")

                    task = Task(
                        description=f"User message: '{user_input}'. Respond helpfully to the user's query. Today's date is {current_date}. Only mention the organization URL if it's relevant to the user's question.",
                        agent=self.master_agent,
                        expected_output="A helpful response to the user's query",
                        context=[{
                            "description": f"You are helping users with OpenEngage. Today's date is {current_date}. Answer the user's question directly and accurately. Only mention providing organization URLs if the user specifically asks about setting up products or organization analysis.",
                            "expected_output": "A helpful and accurate response to the user's query"
                        }]
                    )
                    # Update crew tasks and get response
                    self.crew.tasks = [task]
                    response = self.crew.kickoff()
                    return str(response)
            elif not self.product_data:
                if is_url_input:
                    # If the input looks like a URL, treat it as a product URL
                    try:
                        # First scrape the content
                        content = self.scraper._run(urls[0])
                        # Then analyze the product
                        product_data = json.loads(self.product_analyzer._run(content))
                        # Add URL to the product data
                        product_data["Product_URL"] = urls[0]
                        # Add Company_URL from organization data if available
                        if hasattr(self, 'organization_url'):
                            product_data["Company_URL"] = self.organization_url
                            product_data["organization_url"] = self.organization_url
                        elif self.organization_data and "url" in self.organization_data:
                            product_data["Company_URL"] = self.organization_data["url"]
                            product_data["organization_url"] = self.organization_data["url"]

                        # Initialize products list if not already done
                        if not hasattr(self, 'products_list'):
                            self.products_list = []

                        # Add to products list
                        self.products_list.append(product_data)
                        self.product_data = product_data

                        # Save product data
                        self.save_product_data()

                        return f"I've analyzed your product: {product_data['Product_Name']}. You can now edit the product details on the right panel."
                    except Exception as e:
                        return f"Failed to analyze product at {urls[0]}: {str(e)}"
                else:
                    # If the input doesn't look like a URL, respond as a normal chat
                    from datetime import datetime
                    current_date = datetime.now().strftime("%A, %B %d, %Y")

                    task = Task(
                        description=f"User message: '{user_input}'. Respond helpfully to the user's query. Today's date is {current_date}. Only mention product URLs if it's relevant to the user's question.",
                        agent=self.master_agent,
                        expected_output="A helpful response to the user's query",
                        context=[{
                            "description": f"You are helping users with OpenEngage. Today's date is {current_date}. Answer the user's question directly and accurately. Only mention providing product URLs if the user specifically asks about setting up or analyzing products.",
                            "expected_output": "A helpful and accurate response to the user's query"
                        }]
                    )
                    # Update crew tasks and get response
                    self.crew.tasks = [task]
                    response = self.crew.kickoff()
                    return str(response)
            else:
                from datetime import datetime
                current_date = datetime.now().strftime("%A, %B %d, %Y")

                task = Task(
                    description=f"User message: '{user_input}'. Respond appropriately to the user's query. Today's date is {current_date}. Answer the question directly without mentioning organization or product URLs unless specifically asked.",
                    agent=self.master_agent,
                    expected_output="A helpful response based on the current context",
                    context=[{
                        "description": f"You are helping users with OpenEngage. Today's date is {current_date}. Both organization and product data are already provided. Answer the user's question directly and accurately without unnecessarily mentioning organization or product URLs.",
                        "expected_output": "A helpful and accurate response to the user's query"
                    }]
                )

            # Update crew tasks and get response
            if 'task' in locals():
                self.crew.tasks = [task]
                response = self.crew.kickoff()
                return str(response)
            else:
                # If no task was defined, create a default task
                from datetime import datetime
                current_date = datetime.now().strftime("%A, %B %d, %Y")

                default_task = Task(
                    description=f"User message: '{user_input}'. Respond helpfully to the user's query. Today's date is {current_date}. Answer the question directly without mentioning organization or product URLs unless specifically asked.",
                    agent=self.master_agent,
                    expected_output="A helpful response to the user's query",
                    context=[{
                        "description": f"You are helping users with OpenEngage. Today's date is {current_date}. Answer the user's question directly and accurately without mentioning organization or product URLs unless specifically asked about them.",
                        "expected_output": "A helpful and accurate response to the user's query"
                    }]
                )
                self.crew.tasks = [default_task]
                response = self.crew.kickoff()
                return str(response)

    def get_organization_data(self) -> dict:
        """Get the current organization data."""
        return self.organization_data

    def get_product_data(self) -> dict:
        """Get the current product data."""
        return self.product_data

    def get_all_products(self) -> list:
        """Get all analyzed products."""
        if hasattr(self, 'products_list') and self.products_list:
            return self.products_list
        elif self.product_data:
            return [self.product_data]
        else:
            return []

    def update_product_data(self, updated_data: dict):
        """
        Update the product data and save to file.

        Args:
        updated_data (dict): The updated product data.
        """
        # Import save_product_details function
        from utils.file_utils import save_product_details

        # Ensure organization_url is set
        if 'organization_url' not in updated_data and 'Company_URL' in updated_data:
            updated_data['organization_url'] = updated_data['Company_URL']

        # Update the current product data
        self.product_data = updated_data

        # If we have a products list, update the corresponding product in the list
        if hasattr(self, 'products_list') and self.products_list:
            product_url = updated_data.get('Product_URL')
            if product_url:
                # Find the product with the matching URL and update it
                for i, product in enumerate(self.products_list):
                    if product.get('Product_URL') == product_url:
                        self.products_list[i] = updated_data
                        break
                else:
                    # If no matching product found, add it to the list
                    self.products_list.append(updated_data)

        # Save the updated product directly to file
        # This will handle appending or updating as needed
        save_product_details(updated_data, replace_all=False)

    def analyze_collateral(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze a collateral file and return summary and key aspects.

        Args:
            file_path (str): Path to the collateral file to analyze

        Returns:
            Dict[str, Any]: Dictionary containing 'summary' and 'key_aspects'
        """
        # Determine file type and read content
        mime_type, _ = mimetypes.guess_type(file_path)
        content = ""

        if mime_type == 'application/pdf':
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for page in pdf_reader.pages:
                    content += page.extract_text() + "\n"
        else:
            # Try reading as text file with different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        break
                except UnicodeDecodeError:
                    continue

        if not content:
            return {
                "summary": "Error: Could not read file content",
                "key_aspects": []
            }
        # Create analysis task
        task_description = f"""
        Analyze the provided marketing collateral and extract the following information:
        1. Summary: A overview of what the collateral is about in 200 words
        2. Features: List of 5 features, capabilities, or offerings mentioned in the collateral
        3. Marketing Points: Key aspects that would be compelling for marketing, such as unique selling points,
        benefits, competitive advantages, or customer value propositions

        Marketing Collateral:
        {content[:1000] + ("..." if len(content) > 1000 else "")}

        Response must be in this exact JSON format with the following keys:
            "summary": "Your concise summary here",
            "features": [
                "Feature 1",
                "Feature 2",
                ...,
                "Feature 5"
            ],
            "marketing_points": [
                "Marketing point 1",
                "Marketing point 2",
                ...,
                "Marketing point 5"
            ]
        """

        task = Task(
            description=task_description,
            agent=self.analyst_agent,
            context=[{
                "content": content[:2000] + ("..." if len(content) > 2000 else ""),
                "description": task_description,
                "expected_output": "JSON with summary and features and marketing points"
            }],
            expected_output="JSON with summary and features and marketing points",
            async_execution=False
        )

        self.crew.tasks = [task]
        result = self.crew.kickoff()
        if hasattr(result, '__dict__'):
            result = str(result)
        else:
            result = result

        result = str(result)
        
        return eval(result.replace("```", '').replace("json", ''))

    def generate_fully_personalized_email(self, user_data, product_data, communication_settings):
        """Generate a fully personalized email without using templates.

        Args:
            user_data (dict): User data including first_name, user_behaviour, and user_stage
            product_data (dict): Product data including name, description, features, and URL
            communication_settings (dict): Communication settings including tone, style, etc.

        Returns:
            dict: Dictionary with subject, content, and preheader fields
        """
        # Extract user information
        first_name = user_data.get('first_name', '')
        user_behavior = user_data.get('user_behaviour', '')
        user_stage = user_data.get('user_stage', '')

        # Extract product information
        product_name = product_data.get('Product_Name', '')
        product_description = product_data.get('Product_Description', '')
        product_features = product_data.get('Product_Features', [])
        product_url = product_data.get('Product_URL', '')

        # Extract communication settings
        tone = communication_settings.get('style', 'friendly')
        brand_personality = communication_settings.get('brand_personality', 'Professional, Helpful, Trustworthy')
        tone_of_voice = communication_settings.get('tone_of_voice', 'Professional, Informative')
        sender_name = communication_settings.get('sender_name', 'OpenEngage Team')

        # Create task description for personalized email
        task_description = f"""
        Generate a fully personalized email for {first_name} based on their behavior and stage in the customer journey.

        User Information:
        - Name: {first_name}
        - Behavior: {user_behavior}
        - Stage: {user_stage}

        Product Information:
        - Name: {product_name}
        - Description: {product_description}
        - Features: {', '.join(product_features[:5] if product_features else [])}
        - URL: {product_url}

        Communication Guidelines:
        - Tone: {tone}
        - Brand Personality: {brand_personality}
        - Tone of Voice: {tone_of_voice}
        - Sender Name: {sender_name}

        Create a completely personalized email that addresses the user's specific behavior and needs.
        Do not use generic templates. Make it feel like it was written specifically for this user.
        Include specific references to their behavior and how the product can help them.

        The email should include:
        1. A personalized subject line
        2. A preheader text
        3. A personalized greeting
        4. Body content that directly addresses their behavior
        5. A clear call to action
        6. A professional sign-off

        Format your response as follows:

        Subject: [Your compelling subject line here]
        Pre-header: [Your brief pre-header text that complements the subject line]

        [Your email body content here]
        """

        # Create and execute task
        task = Task(
            description=task_description,
            agent=self.campaign_agent,
            context=[{
                "description": task_description,
                "expected_output": "Personalized email content with subject line, preheader, and body"
            }],
            expected_output="Personalized email content",
            async_execution=False
        )

        # Update crew tasks and execute
        self.crew.tasks = [task]
        response = self.crew.kickoff()

        # Convert response to string if it's not already
        if hasattr(response, '__dict__'):
            response = str(response)
        else:
            response = str(response)

        return response

    def generate_popup_content(self, popup_prompt):
        """Generate personalized popup content based on user data.

        Args:
            popup_prompt (str): Formatted prompt with user details for popup generation

        Returns:
            dict: Dictionary with popup_title, popup_text, and button_text fields
        """
        try:
            # Use the campaign agent to generate the popup content
            task = Task(
                description="Generate personalized popup content for a website visitor",
                agent=self.campaign_agent,
                context=popup_prompt,
                async_execution=False
            )

            result = self.crew.execute_task(task)

            # Process the result - should be a valid JSON string
            try:
                # Try to parse the JSON response
                if '{' in result and '}' in result:
                    # Extract JSON from possible text wrapping
                    json_str = result[result.find('{'):result.rfind('}')+1]
                    popup_data = json.loads(json_str)
                else:
                    # Fall back to default content if JSON parsing fails
                    popup_data = {
                        "popup_title": "Limited Time Offer",
                        "popup_text": "Enhance your AI skills today!",
                        "button_text": "Learn More"
                    }

                # Ensure we have all required fields
                if 'popup_title' not in popup_data:
                    popup_data['popup_title'] = "Limited Time Offer"
                if 'popup_text' not in popup_data:
                    popup_data['popup_text'] = "Enhance your AI skills today!"
                if 'button_text' not in popup_data:
                    popup_data['button_text'] = "Learn More"

                return popup_data
            except Exception as e:
                print(f"Error parsing popup content: {str(e)}")
                # Return default popup content if parsing fails
                return {
                    "popup_title": "Limited Time Offer",
                    "popup_text": "Enhance your AI skills today!",
                    "button_text": "Learn More"
                }
        except Exception as e:
            print(f"Error generating popup content: {str(e)}")
            # Return default popup content if generation fails
            return {
                "popup_title": "Limited Time Offer",
                "popup_text": "Enhance your AI skills today!",
                "button_text": "Learn More"
            }

    def delegate_work(self, task: str, context: str, coworker: str) -> str:
        """Delegate work to a specific agent"""
        # Map coworker name to agent
        agent_map = {
            "Campaign Generation Agent": self.campaign_agent,
            "Analyst Agent": self.analyst_agent
        }

        if coworker not in agent_map:
            return f"Unknown coworker: {coworker}"

        # Create and execute task - ensure context is a list of dictionaries
        task_obj = Task(
            description=task,
            agent=agent_map[coworker],
            context=[{
                "description": context,
                "expected_output": "Generated content"  # Required field in each context item
            }],
            expected_output="Generated content",  # Required field for the task
            async_execution=False
        )

        # Update crew tasks and kickoff
        self.crew.tasks = [task_obj]
        return self.crew.kickoff()


    def get_default_journey(self):
        """Return the default user journey stages"""
        return [
            {
                "s_no": 1,
                "current_stage": "New Visitor",
            "description": "User is not aware of the product",
            "goal_stage": "Product Page Viewed"
        },
        {
            "s_no": 2,
            "current_stage": "Product Page Viewed",
            "description": "User has viewed product details",
            "goal_stage": "Product Lead Generated"
        },
        {
            "s_no": 3,
            "current_stage": "Product Lead Generated",
            "description": "User has shown interest in the product",
            "goal_stage": "Product Purchased"
        },
        {
            "s_no": 4,
            "current_stage": "Product Purchased",
            "description": "User has completed the purchase",
            "goal_stage": "Thank You"
        }
    ]



    def load_user_journey(self,product_name=None):
        """Load user journey for a specific product or return default journey"""
        try:
            with open('data/user_journey.json', 'r') as f:
                journeys = json.load(f)
                if not isinstance(journeys, dict):
                    # Convert old format to dict with default journey
                    journeys = {"default": journeys if isinstance(journeys, list) else []}

                if product_name:
                    # Return journey for specific product or default
                    return journeys.get(product_name, journeys.get("default", self.get_default_journey()))

                # Return default journey
                return journeys.get("default", self.get_default_journey())
        except (FileNotFoundError, json.JSONDecodeError):
            return self.get_default_journey()

    def generate_campaign(self, product_data, channels, settings, stage, instructions=""):
        """Generate a marketing campaign for the given product and stage."""

        # Format task description based on template context if available
        if settings and "template_context" in settings:
            template_context = settings["template_context"]
            base_template = template_context["base_template"]
            user_behavior = template_context["user_behavior"]
            first_name = template_context.get("first_name", "")  # Get first name from context

            # Use product data from the template instead of current product data
            template_product_data = base_template["product_data"]
            template_settings = base_template["communication_settings"]

            task_description = self.prompts['campaign_generation']['template'].format(
                stage=stage,
                product_details=json.dumps(template_product_data, indent=2),
                subject=base_template['template']['subject'],
                body=base_template['template']['body'],
                tone=template_settings.get('tone', 'professional'),
                style=template_settings.get('style', 'friendly'),
                length=template_settings.get('length', '100-150 words'),
                sender_name=template_settings.get('sender_name', ''),
                brand_personality=template_settings.get('brand_personality', 'Sage'),
                brand_tone_of_voice=template_settings.get('tone_of_voice', 'Professional, Informative'),
                first_name=first_name,
                user_behavior=user_behavior,
                instructions=instructions
            )
        else:
            # Get the user journey stage details
            journey_data = self.load_user_journey(product_data.get('Product_Name', ''))
            current_stage = next((s for s in journey_data if s['current_stage'] == stage), None)

            if not current_stage:
                return f"Error: Stage {stage} not found in user journey"

            task_description = f"""Generate an email campaign for the {stage} stage using these details:

                                    Product Details:
                                    Target Product: {product_data.get('Product_Name', '')}
                                    Target Product URL: {product_data.get('Product_URL', '')}
                                    Target Product Summary: {product_data.get('Product_Summary', '')}
                                    Target Product Features:
                                    {chr(10).join('- ' + feature for feature in product_data.get('Product_Features', []))}
                                    Target Product Collaterals: {str(product_data.get('Collaterals', []))}


                                    Stage Details:
                                    Current Stage: {current_stage['current_stage']}
                                    Goal Stage: {current_stage['goal_stage']}
                                    Stage Description: {current_stage['description']}

                                    Communication Settings:
                                    Tone: {settings.get('tone', 'professional')}
                                    Style: {settings.get('style', 'friendly')}
                                    Length: {settings.get('length', '100-150 words')}
                                    Sender Name: {settings.get('sender_name', '')}
                                    Brand Personality: {settings.get('brand_personality', 'Sage')}
                                    Brand Tone of Voice: {settings.get('tone_of_voice', 'Professional, Informative')}

                                    Instructions to make this email effective:
                                    {instructions}

                                    Instructions:
                                    1. Write an engaging email that moves the user from {current_stage['current_stage']} to {current_stage['goal_stage']}
                                    2. Use the specified tone and style
                                    3. Incorporate the brand personality traits into the messaging
                                    4. Use the brand tone of voice to guide the writing style
                                    5. Include relevant product features and benefits
                                    6. Add UTM-tagged links where appropriate
                                    7. You can pitch a collateral if it is relevant to the email
                                    
                                    Striclty follow the below format for Output:

                                    Email Content:
                                    Subject: [Write your subject line here]\n
                                    Pre-header: [Write a brief pre-header text that complements the subject line]\n
                                    [Write your email body here where paragraphs are separated by "\n\n"]
                                    """

        task = Task(
            description=task_description,
            agent=self.campaign_agent,
            context=[{
                "description": task_description,
                "expected_output": "Email content with subject line and body"
            }],
            expected_output="Email content with subject line and body",
            async_execution=False
        )

        # Update crew tasks and execute
        self.crew.tasks = [task]
        raw_response = self.crew.kickoff()

        # Convert raw response to a structured object using Pydantic
        try:
            # First, try to parse as Pydantic model if it's already formatted correctly
            if isinstance(raw_response, dict) and 'subject' in raw_response and ('pre_header' in raw_response or 'preheader' in raw_response) and ('body' in raw_response or 'content' in raw_response):
                # If we have a dictionary with expected fields, convert to our model
                preheader = raw_response.get('pre_header', raw_response.get('preheader', ''))
                body_content = raw_response.get('body', raw_response.get('content', ''))
                email_output = EmailCampaignOutput(
                    subject=raw_response.get('subject', ''),
                    pre_header=preheader,
                    body=body_content
                )
            else:
                # Otherwise, parse the raw response string
                raw_str = str(raw_response)
                email_output = EmailCampaignOutput.parse_raw_response(raw_str)
            
            # Convert to the expected string format
            response = str(email_output)
            
        except Exception as e:
            # Fallback to the original parsing logic if anything goes wrong
            if hasattr(raw_response, '__dict__'):
                response = str(raw_response)
            else:
                response = raw_response

            # Ensure response has the correct format
            if "Email Content:" not in response:
                # Check if we got a dictionary response
                if isinstance(response, dict):
                    response = f"Email Content:\nSubject: {response.get('subject', '')}\n\n{response.get('content', '')}"
                else:
                    # Try to format the raw response
                    response = f"Email Content:\n{response}"

            if "Subject:" not in response:
                # Try to extract a subject from the first line
                lines = response.split('\n')
                first_line = next((line for line in lines if line.strip()), "")
                response = f"Email Content:\nSubject: {first_line}\n\n{response}"
        validation_result = validate_email(
            email_body=response,
            product_name=product_data.get('Product_Name', ''),
            run_safety_check=True,
            run_template_check=False,
            run_feature_check=False
        )
        valid = validation_result['overall_valid']

        if valid==False:
            instruction=""
            for i,j in validation_result['validation_results'].items():
                if "is_valid" in j:
                    if j['is_valid'] == False:
                        instruction += f"{j['message']}\n"
                if "is_safe" in j:
                    if j["is_safe"]==False:
                        instruction += f"Not safe NSFW content: {j['message']}\n"
            self.generate_campaign(product_data, channels, settings, stage, instructions=instruction)
        return response

    def generate_personalized_email_for_journey(self, user_data, product_data, communication_settings, stage):
        """Generate a fully personalized email for journey builder using the specialized agent."""

        # Extract user information
        first_name = user_data.get('first_name', '')
        user_behavior = user_data.get('user_behaviour', '')
        user_stage = user_data.get('user_stage', '')

        # Extract product information
        product_name = product_data.get('Product_Name', '')
        product_summary = product_data.get('Product_Summary', '')
        product_description = product_data.get('Product_Description', '')
        product_features = product_data.get('Product_Features', [])
        product_url = product_data.get('Product_URL', '')

        # Load organization information from organization_data.json
        company_name = product_data.get('Company_Name', '')
        company_url = product_data.get('Company_URL', product_data.get('organization_url', ''))

        # Load organization data from file
        organization_info = {}
        try:
            with open('data/organization_data.json', 'r') as f:
                org_data = json.load(f)
                if company_url in org_data:
                    organization_info = org_data[company_url]
        except Exception as e:
            print(f"Error loading organization data: {e}")

        # Extract organization details
        organization_domain = organization_info.get('Domain', '')
        organization_what_we_do = organization_info.get('WhatWeDo', '')
        organization_about_us = organization_info.get('AboutUs', '')
        organization_class = organization_info.get('Class', '')

        # Extract communication settings
        tone = communication_settings.get('style', 'friendly')
        brand_personality = communication_settings.get('brand_personality', 'Professional, Helpful, Trustworthy')
        tone_of_voice = communication_settings.get('tone_of_voice', 'Professional, Informative')
        sender_name = communication_settings.get('sender_name', 'OpenEngage Team')
        length = communication_settings.get('length', '100-150 words')

        # Load user journey data from user_journey.json
        import streamlit as st
        journey_data = {}
        try:
            with open('data/user_journey.json', 'r') as f:
                journey_data = json.load(f)
        except Exception as e:
            print(f"Error loading user journey data: {e}")

        # Get journey stages for the specific product or use default
        product_journey_key = product_name if product_name in journey_data else "default"
        journey_stages = journey_data.get(product_journey_key, [])

        # Find current stage and goal stage information
        current_stage_desc = ""
        goal_stage_desc = ""
        goal_stage_name = ""

        for stage_info in journey_stages:
            if stage_info.get('current_stage') == user_stage:
                current_stage_desc = stage_info.get('description', '')
                goal_stage_name = stage_info.get('goal_stage', '')

                # Find goal stage description
                for goal_stage_info in journey_stages:
                    if goal_stage_info.get('current_stage') == goal_stage_name:
                        goal_stage_desc = goal_stage_info.get('description', '')
                        break
                break

        # Format stage information
        if goal_stage_name:
            stage_info = f"Current Stage: {user_stage}({current_stage_desc}) & Goal Stage: {goal_stage_name}({goal_stage_desc})"
        else:
            stage_info = f"Current Stage: {user_stage}({current_stage_desc})"

        # Create the enhanced prompt with stage descriptions and organization info
        prompt = f"""
        Generate a fully personalized email for {first_name} based on their behavior and stage in the customer journey.

        User Information:
        - Name: {first_name}
        - Behavior: {user_behavior}
        - {stage_info}

        Product Information:
        - Name: {product_name}
        - Summary: {product_summary if product_summary else product_description}
        - Features: {', '.join(product_features[:5] if product_features else [])}
        - URL: {product_url}

        Organization Information:
        - Company Name: {company_name}
        - Company URL: {company_url}
        - Domain: {organization_domain}
        - What We Do: {organization_what_we_do}
        - About Us: {organization_about_us}
        - Class: {organization_class}

        Communication Guidelines:
        - Tone: {tone}
        - Brand Personality: {brand_personality}
        - Tone of Voice: {tone_of_voice}
        - Sender Name: {sender_name}
        - Length of the Mail: {length}

        Create a completely personalized email that addresses the user's specific behavior and needs.
        Do not use generic templates. Make it feel like it was written specifically for this user.
        Include specific references to their behavior and how the product can help them. Don't pitch the product if the user has already purchased or opted for the product, we should Welcome him or Write a Thank You mail instead and You can know this from User's Current Stage.

        Structure of the Mail should be like below if the user's has not Purchased or opted for the Product:-
        1. It should start with HOOK. The HOOK should be as small as it can be. It should grab the attention of user. You can get an idea of what to write in the hook from the user behavior.
        2. The second part of mail should be TRUST. By reading this part, user should feel a deep urge to listen to you.
        3. The third part should be PITCH . Here, you will tell about the product and pitch it. In the pitch the user should feel that you are not only selling something. But you are his well wisher.
        4. The fourth part should be MOTIVATION. In this you will motivate the user based on his behavior and what the program contains and the benefits of the program.

        This doesn't mean that the Email Content will contain four paragraphs only.

        The email should include:
        1. A personalized subject line
        2. A preheader text
        3. A personalized greeting
        4. Body content based on structure of the mail.
        5. A clear call to action
        6. A professional sign-off

        Don't add anything irrelevant from your side. Only use the information given to you.

        Format your response as follows:

        Subject: [Your compelling subject line here]
        Pre-header: [Your brief pre-header text that complements the subject line]

        [Your email body content here]
        """

        # Create task for the personalized email agent
        task = Task(
            description=prompt,
            agent=self.personalized_agent,
            context=[{
                "description": prompt,
                "expected_output": "Personalized email content with subject line, preheader, and body"
            }],
            expected_output="Personalized email content with subject line, preheader, and body",
            async_execution=False
        )

        # Update crew tasks and execute
        self.crew.tasks = [task]
        response = self.crew.kickoff()

        # Handle different response types
        if hasattr(response, '__dict__'):
            # If response is a CrewOutput object, get the raw output
            if hasattr(response, 'raw'):
                response_text = str(response.raw)
            else:
                response_text = str(response)
        else:
            response_text = str(response)

        # Try to parse the response and return a structured format
        try:
            # Check if response is already in the expected format
            if "Subject:" in response_text and "Pre-header:" in response_text:
                # Parse the structured response
                lines = response_text.strip().split('\n')
                subject = ""
                pre_header = ""
                content_lines = []

                current_section = None
                for line in lines:
                    line = line.strip()
                    if line.startswith("Subject:"):
                        subject = line.replace("Subject:", "").strip()
                        current_section = "subject"
                    elif line.startswith("Pre-header:"):
                        pre_header = line.replace("Pre-header:", "").strip()
                        current_section = "pre_header"
                    elif line and current_section in ["pre_header", "subject"]:
                        # This is the email body content
                        content_lines.append(line)
                        current_section = "content"
                    elif line and current_section == "content":
                        content_lines.append(line)

                # Return structured response
                return {
                    "subject": subject,
                    "preheader": pre_header,
                    "content": '\n'.join(content_lines)
                }
            else:
                # Return the raw response as content
                return {
                    "subject": f"Personalized Email for {user_stage}",
                    "preheader": "A personalized message for you",
                    "content": response_text
                }
        except Exception as e:
            print(f"Error parsing personalized email response: {e}")
            # Return fallback structure
            return {
                "subject": f"Personalized Email for {user_stage}",
                "preheader": "A personalized message for you",
                "content": response_text
            }

    def generate_followup_email(self, product_name: str, original_email: dict, interaction_type: str, settings: dict, instructions="") -> dict:
        """Generate a follow-up email based on user's interaction with the original email."""

        task_description = f"""
        Original Email: {original_email.get('content', '')}
        User Interaction: User has {interaction_type} the email

        Task: Create a follow-up email and strictly follow the below conditions:
        1. Acknowledges the user's interaction
        2. Builds on the original message
        3. Provides additional value or information
        4. Maintains the same tone ({settings.get('tone', 'professional')}) and style ({settings.get('style', 'formal')})
        5. Encourages further engagement
        6. Follows the instructions: {instructions}
        7. Don't change the name of the user in Greetings. And, always start the Email with a greeting such as Hi, Hello, etc.
        8. Don't add any Offer/Discount in the Email, if there is no Offer/Discount in the Original Email.

        Strictly follow the below format for Output:

        Subject: [Write your subject line here]\n
        Pre-header: [Write a brief pre-header text that complements the subject line]\n

        [Write your email body here where paragraphs are separated by \n\n]
        """

        task = Task(
            description=task_description,
            agent=self.campaign_agent,  # Using campaign agent instead of master agent
            context=[{
                "description": task_description,
                "expected_output": "Email content with subject line and body",
                "settings": settings
            }],
            expected_output="Email content with subject line and body",
            async_execution=False
        )

        # Update crew tasks and execute
        self.crew.tasks = [task]
        response = self.crew.kickoff()

        # Convert response to string if it's not already
        if hasattr(response, '__dict__'):
            response = str(response)

        try:
            # Parse the response format
            if "Subject:" in response:
                # Parse the structured response properly
                lines = response.strip().split('\n')
                subject = ""
                pre_header = ""
                content_lines = []

                current_section = None
                for line in lines:
                    line = line.strip()
                    if line.startswith("Subject:"):
                        subject = line.replace("Subject:", "").strip()
                        current_section = "subject"
                    elif line.startswith("Pre-header:"):
                        pre_header = line.replace("Pre-header:", "").strip()
                        current_section = "pre_header"
                    elif line and current_section in ["pre_header", "subject"]:
                        # This is the email body content
                        content_lines.append(line)
                        current_section = "content"
                    elif line and current_section == "content":
                        content_lines.append(line)

                # Join content lines and clean up
                body = '\n'.join(content_lines).strip()

                follow_up = {
                    "subject": subject,
                    "pre_header": pre_header,
                    "content": body,
                    "cta": "Learn More",
                    "cta_link": "#",
                    "original_email_id": original_email.get('id'),
                    "interaction_type": interaction_type,
                    "generated_at": datetime.now().isoformat(),
                    "settings": settings  # Include settings in the response
                }
                validation_result = validate_email(
                    email_body=str(follow_up['content']),
                    product_name=product_name,
                    run_safety_check=True,
            run_template_check=False,
            run_feature_check=False
                    )
                valid = validation_result['overall_valid']
                print(valid)
                if valid==False:
                    instruction=""
                    for i,j in validation_result['validation_results'].items():
                        if "is_valid" in j:
                            if j['is_valid'] == False:
                                instruction += f"{j['message']}\n"
                        if "is_safe" in j:
                            if j["is_safe"]==False:
                                instruction += f"Not safe NSFW content: {j['message']}\n"
                    self.generate_followup_email(product_name, original_email, interaction_type, settings, instructions=instruction)
                return follow_up
            else:
                return {
                    'error': 'Invalid response format',
                    'original_email_id': original_email.get('id'),
                    'settings': settings
                }
        except Exception as e:
            return {
                'error': f'Failed to generate follow-up email: {str(e)}',
                'original_email_id': original_email.get('id'),
                'settings': settings
            }

    def save_product_data(self):
        """Save the product data to a JSON file using file_utils.save_product_details"""
        from utils.file_utils import save_product_details

        # If we have a products list, save all products at once
        if hasattr(self, 'products_list') and self.products_list:
            # Ensure organization_url is set for all products
            for product in self.products_list:
                if 'organization_url' not in product and 'Company_URL' in product:
                    product['organization_url'] = product['Company_URL']

            # Pass the entire list to save_product_details
            save_product_details(self.products_list, replace_all=False)
        # Otherwise, save just the current product data
        elif self.product_data:
            # Ensure organization_url is set
            if 'organization_url' not in self.product_data and 'Company_URL' in self.product_data:
                self.product_data['organization_url'] = self.product_data['Company_URL']

            save_product_details(self.product_data, replace_all=False)
