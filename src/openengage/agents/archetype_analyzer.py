"""
Archetype Analyzer Agent for analyzing brand archetypes.

This agent analyzes brand content and determines the alignment with
the 12 brand archetypes, providing percentage-based scores and
detailed reasoning for each archetype.
"""

from typing import Dict, List, Any, Optional, Tuple
from openai import OpenAI
import os
import json
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ArchetypeScore(BaseModel):
    """Model for archetype score and reasoning."""
    score: float = Field(description="Percentage score (0-100) for this archetype")
    reasoning: str = Field(description="Detailed reasoning for this score")

class ArchetypeAnalysis(BaseModel):
    """Model for complete archetype analysis results."""
    archetype_scores: Dict[str, ArchetypeScore] = Field(description="Scores and reasoning for each archetype")
    primary_archetype: str = Field(description="The primary archetype with the highest score")
    primary_reasoning: str = Field(description="Detailed reasoning for the primary archetype")

class ArchetypeAnalyzer:
    """Analyzer for brand archetypes."""
    
    def __init__(self):
        """Initialize the archetype analyzer."""
        self.archetypes = [
            "Creator", "<PERSON>", "Caregiver", "<PERSON>", "Jester", "Magician", 
            "Ruler", "Hero", "Everyman", "Rebel", "Explorer", "Lover"
        ]
        self.archetype_descriptions = {
            "Creator": "Innovates to build original, lasting products or experiences that express their vision.",
            "Sage": "Seeks truth and wisdom to enlighten others through knowledge and insight.",
            "Caregiver": "Protects and nurtures others with compassion and selflessness.",
            "Innocent": "Spreads joy and optimism by living simply and doing what's right.",
            "Jester": "Brings happiness through humor, fun, and lightheartedness.",
            "Magician": "Transforms reality to create awe-inspiring, dream-like experiences.",
            "Ruler": "Leads with authority and order to achieve control, success, and stability.",
            "Hero": "Strives to overcome challenges and inspire through courage and determination.",
            "Everyman": "Relatable and grounded, values connection and belonging for all.",
            "Rebel": "Challenges norms to spark change and revolution with bold independence.",
            "Explorer": "Embarks on adventures to discover new experiences and personal freedom.",
            "Lover": "Pursues deep emotional and physical connections through passion and desire."
        }
        self.archetype_traits = {
            "Creator": ["innovative", "creative", "artistic", "inventive", "expressive", "original", "visionary"],
            "Sage": ["wise", "knowledgeable", "analytical", "intelligent", "thoughtful", "informative", "expert"],
            "Caregiver": ["nurturing", "supportive", "compassionate", "helpful", "generous", "protective", "empathetic"],
            "Innocent": ["optimistic", "pure", "honest", "simple", "moral", "ethical", "trustworthy"],
            "Jester": ["playful", "humorous", "fun", "entertaining", "light-hearted", "spontaneous", "joyful"],
            "Magician": ["transformative", "mystical", "inspiring", "visionary", "charismatic", "powerful", "imaginative"],
            "Ruler": ["authoritative", "powerful", "controlling", "structured", "organized", "prestigious", "leading"],
            "Hero": ["brave", "determined", "skilled", "inspiring", "strong", "courageous", "triumphant"],
            "Everyman": ["relatable", "authentic", "grounded", "inclusive", "approachable", "friendly", "down-to-earth"],
            "Rebel": ["disruptive", "revolutionary", "unconventional", "bold", "provocative", "independent", "challenging"],
            "Explorer": ["adventurous", "free", "independent", "curious", "pioneering", "daring", "discovering"],
            "Lover": ["passionate", "sensual", "intimate", "romantic", "attractive", "beautiful", "emotional"]
        }
    
    def analyze_brand_content(self, brand_data: Dict[str, Any]) -> ArchetypeAnalysis:
        """
        Analyze brand content to determine archetype alignment.
        
        Args:
            brand_data: Dictionary containing brand information
                (mission, vision, personality, tone, etc.)
                
        Returns:
            ArchetypeAnalysis object with scores and reasoning
        """
        # Extract relevant brand information
        mission = brand_data.get("mission", "")
        vision = brand_data.get("vision", "")
        brand_personality = brand_data.get("brand_personality", "")
        tone_of_voice = brand_data.get("tone_of_voice", "")
        
        # Combine all text for analysis
        combined_text = f"{mission} {vision} {brand_personality} {tone_of_voice}".lower()
        
        # If we have enough content, use AI for analysis
        if len(combined_text) > 50:
            return self._analyze_with_ai(brand_data)
        else:
            # Fallback to trait-based analysis if not enough content
            return self._analyze_with_traits(combined_text)
    
    def _analyze_with_traits(self, text: str) -> ArchetypeAnalysis:
        """
        Analyze brand content using trait matching.
        
        Args:
            text: Combined brand text for analysis
            
        Returns:
            ArchetypeAnalysis object with scores and reasoning
        """
        scores = {}
        
        # Calculate scores based on trait matching
        for archetype, traits in self.archetype_traits.items():
            trait_matches = 0
            matching_traits = []
            
            for trait in traits:
                if trait.lower() in text.lower():
                    trait_matches += 1
                    matching_traits.append(trait)
            
            # Calculate percentage score (0-100)
            if traits:
                percentage = min(100, (trait_matches / len(traits)) * 100)
            else:
                percentage = 0
                
            # Generate reasoning based on matching traits
            if matching_traits:
                reasoning = f"The brand exhibits {len(matching_traits)} traits associated with the {archetype} archetype: {', '.join(matching_traits)}."
            else:
                reasoning = f"The brand shows limited alignment with the {archetype} archetype traits."
            
            scores[archetype] = ArchetypeScore(score=percentage, reasoning=reasoning)
        
        # Find primary archetype (highest score)
        primary_archetype = max(scores.items(), key=lambda x: x[1].score)
        
        return ArchetypeAnalysis(
            archetype_scores=scores,
            primary_archetype=primary_archetype[0],
            primary_reasoning=primary_archetype[1].reasoning
        )
    
    def _analyze_with_ai(self, brand_data: Dict[str, Any]) -> ArchetypeAnalysis:
        """
        Analyze brand content using AI.
        
        Args:
            brand_data: Dictionary containing brand information
            
        Returns:
            ArchetypeAnalysis object with scores and reasoning
        """
        try:
            # Prepare the prompt with brand information
            prompt = self._prepare_analysis_prompt(brand_data)
            
            # Call OpenAI API
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a brand archetype expert who analyzes brand content to determine alignment with the 12 brand archetypes.
                        For each archetype, provide:
                        1. A percentage score (0-100%) indicating how well the brand aligns with that archetype
                        2. Detailed reasoning explaining why the brand does or doesn't align with that archetype
                        
                        Base your analysis on the brand's mission, vision, personality, tone of voice, and other provided information.
                        Be specific and reference actual elements from the brand in your reasoning.
                        """
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1500,
                response_format={"type": "json_object"}
            )
            
            # Parse the response
            result = json.loads(response.choices[0].message.content)
            
            # Convert to our format
            scores = {}
            for archetype in self.archetypes:
                if archetype in result:
                    arch_data = result[archetype]
                    # Extract score as a percentage (0-100)
                    if isinstance(arch_data, dict) and "score" in arch_data and "reasoning" in arch_data:
                        # Handle different score formats
                        score_str = str(arch_data["score"]).strip().rstrip("%")
                        try:
                            score = float(score_str)
                        except ValueError:
                            # Default to 50 if we can't parse the score
                            score = 50
                        
                        scores[archetype] = ArchetypeScore(
                            score=score,
                            reasoning=arch_data["reasoning"]
                        )
            
            # If no scores were parsed, use trait-based analysis as fallback
            if not scores:
                return self._analyze_with_traits(
                    f"{brand_data.get('mission', '')} {brand_data.get('vision', '')} {brand_data.get('brand_personality', '')} {brand_data.get('tone_of_voice', '')}"
                )
            
            # Find primary archetype (highest score)
            primary_archetype = max(scores.items(), key=lambda x: x[1].score)
            
            return ArchetypeAnalysis(
                archetype_scores=scores,
                primary_archetype=primary_archetype[0],
                primary_reasoning=primary_archetype[1].reasoning
            )
            
        except Exception as e:
            # Fallback to trait-based analysis on error
            print(f"Error in AI analysis: {str(e)}")
            return self._analyze_with_traits(
                f"{brand_data.get('mission', '')} {brand_data.get('vision', '')} {brand_data.get('brand_personality', '')} {brand_data.get('tone_of_voice', '')}"
            )
    
    def _prepare_analysis_prompt(self, brand_data: Dict[str, Any]) -> str:
        """
        Prepare the prompt for AI analysis.
        
        Args:
            brand_data: Dictionary containing brand information
            
        Returns:
            Formatted prompt string
        """
        # Extract brand information
        organization_url = brand_data.get("organization_url", "")
        mission = brand_data.get("mission", "")
        vision = brand_data.get("vision", "")
        brand_personality = brand_data.get("brand_personality", "")
        tone_of_voice = brand_data.get("tone_of_voice", "")
        
        # Build the prompt
        prompt = f"""Analyze the following brand information and determine the alignment with each of the 12 brand archetypes.
        
        Brand URL: {organization_url}
        Mission: {mission}
        Vision: {vision}
        Brand Personality: {brand_personality}
        Tone of Voice: {tone_of_voice}
        
        For each of the following archetypes, provide:
        1. A percentage score (0-100%) indicating how well the brand aligns with that archetype
        2. Detailed reasoning explaining why the brand does or doesn't align with that archetype
        
        The 12 archetypes are:
        """
        
        # Add archetype descriptions
        for archetype, description in self.archetype_descriptions.items():
            prompt += f"\n- {archetype}: {description}"
        
        prompt += """
        
        Return your analysis as a JSON object with this structure:
        {
            "Creator": {"score": 75, "reasoning": "Detailed reasoning..."},
            "Sage": {"score": 45, "reasoning": "Detailed reasoning..."},
            ...
        }
        
        Be specific in your reasoning and reference actual elements from the brand information provided.
        """
        
        return prompt

def analyze_brand_archetypes(brand_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze brand archetypes for the given brand data.
    
    Args:
        brand_data: Dictionary containing brand information
        
    Returns:
        Dictionary with archetype scores and primary archetype
    """
    analyzer = ArchetypeAnalyzer()
    analysis = analyzer.analyze_brand_content(brand_data)
    
    # Convert to dictionary format
    result = {
        "archetype_scores": {},
        "primary_archetype": analysis.primary_archetype,
        "primary_reasoning": analysis.primary_reasoning
    }
    
    # Format scores as dictionaries
    for archetype, score_obj in analysis.archetype_scores.items():
        result["archetype_scores"][archetype] = {
            "score": score_obj.score,
            "reasoning": score_obj.reasoning
        }
    
    return result
