"""
Agents package for OpenEngage.
Contains AI agent tools for various analysis tasks.
"""

# Import from the brand_analyzer module in this package
from .brand_analyzer import WebsiteBrandAnalyzerTool

# Import from the agents.py module in the parent directory
import os
import sys
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

# Import required classes - Need to use import machinery to avoid circular imports
import importlib.util
spec = importlib.util.spec_from_file_location("agents_module", os.path.join(parent_dir, "agents.py"))
agents_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(agents_module)

# Get the classes from the agents module
AgentFactory = agents_module.AgentFactory
ProductAnalyzerTool = agents_module.ProductAnalyzerTool
WebScrapingTool = agents_module.WebScrapingTool
OrganizationAnalyzerTool = agents_module.OrganizationAnalyzerTool

# Export everything
__all__ = [
    'WebsiteBrandAnalyzerTool',
    'AgentFactory',
    'ProductAnalyzerTool',
    'WebScrapingTool',
    'OrganizationAnalyzerTool'
]
