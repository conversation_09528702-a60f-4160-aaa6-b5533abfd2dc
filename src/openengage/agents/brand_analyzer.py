"""
Brand Analyzer Agent for extracting brand guidelines from websites.

This agent analyzes website HTML and CSS to extract brand colors,
typography, and UI component styles.
"""

from crewai.tools import BaseTool
from typing import Dict, List, Any, Optional
from openai import OpenAI
import os
import json
import requests
from bs4 import BeautifulSoup
import re
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class BrandGuidelines(BaseModel):
    """
    Model for website brand guidelines output.
    """
    primary_color: str = Field(description="Primary brand color (hex code)")
    secondary_color: str = Field(description="Secondary brand color (hex code)")
    accent_color: str = Field(description="Accent/highlight color (hex code)")
    neutral_color: str = Field(description="Neutral color for backgrounds/text (hex code)")
    background_color: str = Field(description="Main background color (hex code)")
    text_color: str = Field(description="Main text color (hex code)")
    has_gradient: bool = Field(description="Whether the website uses gradient colors", default=False)
    gradient_colors: Optional[str] = Field(description="Gradient color description (e.g., 'linear-gradient(to right, #ff416c, #ff4b2b)')", default=None)
    gradient_direction: Optional[str] = Field(description="Direction of gradient (e.g., 'to right', 'to bottom', 'diagonal')", default=None)
    cta_type: str = Field(description="Type of call-to-action (Button, Link, etc.)")
    cta_size: str = Field(description="Size of call-to-action elements (Small, Medium, Large)")
    button_style: str = Field(description="Button style (Rounded, Square, Pill, etc.)")
    border_radius: str = Field(description="Border radius size (e.g., '4px', '0.5rem')")
    font: str = Field(description="Primary font family")
    font_size: str = Field(description="Base font size (e.g., '16px')")
    font_weight: str = Field(description="Default font weight (Normal, Bold, etc.)")
    mission: Optional[str] = Field(description="Company mission statement if found", default=None)
    vision: Optional[str] = Field(description="Company vision statement if found", default=None)
    brand_personality: Optional[str] = Field(description="Brand personality traits if found", default=None)
    tone_of_voice: Optional[str] = Field(description="Brand tone of voice if found", default=None)
    archetype_scores: Optional[Dict[str, Dict[str, Any]]] = Field(description="Scores and reasons for each brand archetype", default=None)

class WebsiteBrandAnalyzerTool(BaseTool):
    """Tool for extracting brand guidelines from websites."""

    name: str = "Website Brand Analyzer"
    description: str = "Analyzes website HTML and CSS to extract brand guidelines and visual identity elements"

    def _extract_html_and_css(self, url: str) -> tuple:
        """Extract HTML content and CSS from a website."""
        try:
            # Send request with headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # Parse content
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract inline CSS
            inline_css = ""
            for style in soup.find_all('style'):
                inline_css += style.string if style.string else ""

            # Extract linked CSS files
            linked_css = ""
            for link in soup.find_all('link', rel='stylesheet'):
                css_url = link.get('href')
                if css_url:
                    # Handle relative URLs
                    if css_url.startswith('/'):
                        base_url = '/'.join(url.split('/')[:3])  # http(s)://domain.com
                        css_url = base_url + css_url
                    elif not css_url.startswith(('http://', 'https://')):
                        css_url = url.rstrip('/') + '/' + css_url

                    try:
                        css_response = requests.get(css_url, headers=headers, timeout=5)
                        if css_response.status_code == 200:
                            linked_css += css_response.text
                    except:
                        pass  # Skip if CSS file can't be fetched

            # Extract inline styles from elements for gradient detection
            inline_styles = ""
            for element in soup.find_all(style=True):
                inline_styles += element.get('style', '') + "\n"

            # Combine all CSS
            all_css = inline_css + linked_css + inline_styles

            return soup, all_css

        except Exception as e:
            raise Exception(f"Failed to extract website content: {str(e)}")

    def _execute(self, url: str) -> Dict[str, Any]:
        """Execute the brand analysis on the given website URL."""
        try:
            # Extract HTML and CSS
            soup, css = self._extract_html_and_css(url)

            # Get text content for mission/vision analysis
            text_content = soup.get_text()

            # Prepare prompt with HTML, CSS, and extracted text
            html_sample = str(soup.prettify())[:3000]  # First 3000 chars of HTML
            css_sample = css[:3000]  # First 3000 chars of CSS

            # Use OpenAI to analyze the content
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            # Send the website content to the GPT model
            # Pre-analyze CSS for gradient patterns
            has_gradient = False
            gradient_colors = None
            gradient_direction = None
            gradient_details = None

            # Look for gradients in CSS
            import re
            gradient_patterns = [
                r'linear-gradient\s*\([^)]+\)',
                r'radial-gradient\s*\([^)]+\)',
                r'conic-gradient\s*\([^)]+\)',
                r'repeating-linear-gradient\s*\([^)]+\)',
                r'repeating-radial-gradient\s*\([^)]+\)'
            ]

            all_gradients = []
            for pattern in gradient_patterns:
                gradient_matches = re.findall(pattern, css)
                all_gradients.extend(gradient_matches)

            # Sort by length to find the most complex/detailed gradient
            all_gradients.sort(key=len, reverse=True)

            if all_gradients:
                has_gradient = True
                gradient_colors = all_gradients[0]  # Get the most complex match

                # Try to extract direction
                direction_match = re.search(r'(?:to\s+(?:top|bottom|left|right|top\s+left|top\s+right|bottom\s+left|bottom\s+right)|\d+deg)', gradient_colors)
                if direction_match:
                    gradient_direction = direction_match.group(0)
                else:
                    gradient_direction = "not specified"

                # Extract all hex colors in the gradient
                hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', gradient_colors)
                if hex_colors:
                    gradient_details = ", ".join(hex_colors)

                # Also look for gradients in element backgrounds
                print(f"Found gradient: {gradient_colors}")
                print(f"Direction: {gradient_direction}")
                print(f"Colors: {gradient_details}")

            response = client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a brand identity expert who analyzes websites to extract branding guidelines.
                        Look for patterns in colors, typography, button styles, and company information.

                        Analyze the provided HTML and CSS to extract:
                        1. Color scheme(as hex codes):
                            -Primary Color: The core color that represents your brand’s identity and is used most prominently.
                            -Secondary Color: A complementary color that adds depth and variety to your visual palette.
                            -Accent Color: A bold, eye-catching color used sparingly to highlight important elements or actions.
                            -Neutral Color: Subtle tones like gray, beige, or white that provide balance and support the main colors.
                            -Background Color: The base color used behind content to create contrast and visual clarity.
                            -Text Color: A color chosen for legibility and contrast to ensure your message is readable on all backgrounds.
                        2. Gradient colors: look for linear-gradient, radial-gradient, etc. in CSS
                        3. Button/CTA styling: type (Button/Link), size (Small/Medium/Large), style (Rounded/Square/Pill), border radius
                        4. Typography: font family, size, weight
                        5. Brand identity: mission, vision, personality traits, tone of voice (if available)

                        For colors, look for the most prominent and consistently used colors in headers, buttons, backgrounds.
                        For missing values, provide reasonable defaults based on common web design patterns.

                        For brand personality, you MUST analyze ALL of these 12 Brand Archetypes and provide a score (0-10) and detailed reasoning for each one. Then select exactly ONE archetype that best matches the brand as the primary brand personality. Definition of all archetypes are given below:
                        1. Creator – Innovates to build original, lasting products or experiences that express their vision.
                        2. Sage – Seeks truth and wisdom to enlighten others through knowledge and insight.
                        3. Caregiver – Protects and nurtures others with compassion and selflessness.
                        4. Innocent – Spreads joy and optimism by living simply and doing what’s right.
                        5. Jester – Brings happiness through humor, fun, and lightheartedness.
                        6. Magician – Transforms reality to create awe-inspiring, dream-like experiences.
                        7. Ruler – Leads with authority and order to achieve control, success, and stability.
                        8. Hero – Strives to overcome challenges and inspire through courage and determination.
                        9. Everyman – Relatable and grounded, values connection and belonging for all.
                        10. Rebel – Challenges norms to spark change and revolution with bold independence.
                        11. Explorer – Embarks on adventures to discover new experiences and personal freedom.
                        12. Lover – Pursues deep emotional and physical connections through passion and desire.

                        Do not use any other personality descriptions or multiple archetypes for the primary brand personality.
                        """
                    },
                    {
                        "role": "user",
                        "content": f"""Analyze this website with URL: {url}

                        HTML Sample:
                        {html_sample}

                        CSS Sample:
                        {css_sample}

                        Text content sample (for mission/vision analysis):
                        {text_content[:3000]}

                        Extract the brand guidelines and return as JSON with these exact keys:
                        {{
                            "primary_color": "#hex",
                            "secondary_color": "#hex",
                            "accent_color": "#hex",
                            "neutral_color": "#hex",
                            "background_color": "#hex",
                            "text_color": "#hex",
                            "has_gradient": true/false,
                            "gradient_colors": "description of gradient colors or null",
                            "gradient_direction": "direction of gradient or null",
                            "cta_type": "Button or Link",
                            "cta_size": "Small/Medium/Large",
                            "button_style": "Rounded/Square/Pill",
                            "border_radius": "size with unit",
                            "font": "Font Family",
                            "font_size": "size with unit",
                            "font_weight": "Normal/Bold/etc",
                            "mission": "Mission statement if found",
                            "vision": "Vision statement if found",
                            "brand_personality": "One of the following brand archetypes: The Innocent, Everyman, Hero, Outlaw, Explorer, Creator, Ruler, Magician, Lover, Caregiver, Jester, or Sage",
                            "brand_personality_reasoning": "Explanation of why this brand archetype was selected, based on website content, visuals, messaging, tone, and overall brand presentation",
                            "tone_of_voice": "3-4 brand tone of voice Separated by Commas",
                            "archetype_scores": {{
                                "Creator": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Sage": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Caregiver": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Innocent": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Jester": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Magician": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Ruler": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Hero": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Everyman": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Rebel": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Explorer": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
                                "Lover": {{"score": 0-10, "reasoning": "Detailed reasoning"}}
                            }}
                        }}

                        If you can't determine specific values, use sensible defaults but note them as 'default'.

                        For the gradient analysis, I've already detected: has_gradient={has_gradient}, gradient_colors='{gradient_colors}', gradient_direction='{gradient_direction}'. Use this information in your analysis."""
                    }
                ],
                temperature=0.3,
                max_tokens=1024,
                response_format={"type": "json_object"}
            )

            response_content = response.choices[0].message.content
            print(f"Raw API response (first 500 chars): {response_content[:500]}...")
            parsed_response = json.loads(response_content)

            # Assemble return data with brand guidelines and additional extracted info
            return_data = {
                "primary_color": parsed_response.get("primary_color", ""),
                "// Example primary color used in headers/buttons, adjust based on actual usage if necessary": parsed_response.get("primary_color", ""),
                "secondary_color": parsed_response.get("secondary_color", ""),
                "// Example secondary color used in accents, adjust based on actual usage if necessary": parsed_response.get("secondary_color", ""),
                "accent_color": parsed_response.get("accent_color", ""),
                "// Example accent color used for highlights, adjust based on actual usage if necessary": parsed_response.get("accent_color", ""),
                "neutral_color": parsed_response.get("neutral_color", "#FFFFFF"),
                "// Default neutral color for backgrounds, can be adjusted": parsed_response.get("neutral_color", "#FFFFFF"),
                "background_color": parsed_response.get("background_color", "#F5F5F5"),
                "// Default background color, can be adjusted": parsed_response.get("background_color", "#F5F5F5"),
                "text_color": parsed_response.get("text_color", "#212121"),
                "// Default text color, can be adjusted": parsed_response.get("text_color", "#212121"),
                "cta_type": parsed_response.get("cta_type", "Button"),
                "// Commonly used for call-to-action elements": parsed_response.get("cta_type", "Button"),
                "cta_size": parsed_response.get("cta_size", "Medium"),
                "// Default size for buttons, can be adjusted based on actual usage": parsed_response.get("cta_size", "Medium"),
                "button_style": parsed_response.get("button_style", "Rounded"),
                "// Default button style, can be adjusted based on actual usage": parsed_response.get("button_style", "Rounded"),
                "border_radius": parsed_response.get("border_radius", "8px"),
                "// Default border radius for buttons, can be adjusted based on actual usage": parsed_response.get("border_radius", "8px"),
                "font": parsed_response.get("font", ""),
                "// Extracted font family from CSS": "",
                "font_size": parsed_response.get("font_size", "16px"),
                "// Default font size, can be adjusted based on actual usage": parsed_response.get("font_size", "16px"),
                "font_weight": parsed_response.get("font_weight", "Normal"),
                "// Default font weight, can be adjusted based on actual usage": parsed_response.get("font_weight", "Normal"),
                "mission": parsed_response.get("mission", ""),
                "vision": parsed_response.get("vision", ""),
                "brand_personality": parsed_response.get("brand_personality", ""),
                "brand_personality_reasoning": parsed_response.get("brand_personality_reasoning", ""),
                "tone_of_voice": parsed_response.get("tone_of_voice", ""),
                "archetype_scores": parsed_response.get("archetype_scores", {}),
                "organization_url": url
            }

            # Debug: Print the archetype scores to make sure they're being included
            archetype_scores = parsed_response.get("archetype_scores", {})
            if archetype_scores:
                print(f"Archetype scores found: {len(archetype_scores)} archetypes scored")
                print(f"Sample archetype score: {list(archetype_scores.items())[0] if archetype_scores else 'None'}")
            else:
                print("No archetype scores found in the response")

            # Add gradient information if found
            if has_gradient and gradient_colors:
                return_data["has_gradient"] = True
                return_data["gradient_colors"] = gradient_colors
                if gradient_direction:
                    return_data["gradient_direction"] = gradient_direction
                if gradient_details:
                    return_data["gradient_details"] = gradient_details

            return return_data

        except Exception as e:
            # Return default guidelines if analysis fails
            default_guidelines = {
                "primary_color": "#default",
                "secondary_color": "#default",
                "accent_color": "#default",
                "neutral_color": "#default",
                "background_color": "#default",
                "text_color": "#default",
                "has_gradient": False,
                "gradient_colors": None,
                "gradient_direction": None,
                "cta_type": "Button",
                "cta_size": "Medium",
                "button_style": "Rounded",
                "border_radius": "4px",
                "font": "Default system font",
                "font_size": "16px",
                "font_weight": "Normal",
                "mission": None,
                "vision": None,
                "brand_personality": None,
                "brand_personality_reasoning": None,
                "tone_of_voice": None,
                "archetype_scores": {},
                "error": str(e)
            }
            return default_guidelines

    def _run(self, url: str) -> Dict[str, Any]:
        """Run the tool with the given URL."""
        return self._execute(url)
