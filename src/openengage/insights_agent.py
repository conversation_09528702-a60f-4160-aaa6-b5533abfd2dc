#!/usr/bin/env python3
"""
Email Marketing SQL Agent with LangGraph

This script implements an SQL agent that uses <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>rap<PERSON> with OpenAI GPT-4o
to translate natural language queries into SQL for email marketing campaign analysis.
"""

# ----- 1. Import Libraries -----
import os
import sqlite3
import json
import yaml
import traceback
from dotenv import load_dotenv
from typing import Annotated, Literal, Any, Dict, List, Optional, Union

# LangChain & LangGraph Imports
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.utilities import SQLDatabase 
from langchain_core.messages import AIMessage, AnyMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableLambda, RunnableWithFallbacks
from langchain_community.agent_toolkits import SQLDatabaseToolkit

# Pydantic & LangGraph
from pydantic import BaseModel, Field
from typing_extensions import TypedDict
from langgraph.graph import E<PERSON>, StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode

# Optional visualization imports
try:
    from IPython.display import Image, display
    from langchain_core.runnables.graph import MermaidDrawMethod
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False

# ----- 2. Configuration Loading -----

# Load environment variables
load_dotenv()

# Load prompts and SQL queries from config files
def load_config():
    """Load prompts and SQL queries from the config files"""
    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
    
    # Load prompts
    prompts_path = os.path.join(config_dir, 'prompts.yml')
    try:
        with open(prompts_path, 'r', encoding='utf-8') as file:
            prompts_config = yaml.safe_load(file)
            prompts = prompts_config['sql_agent']
    except FileNotFoundError:
        print(f"Prompts config file not found at: {prompts_path}")
        raise
    except KeyError:
        print("sql_agent section not found in prompts config")
        raise
    
    # Load SQL queries
    queries_path = os.path.join(config_dir, 'sql_queries.yml')
    try:
        with open(queries_path, 'r', encoding='utf-8') as file:
            queries_config = yaml.safe_load(file)
    except FileNotFoundError:
        print(f"SQL queries config file not found at: {queries_path}")
        raise
    
    return prompts, queries_config

# Load configuration before any usage
prompts, sql_queries = load_config()
print("Prompts and SQL queries loaded from config files.")

# Set database path to root project directory
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DB_NAME = os.path.join(root_dir, "email_marketing.db")
print(f"Database path set to: {DB_NAME}")

# Use a connection with timeout and check if file exists before connecting
import os.path
if not os.path.exists(DB_NAME):
    print(f"WARNING: Database file {DB_NAME} does not exist!")
    raise FileNotFoundError(f"Database file {DB_NAME} not found")

try:
    # Use a connection with timeout to avoid locking issues
    connection = sqlite3.connect(DB_NAME, timeout=60.0)
    # Disable WAL mode as it might cause issues on some systems
    connection.execute('PRAGMA journal_mode=DELETE')  # Use DELETE journal mode instead of WAL
    connection.execute('PRAGMA synchronous=NORMAL')  # Balance between safety and performance
    connection.execute('PRAGMA busy_timeout=60000')  # Set busy timeout to 60 seconds
    cursor = connection.cursor()
    print(f"Connected to database: {DB_NAME}")
except sqlite3.Error as e:
    print(f"SQLite connection error: {e}")
    raise

# ----- 3. LLM Initialization -----
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    print("OPENAI_API_KEY not found. Please set it in your .env file or environment.")
    raise ValueError("OPENAI_API_KEY environment variable is not set")
else:
    os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY
    print("OPENAI_API_KEY loaded.")

llm = ChatOpenAI(model="gpt-4o")#-mini-2024-07-18")
print("ChatOpenAI GPT-4o LLM initialized.")

# ----- 4. Database Setup -----
# Database path already set above - using full path to database

# ----- 5. SQLDatabase Connection & Toolkit -----
try:
    print(f"\n[DEBUG] Attempting to connect to database: {DB_NAME}")
    # Check if the database file exists
    import os.path
    if os.path.exists(DB_NAME):
        print(f"[DEBUG] Database file exists. Size: {os.path.getsize(DB_NAME)} bytes")
    else:
        print(f"[DEBUG] WARNING: Database file does not exist at: {os.path.abspath(DB_NAME)}")
    
    # Try opening a direct connection to check schema
    direct_conn = sqlite3.connect(DB_NAME)
    direct_cursor = direct_conn.cursor()
    print("[DEBUG] Direct SQLite connection established")
    
    # Check for tables directly
    direct_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = direct_cursor.fetchall()
    print(f"[DEBUG] Tables found via direct connection: {tables}")
    
    # Check row counts for each table
    for table in tables:
        table_name = table[0]
        direct_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = direct_cursor.fetchone()[0]
        print(f"[DEBUG] Table '{table_name}' contains {row_count} rows")
    
    direct_conn.close()
    
    # Create SQLDatabase connection with proper URI format
    # Use a simpler URI format to avoid potential issues
    uri = f"sqlite:///{DB_NAME}"
    db = SQLDatabase.from_uri(uri)
    print(f"[DEBUG] SQLDatabase connection successful with URI: {uri}")
    print("Dialect:", db.dialect)
    
    # Get usable tables
    usable_tables = db.get_usable_table_names()
    print(f"[DEBUG] Usable tables: {usable_tables}")
    if not usable_tables:
        print("[DEBUG] WARNING: No usable tables found in the database!")
    
    # Try to get sample data
    if 'campaign_data' in usable_tables:
        try:
            sample = db.run("SELECT * FROM campaign_data LIMIT 2")
            print("Sample rows from campaign_data:", sample)
        except Exception as e:
            print(f"[DEBUG] Error fetching sample data: {str(e)}")
    else:
        print("[DEBUG] campaign_data table not found - database may be empty or not properly initialized")
        
except Exception as e:
    print(f"[DEBUG] Database connection error: {str(e)}")
    import traceback
    print(traceback.format_exc())
    raise

# ----- 6. LangGraph Agent Definition -----

# --- 6.1 Define Tools ---
# Standard SQL tools from toolkit
toolkit = SQLDatabaseToolkit(db=db, llm=llm)
sql_tools = toolkit.get_tools() # This gives query, schema, list_tables, query_checker

# Extract specific tools we'll use in the graph if needed by name, or use them as a list
list_tables_tool = next((t for t in sql_tools if t.name == "sql_db_list_tables"), None)
get_schema_tool = next((t for t in sql_tools if t.name == "sql_db_schema"), None)

# Custom tool for executing queries, ensuring it doesn't throw an error to stop the graph
@tool
def db_query_tool(query: str) -> str:
    """
    Execute a SQL query against the database and return the result.
    If the query is invalid or returns no result, an error message or 'No results found.' will be returned.
    In case of an error, the user is advised to rewrite the query and try again.
    """
    
    print(f"\n[DEBUG] Executing SQL query: {query}")
    try:
        # Create a direct connection to the database for this query
        # This bypasses potential issues with the SQLDatabase abstraction
        direct_conn = None
        try:
            # First check if database has tables using the SQLDatabase object
            usable_tables = db.get_usable_table_names()
            print(f"[DEBUG] Usable tables before query: {usable_tables}")
            if not usable_tables:
                print("[DEBUG] WARNING: Attempting to query a database with no tables!")
                return "Error: Database appears to be empty. No tables available to query."
            
            # Execute the query with timeout and retry logic using a direct connection
            max_retries = 5
            retry_count = 0
            last_error = None
            
            while retry_count < max_retries:
                try:
                    # Create a fresh connection for each retry
                    if direct_conn:
                        try:
                            direct_conn.close()
                        except:
                            pass
                    
                    direct_conn = sqlite3.connect(DB_NAME, timeout=60.0)
                    direct_conn.execute('PRAGMA busy_timeout=60000')  # 60 seconds
                    direct_cursor = direct_conn.cursor()
                    
                    # Execute the query directly
                    direct_cursor.execute(query)
                    rows = direct_cursor.fetchall()
                    
                    # Get column names
                    column_names = [description[0] for description in direct_cursor.description] if direct_cursor.description else []
                    
                    # Format the result as a string similar to SQLDatabase.run
                    if not rows:
                        print("[DEBUG] Query executed but returned empty result")
                        if direct_conn:
                            direct_conn.close()
                        return "Query executed successfully, but no results were found for your criteria."
                    
                    # Format the result as a table string
                    result = "\n" + " | ".join(column_names) + "\n"
                    result += "-" * len(result) + "\n"
                    for row in rows:
                        result += " | ".join(str(cell) for cell in row) + "\n"
                    
                    print(f"[DEBUG] Query result: {result[:200]}...")
                    
                    if direct_conn:
                        direct_conn.close()
                    return result
                    
                except sqlite3.OperationalError as e:
                    if "disk I/O error" in str(e) or "database is locked" in str(e):
                        retry_count += 1
                        last_error = e
                        print(f"[DEBUG] SQLite error on attempt {retry_count}/{max_retries}: {e}. Retrying...")
                        import time
                        time.sleep(2 * retry_count)  # Exponential backoff
                    else:
                        if direct_conn:
                            direct_conn.close()
                        print(f"[DEBUG] SQLite operational error: {e}")
                        return f"Error: SQLite operational error: {str(e)}"
                except Exception as e:
                    if direct_conn:
                        direct_conn.close()
                    print(f"[DEBUG] Unexpected error: {e}")
                    return f"Error: Unexpected error during query execution: {str(e)}"
            
            # If we've exhausted retries
            if last_error:
                if direct_conn:
                    direct_conn.close()
                print(f"[DEBUG] Exhausted retries: {last_error}")
                return f"Error: Database access issue after {max_retries} retries: {str(last_error)}"
        except Exception as e:
            if direct_conn:
                direct_conn.close()
            raise e
            
    except Exception as e:
        error_msg = f"Exception during query execution: {str(e)}"
        print(f"[DEBUG] {error_msg}")
        import traceback
        print(traceback.format_exc())
        return f"Error: {error_msg}"

# Pydantic model for submitting the final answer
class SubmitFinalAnswer(BaseModel):
    """Submit the final answer to the user based on the query results."""
    final_answer: str = Field(..., description="The final natural language answer to the user's question, based on the SQL query results.")

print("Tools defined.")
print(f"List tables tool: {list_tables_tool.name if list_tables_tool else 'Not found'}")
print(f"Get schema tool: {get_schema_tool.name if get_schema_tool else 'Not found'}")
print(f"DB query tool: {db_query_tool.name}")

# #### 4.2. Define State

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    # Optional: to explicitly track errors if needed beyond tool message content
    error: str | None 

print("State defined.")

# #### 4.3. Define Prompts and LLM Bindings
query_check_system = prompts['query_check_system']

query_check_prompt = ChatPromptTemplate.from_messages([
    ("system", query_check_system),
    ("placeholder", "{messages}")
])

query_check = query_check_prompt | llm.bind_tools([db_query_tool])

print("Query check prompt and LLM binding defined.")

query_gen_system = prompts['query_generation_system']

query_gen_prompt = ChatPromptTemplate.from_messages([
    ("system", query_gen_system),
    ("placeholder", "{messages}")
])

query_gen = query_gen_prompt | llm.bind_tools([SubmitFinalAnswer])
print("Query generation prompt and LLM binding defined.")

# #### 4.4. Define Nodes and Edges

def first_tool_call(state: State) -> dict[str, list[AIMessage]]:
    # This node prepares the initial call to list tables to inform the LLM about the DB structure.
    return {"messages": [AIMessage(content="", tool_calls=[{"name": "sql_db_list_tables", "args": {}, "id": "tool_list_tables"}])]}

def handle_tool_error(state: State) -> dict:
    error = state.get("error")
    # Ensure messages is a list and the last message exists and has tool_calls
    messages = state.get("messages", [])
    if not messages or not hasattr(messages[-1], 'tool_calls') or not messages[-1].tool_calls:
        # Fallback if there's an error but no tool_calls to attribute it to (should be rare)
        return {"messages": [AIMessage(content=f"An unexpected error occurred: {repr(error)}. Please try rephrasing your request.")]}
    
    tool_calls = messages[-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"Error: Tool call failed with error: {repr(error)}. Please analyze the error and fix your approach.",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }

def create_tool_node_with_fallback(tools: list) -> RunnableWithFallbacks[Any, dict]:
    return ToolNode(tools).with_fallbacks(
        [RunnableLambda(handle_tool_error)], exception_key="error"
    )



# ... (inside query_gen_node)
def query_gen_node(state: State):
    # ... (existing code) ...
    print(f"--- Entering query_gen_node ---")
    current_messages = state["messages"]
    response_message = query_gen.invoke({"messages": current_messages})
    print(f"query_gen_node initial LLM response_message: {response_message}")

    tool_error_messages = []

    # Attempt to fix LLM outputting tool call as string content
    if not response_message.tool_calls and response_message.content:
        try:
            # Check if content is a JSON string that looks like a tool call
            content_as_json = json.loads(response_message.content)
            if isinstance(content_as_json, dict) and "tool_calls" in content_as_json:
                # Attempt to reconstruct the message with proper tool_calls
                parsed_tool_calls = content_as_json["tool_calls"]
                # Basic validation of the parsed structure
                if isinstance(parsed_tool_calls, list) and \
                   all(isinstance(tc, dict) and "name" in tc and "args" in tc and "id" in tc for tc in parsed_tool_calls):
                    print(f"query_gen_node: Detected tool call in content, attempting to parse and fix.")
                    response_message.tool_calls = parsed_tool_calls
                    response_message.content = "" # Clear content if it was just the tool call
                    print(f"query_gen_node: Fixed response_message: {response_message}")
                else:
                    print(f"query_gen_node: Content looked like JSON tool_calls but structure was invalid.")
            elif isinstance(content_as_json, dict) and "function" in content_as_json and "parameters" in content_as_json and content_as_json.get("function", {}).get("name") == "SubmitFinalAnswer":
                # Handle cases where the LLM might output a single tool call structure directly as JSON content
                print(f"query_gen_node: Detected single tool call (SubmitFinalAnswer) in content, attempting to parse and fix.")
                tool_call_args = content_as_json["parameters"]
                tool_call_name = content_as_json["function"]["name"]
                response_message.tool_calls = [{"name": tool_call_name, "args": tool_call_args, "id": "parsed_from_content"}]
                response_message.content = "" # Clear content
                print(f"query_gen_node: Fixed response_message for single tool_call: {response_message}")

        except json.JSONDecodeError:
            # Content is not JSON, or not the specific JSON structure we're looking for
            pass # Proceed as normal

    if response_message.tool_calls:
        for tc in response_message.tool_calls:
            if tc["name"] != "SubmitFinalAnswer":
                tool_error_messages.append(
                    ToolMessage(
                        content=f"Error: You called the wrong tool: {tc['name']}. You should only call SubmitFinalAnswer to give the final response, or output a plain SQL query. Please regenerate the SQL query as plain text.",
                        tool_call_id=tc["id"],
                    )
                )
    
    final_messages_to_add = [response_message] + tool_error_messages
    print(f"--- Exiting query_gen_node (adding {len(final_messages_to_add)} messages) ---")
    return {"messages": final_messages_to_add}


def model_check_query_node(state: State) -> dict[str, list[AIMessage]]:
    """Node that calls the LLM to check/correct a generated SQL query before execution."""
    # The last message should be the plain text SQL query generated by query_gen_node
    print("--- Entering model_check_query_node ---")
    # print(f"Current state messages for model_check_query: {state['messages'][-3:]}")
    
    # The query to check is expected to be the content of the last AIMessage if it's not a tool_call
    # This logic assumes query_gen_node produces an AIMessage with the SQL query as its `content`.
    # And that message should NOT have tool_calls (unless it was a mistake handled by query_gen_node)
    if state["messages"][-1].tool_calls: # Should not happen if query_gen is correct
        print("Error in model_check_query_node: Expected plain query, got tool call.")
        # This is a graph logic error, pass it through to let the graph handle it or error out
        return {"messages": [AIMessage(content="Graph Logic Error: model_check_query_node received unexpected tool call.")]}

    # query_check is LLM bound with db_query_tool
    # We pass the last message (which should contain the raw SQL from query_gen) as a HumanMessage to fit query_check_prompt
    raw_sql_query_message = HumanMessage(content=state["messages"][-1].content)
    checked_query_response = query_check.invoke({"messages": [raw_sql_query_message]})
    print(f"model_check_query_node response: {checked_query_response}")
    print("--- Exiting model_check_query_node ---")
    return {"messages": [checked_query_response]}

def should_continue(state: State) -> Literal[END, "model_check_query_node", "query_gen_node"]:
    print("--- Deciding in should_continue ---")
    messages = state["messages"]
    last_message = messages[-1]
    # print(f"Last message for should_continue: {last_message}")

    if isinstance(last_message, ToolMessage) and "Error: Tool call failed" in last_message.content:
        print("Decision: Tool execution error, go back to query_gen_node to retry.")
        return "query_gen_node"
    
    if isinstance(last_message, ToolMessage) and "Query executed successfully, but no results were found" in last_message.content:
        print("Decision: Query returned no results, go to query_gen_node for interpretation.")
        return "query_gen_node"

    if getattr(last_message, "tool_calls", None):
        is_final_answer = any(tc['name'] == 'SubmitFinalAnswer' for tc in last_message.tool_calls)
        if is_final_answer:
            print("Decision: SubmitFinalAnswer called via tool_calls, END.")
            return END
        else: # LLM called a tool other than SubmitFinalAnswer (e.g. db_query_tool directly from query_gen)
              # This can happen if query_gen_node's correction logic above wasn't triggered
              # or if the LLM in query_gen decided to call db_query_tool itself.
            print(f"Decision: LLM called unexpected tool '{last_message.tool_calls[0]['name']}' from query_gen, go to query_gen_node to correct.")
            # We want query_gen_node to see this problematic tool call and add an error message.
            return "query_gen_node" # This might be redundant if query_gen_node already added error, but safe.

    if isinstance(last_message, AIMessage) and not last_message.tool_calls and last_message.content:
        # Check if the content is likely an SQL query
        content_lower = last_message.content.lower()
        # Simple check for SQL keywords, can be made more robust
        is_sql_like = any(keyword in content_lower for keyword in ["select ", "insert ", "update ", "delete ", "create ", "drop ", "alter "])
        
        if is_sql_like and "submitfinalanswer" not in content_lower: # Further check to avoid misinterpreting stringified tool calls
            print("Decision: Plain SQL query generated, go to model_check_query_node.")
            return "model_check_query_node"
        else:
            # Content is present but not SQL-like, or might be a stringified SubmitFinalAnswer
            # Let query_gen_node try to parse it or handle it.
            print("Decision: AIMessage with content, but not clearly SQL or already handled as tool_call. Route to query_gen_node.")
            return "query_gen_node"
            
    print("Decision: Default or tool result (not an error, not no results), go to query_gen_node for interpretation/retry.")
    return "query_gen_node"

print("Node and edge logic functions defined.")

# #### 4.5. Construct the Graph

workflow = StateGraph(State)

# Add nodes
workflow.add_node("first_tool_call_node", first_tool_call) # Kicks off by listing tables
workflow.add_node("list_tables_tool_node", create_tool_node_with_fallback([list_tables_tool]))

# Node to prompt LLM to call get_schema_tool based on listed tables
model_get_schema_prompt = ChatPromptTemplate.from_messages([
    ("system", "Based on the following list of tables, identify all relevant tables for answering typical email marketing analysis questions. Then, call the 'sql_db_schema' tool with a comma-separated list of these table names to get their schema. Tables: {table_list}"),
    ("placeholder", "{messages}") # To carry over the ToolMessage from list_tables_tool
])
model_get_schema = model_get_schema_prompt | llm.bind_tools([get_schema_tool])
workflow.add_node("model_get_schema_node", 
                  lambda state: {"messages": [model_get_schema.invoke({"messages": state["messages"], 
                                                                       "table_list": state["messages"][-1].content})]} # Get table list from previous tool message
                 )

workflow.add_node("get_schema_tool_node", create_tool_node_with_fallback([get_schema_tool]))
workflow.add_node("query_gen_node", query_gen_node) # Generates SQL or final answer
workflow.add_node("model_check_query_node", model_check_query_node) # LLM checks/corrects SQL
workflow.add_node("execute_query_tool_node", create_tool_node_with_fallback([db_query_tool])) # Executes corrected SQL
# workflow.add_node("final_answer_node", create_tool_node_with_fallback([SubmitFinalAnswer])) # Dummy node for SubmitFinalAnswer if it were a real tool call for graph to hit END

# Define edges
workflow.add_edge(START, "first_tool_call_node")
workflow.add_edge("first_tool_call_node", "list_tables_tool_node")
workflow.add_edge("list_tables_tool_node", "model_get_schema_node") 
workflow.add_edge("model_get_schema_node", "get_schema_tool_node")
workflow.add_edge("get_schema_tool_node", "query_gen_node") # Schema info goes to query_gen

workflow.add_conditional_edges(
    "query_gen_node",
    should_continue,
    {
        "model_check_query_node": "model_check_query_node",
        "query_gen_node": "query_gen_node", # Loop back if error or needs more processing
        END: END 
    }
)

workflow.add_edge("model_check_query_node", "execute_query_tool_node") # Checked query goes for execution
workflow.add_edge("execute_query_tool_node", "query_gen_node") # Results/errors of execution go back to query_gen for final answer formulation or retry

# Compile the graph
app = workflow.compile()
print("LangGraph workflow compiled.")

# #### 4.6. Visualize the Graph (Optional)

try:
    display(
        Image(
            app.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API, # Requires internet and API access for mermaid.ink
                # Or use MermaidDrawMethod.PYPPETEER if you have pyppeteer installed
            )
        )
    )
except Exception as e:
    print(f"Could not display graph: {e}. Ensure playwright is installed for local rendering or use API method.")
    # To install playwright for local rendering: 
    # pip install playwright
    # playwright install

# ### 5. Running the Agent
def run_query(question):
    print(f"\n--- Running query for: '{question}' ---")
    
    # Verify database state before running query
    print("[DEBUG] Checking database state before running the query")
    try:
        # Check if database file exists and has tables
        import os.path
        if not os.path.exists(DB_NAME):
            print(f"[DEBUG] WARNING: Database file {DB_NAME} does not exist!")
            return f"Error: Database file {DB_NAME} does not exist. Please set up the database first."
        
        # Check database tables via direct connection
        direct_conn = sqlite3.connect(DB_NAME)
        direct_cursor = direct_conn.cursor()
        direct_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = direct_cursor.fetchall()
        print(f"[DEBUG] Available tables in database: {tables}")
        
        # Check if any tables exist
        if not tables:
            print("[DEBUG] No tables found in database - it appears to be empty")
            return "The database appears to be empty. Please set up the database with data first."
            
        # Check row counts for crucial tables
        table_counts = {}
        for table in tables:
            table_name = table[0]
            try:
                direct_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = direct_cursor.fetchone()[0]
                table_counts[table_name] = count
                print(f"[DEBUG] Table '{table_name}' has {count} rows")
            except Exception as e:
                print(f"[DEBUG] Error counting rows in {table_name}: {str(e)}")
        
        direct_conn.close()
        
        # Check if all tables are empty
        if all(count == 0 for count in table_counts.values()):
            print("[DEBUG] All tables in the database are empty")
            return "All tables in the database are empty. Please populate the database with data first."
            
    except Exception as e:
        error_msg = f"Database verification error: {str(e)}"
        print(f"[DEBUG] {error_msg}")
        import traceback
        print(traceback.format_exc())
        # Continue with query processing despite the error, as we've logged it
    
    # Process the actual query
    query_input = {"messages": [("user", question)]}
    response = None
    print("[DEBUG] Starting LangGraph agent execution")
    
    try:
        event_count = 0
        for event in app.stream(query_input, {"recursion_limit": 50}): # Added recursion limit
            event_count += 1
            print(f"[DEBUG] Event {event_count}: {json.dumps(str(event)[:200])}...")
            last_message_key = list(event.keys())[-1] # Get the key of the node that produced the event
            print(f"[DEBUG] Node '{last_message_key}' output: {str(event[last_message_key])[:150]}...")
            
            # Track tool calls for debugging
            if isinstance(event.get(last_message_key), dict) and event.get(last_message_key).get('messages'):
                msgs = event.get(last_message_key).get('messages')
                for msg in msgs:
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        print(f"[DEBUG] Tool call detected: {msg.tool_calls[0].get('name', 'unknown')}")
                        if msg.tool_calls[0].get('name') == 'db_query_tool':
                            print(f"[DEBUG] SQL Query being executed: {msg.tool_calls[0].get('args', {}).get('query', 'unknown')}")
                        elif msg.tool_calls[0].get('name') == 'SubmitFinalAnswer':
                            print(f"[DEBUG] Final answer being submitted: {msg.tool_calls[0].get('args', {}).get('final_answer', 'unknown')}")
            
            if END in event: # Check if the END node is in the event keys
                response = event[END]
                print("[DEBUG] Reached END node")
                break
            elif isinstance(event, dict) and event.get(list(event.keys())[-1]) and event.get(list(event.keys())[-1]).get('messages'):
                 response = event.get(list(event.keys())[-1]) # Keep the last relevant message set
            else:
                 response = event # Fallback

        print(f"[DEBUG] Agent execution complete. Total events: {event_count}")
        print(f"[DEBUG] Final response object type: {type(response)}")
        
        if response and response.get("messages"):
            print(f"[DEBUG] Number of messages in response: {len(response.get('messages'))}")
            final_ai_message = response["messages"][-1]
            print(f"[DEBUG] Final AI Message object: {str(final_ai_message)[:200]}...")
            if hasattr(final_ai_message, 'tool_calls') and final_ai_message.tool_calls and final_ai_message.tool_calls[0]["name"] == "SubmitFinalAnswer":
                final_answer = final_ai_message.tool_calls[0]["args"]["final_answer"]
                print(f"\n[DEBUG] Found SubmitFinalAnswer with answer: {final_answer}")
                print(f"\nFinal Answer: {final_answer}")
                return final_answer
            else:
                print("\n[DEBUG] Agent did not call SubmitFinalAnswer. Last message:")
                content = final_ai_message.content if hasattr(final_ai_message, 'content') else str(final_ai_message)
                print(content)
                return f"Agent did not provide a final answer through SubmitFinalAnswer. Last message: {content[:200]}..."
        else:
            print("\n[DEBUG] No final response messages found or graph ended unexpectedly.")
            print(f"[DEBUG] Last known response object: {str(response)[:200]}...")
            return "No response from agent."
    except Exception as e:
        print(f"[DEBUG] An error occurred during agent execution: {e}")
        import traceback
        traceback.print_exc()
        return f"Error: {e}"

# Test Queries
questions = [
    # "What is the best performing subject line by Open Rate?",
    # "Give me the top 2 email copies by PCR (Click-Through Rate). Include subject and body.",
    # "Which sender has the highest average Click-Through Rate (PCR)?",
    # "How many mails were delivered today (assuming today is 2024-07-04)?", # LLM should use the date provided
    "Give me top 3 insights based on the campaign data, considering Open Rate and PCR."
]

for q in questions:
    run_query(q)
    print("--------------------------------------------------")

# Example of a more complex question that might require iteration or clarification
# run_query("Compare the performance of 'Sales Team' emails versus 'News Team' emails. Provide key metrics like total sent, average OR, and average PCR.")

# Close the database connection when done
try:
    connection.close()
    print("Database connection closed successfully.")
except Exception as e:
    print(f"Error closing database connection: {e}")

