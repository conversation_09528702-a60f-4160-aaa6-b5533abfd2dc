"""
Test script for the email formatter.
"""
import json
import os
from core.email_formatter import text_to_html

def main():
    """Test the email formatter with sample data."""
    # Sample email content with markdown link
    sample_email_with_markdown_link = {
        "subject": "Check Out Our Latest Features",
        "content": """
Hello Sarah,

We've just released some exciting new features in our Agentic AI Pioneer Program that we think you'll love.

Check out our [latest blog post](https://example.com/blog/new-features) to learn more about what's new.

Looking forward to your feedback!

Best regards,
The Product Team
        """,
        "generated_at": "2023-04-08T12:34:56.789Z",
        "stage": "Product Lead Generated"
    }

    # Sample email content with plain URL
    sample_email_with_url = {
        "subject": "Discover Our Amazing Product",
        "content": """
Hello John,

We noticed you've been looking at our Agentic AI Pioneer Program recently. We thought you might be interested in learning more about its features.

Our product offers:

<ul>
<li>Easy integration with your existing systems</li>
<li>Advanced analytics to track performance</li>
<li>24/7 customer support</li>
</ul>

Check out https://example.com/product for more details.

Did you know that users who adopt our solution see an average of 30% increase in productivity?

Let me know if you have any questions!

Best regards,
The Product Team
        """,
        "cta": "Learn More About Our Product",
        "generated_at": "2023-04-08T12:34:56.789Z",
        "stage": "Product Page Viewed"
    }

    # Sample communication settings
    communication_settings = {
        "sender_name": "Product Team",
        "style": "friendly",
        "length": "100-150 words",
        "utm_source": "email_campaign",
        "utm_medium": "email",
        "utm_campaign": "product_awareness",
        "utm_content": "feature_highlight",
        "organization_url": "https://example.com",
        "template_context": {
            "base_template": {
                "product_data": {
                    "Product_Name": "Agentic AI Pioneer Program",
                    "Company_Name": "Product Team",
                    "Company_URL": "https://example.com"
                }
            }
        }
    }

    # Sample product URL
    product_url = "https://example.com/product"

    # Convert to HTML
    html_email_markdown = text_to_html(
        sample_email_with_markdown_link,
        product_url=product_url,
        product_name="Agentic AI Pioneer Program",
        communication_settings=communication_settings,
        recipient_email="<EMAIL>",
        recipient_first_name="Sarah"
    )

    html_email_url = text_to_html(
        sample_email_with_url,
        product_url=product_url,
        product_name="Agentic AI Pioneer Program",
        communication_settings=communication_settings,
        recipient_email="<EMAIL>",
        recipient_first_name="John"
    )

    # Save the HTML to files
    os.makedirs("data/html_emails", exist_ok=True)

    with open("data/html_emails/sample_email_markdown.html", "w") as f:
        f.write(html_email_markdown)

    with open("data/html_emails/sample_email_url.html", "w") as f:
        f.write(html_email_url)

    print(f"HTML emails saved to data/html_emails/")

if __name__ == "__main__":
    main()
