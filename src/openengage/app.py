"""
OpenEngage - AI-powered marketing automation platform.
Main application entry point.
"""
import streamlit as st

# Configure the Streamlit page - MUST be the first Streamlit command
st.set_page_config(
    page_title="OpenEngage",
    page_icon="🚀",
    layout="wide"
)

import json
import os
import re
from dotenv import load_dotenv
from utils.file_utils import ensure_directories, load_feature_toggles, save_feature_toggles
from utils.session_utils import initialize_session_state, check_api_key

# Import UI components
from ui.login import display_login_signup
from ui.templates import display_templates_generator, display_template_verification, display_whatsapp_template_addition
from ui.journey import display_journey_builder
from ui.analytics import display_analytics
from ui.analytics_story import display_analytics_story
from ui.mass_email import display_mass_email_generator
from ui.personalized_email import display_personalized_email_generator
from ui.competitive import display_competitive_analysis
from ui.trigger_points import display_trigger_points
from ui.sending_config import display_sending_configuration
from ui.product_priority import display_product_priority
from ui.trend_analysis import display_trend_analysis
from ui.popup_config import display_popup_configuration
from ui.image_pool import display_image_pool
from ui.pruning_config import render_pruning_config
from ui.channel_selection import display_channel_selection
from ui.insights_chatbot import display_insights_chatbot
from ui.funnel_view import display_funnel_view

def main():
    """Main application entry point"""

     # Add custom CSS for sidebar styling
    st.markdown("""
    <style>
        /* Sidebar header styling */
        .css-1vq4p4l {padding-top: 1rem;}

        /* Expander styling */
        .streamlit-expander {margin-bottom: 0.5rem;}
        .streamlit-expanderHeader {font-weight: 600;}

        /* Sidebar button styling */
        .stButton button {margin-bottom: 0.5rem;}

        /* Settings styling */
        .settings-menu {
            position: fixed;
            bottom: 60px;
            right: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            z-index: 1000;
            max-height: 80vh;
            overflow-y: auto;
            min-width: 300px;
        }
        .settings-button-bottom {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #f0f2f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .settings-button-top {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #f0f2f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .settings-button-bottom:hover, .settings-button-top:hover {
            background-color: #e0e2e6;
        }
        /* Settings sections styling */
        .settings-section {
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .settings-header {
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
            font-size: 1rem;
        }
        /* Toggle styling */
        .stToggle {
            margin: 0.5rem 0;
        }
        .stToggle > div {
            flex-direction: row !important;
            justify-content: space-between !important;
            align-items: center !important;
        }
        .stToggle label {
            font-size: 0.9rem;
            margin-right: 1rem;
        }
        /* Settings title bar */
        .settings-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .settings-title h3 {
            margin: 0;
        }
        .close-button {
            cursor: pointer;
            font-size: 1.2rem;
            color: #666;
            background: none;
            border: none;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
        }
        .close-button:hover {
            background-color: #f0f2f6;
            color: #333;
        }
    </style>
    """, unsafe_allow_html=True)


    # Load environment variables
    load_dotenv()

    # Ensure required directories exist
    ensure_directories()

    # Initialize session state
    initialize_session_state()

    # Initialize view state if not already done
    views = [
        'show_dashboard', 'show_templates_generator', 'show_template_verification', 'show_whatsapp_template_addition',
        'show_journey_builder', 'show_analytics', 'show_analytics_story', 'show_mass_email_generator',
        'show_personalized_email_generator', 'show_funnel_view',
        'show_editor', 'show_org_editor', 'show_product_editor', 'show_competitive_analysis',
        'show_sending_configuration', 'show_product_priority', 'show_trend_analysis', 'show_popup_configuration',
        'show_image_pool', 'show_pruning_config', 'show_channel_selection', 'show_insights_chatbot'
    ]

    # Initialize all views to False
    for view in views:
        if view not in st.session_state:
            st.session_state[view] = False

    # Initialize organization-related state variables
    if "analyzing_organization" not in st.session_state:
        st.session_state.analyzing_organization = False

    # Initialize organization setup step if not already done
    if "org_setup_step" not in st.session_state:
        st.session_state.org_setup_step = 1

    # Initialize brand guidelines if not already done
    if "brand_guidelines" not in st.session_state:
        st.session_state.brand_guidelines = {
            "colors": [],
            "cta_type": "Button",
            "cta_size": "Medium",
            "font": "Arial"
        }

    # Initialize communication settings if not already done
    if "communication_settings" not in st.session_state:
        # Try to get organization name if available
        org_name = "OpenEngage Team"

        # First try to get organization name from current user
        if "current_user" in st.session_state and st.session_state.current_user:
            user_org = st.session_state.current_user.get('organization', {})
            if user_org:
                org_name = user_org.get('name', org_name)

        # If not found in current user, try organization_data
        if org_name == "OpenEngage Team" and "organization_data" in st.session_state and st.session_state.organization_data:
            # Try to extract organization name from domain or URL
            org_data = st.session_state.organization_data
            domain = org_data.get("Domain", "")
            if domain:
                org_name = domain.split(" ")[0]  # Use first word of domain
            else:
                # Extract name from URL
                org_url = org_data.get("url", "")
                if org_url:
                    from urllib.parse import urlparse
                    parsed_url = urlparse(org_url)
                    netloc = parsed_url.netloc or parsed_url.path
                    # Remove www. and .com/.org/etc.
                    org_name = netloc.replace("www.", "").split(".")[0].capitalize()

        st.session_state.communication_settings = {
            "sender_name": org_name,
            "style": "friendly",
            "length": "100-150 words",
            "utm_source": "email",
            "utm_medium": "email",
            "utm_campaign": "product_launch",
            "utm_content": "initial",
            "organization_url": st.session_state.organization_url if "organization_url" in st.session_state else ""
        }

    # Initialize selected channels if not already done
    if "selected_channels" not in st.session_state:
        st.session_state.selected_channels = {
            "Email": True,
            "WhatsApp": False
        }

    # Load feature toggles from JSON file
    if 'feature_toggles' not in st.session_state:
        st.session_state.feature_toggles = load_feature_toggles()

    # Make sure product_priority view is initialized
    if "show_product_priority" not in st.session_state:
        st.session_state.show_product_priority = False

    # Add reset_other_views function to session state
    if not hasattr(st.session_state, 'reset_other_views'):
        def reset_other_views(keep_active=None):
            views = {
                'show_user_journey': False,
                'show_stage_selection': False,
                'show_template_verification': False,
                'show_whatsapp_template_addition': False,
                'show_journey_builder': False,
                'show_mass_generator': False,
                'show_personalized_email_generator': False,
                'show_funnel_view': False,
                'show_templates_generator': False,
                'show_analytics': False,
                'show_analytics_story': False,
                'show_trigger_points': False,
                'show_competitive_analysis': False,
                'show_sending_config': False,
                'show_editor': False,
                'show_org_editor': False,
                'show_product_editor': False,
                'show_product_priority': False,
                'show_trend_analysis': False,
                'show_popup_configuration': False,
                'show_image_pool': False,
                'show_pruning_config': False,
                'show_channel_selection': False
            }

            # Set all views to False except the one to keep active
            for view, _ in views.items():
                if keep_active and view == keep_active:
                    setattr(st.session_state, view, True)
                else:
                    setattr(st.session_state, view, False)
        st.session_state.reset_other_views = reset_other_views

    # Sidebar
    with st.sidebar:
        st.image("data/Openengage logo.png", width=200)
        st.write("---")

        if st.session_state.show_dashboard:
            # Show user info - Check if current_user exists and has a name
            if hasattr(st.session_state, 'current_user') and st.session_state.current_user and 'name' in st.session_state.current_user:
                st.write(f"Welcome, {st.session_state.current_user['name']}!")
            else:
                st.write("Welcome!")
            st.write("---")

            # Navigation and settings
            if st.session_state.get('settings_open', False):
                # Settings gear at top when menu is open
                if st.button("⚙️", key="settings_button", help="Toggle Settings", type="secondary"):
                    st.session_state.settings_open = False
                    st.rerun()

                # Only show settings when settings menu is open
                with st.container():
                    st.markdown('<div class="settings-menu">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-title">', unsafe_allow_html=True)
                    st.markdown('<h3>Settings</h3>', unsafe_allow_html=True)
                    st.markdown('</div>', unsafe_allow_html=True)

                    # Configuration section
                    st.markdown('<div class="settings-section">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-header">Configuration</div>', unsafe_allow_html=True)
                    new_value = st.toggle("Product Setup", value=st.session_state.feature_toggles['product_setup'])
                    if new_value != st.session_state.feature_toggles['product_setup']:
                        st.session_state.feature_toggles['product_setup'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    new_value = st.toggle("Product Priority", value=st.session_state.feature_toggles['product_priority'])
                    if new_value != st.session_state.feature_toggles['product_priority']:
                        st.session_state.feature_toggles['product_priority'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    
                    # Add Pruning Configuration toggle (moved from Analytics section)
                    new_value = st.toggle("Pruning Configuration", value=st.session_state.feature_toggles.get('pruning_config', True))
                    if new_value != st.session_state.feature_toggles.get('pruning_config', True):
                        st.session_state.feature_toggles['pruning_config'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                        
                    # Add Channel Selection toggle
                    new_value = st.toggle("Channel Selection", value=st.session_state.feature_toggles.get('channel_selection', True))
                    if new_value != st.session_state.feature_toggles.get('channel_selection', True):
                        st.session_state.feature_toggles['channel_selection'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                        
                    # Add Image Pool toggle
                    new_value = st.toggle("Image Pool", value=st.session_state.feature_toggles.get('image_pool', True))
                    if new_value != st.session_state.feature_toggles.get('image_pool', True):
                        st.session_state.feature_toggles['image_pool'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    st.markdown('</div>', unsafe_allow_html=True)

                    # Templates section
                    st.markdown('<div class="settings-section">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-header">Templates</div>', unsafe_allow_html=True)
                    new_value = st.toggle("Template Generator", value=st.session_state.feature_toggles['template_generator'])
                    if new_value != st.session_state.feature_toggles['template_generator']:
                        st.session_state.feature_toggles['template_generator'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    new_value = st.toggle("Template Verification", value=st.session_state.feature_toggles['template_verification'])
                    if new_value != st.session_state.feature_toggles['template_verification']:
                        st.session_state.feature_toggles['template_verification'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    st.markdown('</div>', unsafe_allow_html=True)

                    # Journey section
                    st.markdown('<div class="settings-section">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-header">Journey</div>', unsafe_allow_html=True)
                    new_value = st.toggle("Journey Builder", value=st.session_state.feature_toggles['journey_builder'])
                    if new_value != st.session_state.feature_toggles['journey_builder']:
                        st.session_state.feature_toggles['journey_builder'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    new_value = st.toggle("Trigger Points", value=st.session_state.feature_toggles['trigger_points'])
                    if new_value != st.session_state.feature_toggles['trigger_points']:
                        st.session_state.feature_toggles['trigger_points'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    st.markdown('</div>', unsafe_allow_html=True)

                    # Campaigns section
                    st.markdown('<div class="settings-section">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-header">Campaigns</div>', unsafe_allow_html=True)
                    new_value = st.toggle("Mass Campaign", value=st.session_state.feature_toggles['mass_campaign'])
                    if new_value != st.session_state.feature_toggles['mass_campaign']:
                        st.session_state.feature_toggles['mass_campaign'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                        
                    # Add Personalized Email Generator toggle
                    new_value = st.toggle("Personalized Email Generator", value=st.session_state.feature_toggles.get('personalized_email', True))
                    if new_value != st.session_state.feature_toggles.get('personalized_email', True):
                        st.session_state.feature_toggles['personalized_email'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                        
                    # Add WhatsApp Template toggle
                    new_value = st.toggle("WhatsApp Template Addition", value=st.session_state.feature_toggles.get('whatsapp_template', True))
                    if new_value != st.session_state.feature_toggles.get('whatsapp_template', True):
                        st.session_state.feature_toggles['whatsapp_template'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)

                    new_value = st.toggle("Sending Channel Configuration", value=st.session_state.feature_toggles.get('sending_config', False))
                    if new_value != st.session_state.feature_toggles.get('sending_config', False):
                        st.session_state.feature_toggles['sending_config'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    st.markdown('</div>', unsafe_allow_html=True)

                    # Analytics section
                    st.markdown('<div class="settings-section">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-header">Analytics</div>', unsafe_allow_html=True)
                    new_value = st.toggle("Analytics Dashboard", value=st.session_state.feature_toggles['analytics_dashboard'])
                    if new_value != st.session_state.feature_toggles['analytics_dashboard']:
                        st.session_state.feature_toggles['analytics_dashboard'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)

                    new_value = st.toggle("Analytics Story", value=st.session_state.feature_toggles.get('analytics_story', True))
                    if new_value != st.session_state.feature_toggles.get('analytics_story', True):
                        st.session_state.feature_toggles['analytics_story'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)

                    # Add toggle for hiding CSV upload in Analytics Story
                    new_value = st.toggle("Hide CSV Upload in Analytics Story", value=st.session_state.feature_toggles.get('hide_csv_upload_analytics_story', False))
                    if new_value != st.session_state.feature_toggles.get('hide_csv_upload_analytics_story', False):
                        st.session_state.feature_toggles['hide_csv_upload_analytics_story'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                        
                    # Add Insights Chatbot toggle
                    new_value = st.toggle("Insights Chatbot", value=st.session_state.feature_toggles.get('insights_chatbot', True))
                    if new_value != st.session_state.feature_toggles.get('insights_chatbot', True):
                        st.session_state.feature_toggles['insights_chatbot'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)

                    new_value = st.toggle("Competitive Analysis", value=st.session_state.feature_toggles['competitive_analysis'])
                    if new_value != st.session_state.feature_toggles['competitive_analysis']:
                        st.session_state.feature_toggles['competitive_analysis'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    new_value = st.toggle("Trend Analysis", value=st.session_state.feature_toggles.get('trend_analysis', True))
                    if new_value != st.session_state.feature_toggles.get('trend_analysis', True):
                        st.session_state.feature_toggles['trend_analysis'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    st.markdown('</div>', unsafe_allow_html=True)

                    # Organization section
                    st.markdown('<div class="settings-section">', unsafe_allow_html=True)
                    st.markdown('<div class="settings-header">Organization</div>', unsafe_allow_html=True)
                    new_value = st.toggle("Organization Products Visibility", value=st.session_state.feature_toggles.get('org_products_visibility', True))
                    if new_value != st.session_state.feature_toggles.get('org_products_visibility', True):
                        st.session_state.feature_toggles['org_products_visibility'] = new_value
                        save_feature_toggles(st.session_state.feature_toggles)
                    st.markdown('</div>', unsafe_allow_html=True)

                    st.markdown('</div>', unsafe_allow_html=True)
            else:
                # Show navigation sections when settings is closed
                st.markdown("### Navigation")
                # Navigation buttons organized in expandable sections
                with st.expander("⚙️ Configuration"):
                    if st.session_state.feature_toggles['product_setup']:
                        if st.button("Organization Setup", key="btn_org_setup", type="secondary", use_container_width=True):
                            # Try to load existing organization data using the utility function
                            from utils.file_utils import load_organization_data

                            # Check if we have data for the current organization
                            if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                                org_url = st.session_state.organization_url

                                # Load organization data for this URL
                                org_data = load_organization_data(org_url)

                                if org_data:
                                    # Organization data exists, use it
                                    st.session_state.organization_data = org_data
                                    st.session_state.analyzing_organization = False
                                else:
                                    # Organization not found, set flag for analysis
                                    st.session_state.analyzing_organization = True
                            else:
                                # No organization URL, set flag for analysis
                                st.session_state.analyzing_organization = True

                            st.session_state.show_org_editor = True
                            st.session_state.reset_other_views('show_org_editor')
                            st.rerun()

                        if st.button("Product Setup", key="btn_product_setup", type="secondary", use_container_width=True):
                            # Check if organization is set up first
                            if not hasattr(st.session_state, 'organization_data') or not st.session_state.organization_data:
                                st.warning("Please complete Organization Setup first.")
                                st.session_state.show_org_editor = True
                                st.session_state.reset_other_views('show_org_editor')
                            else:
                                st.session_state.show_product_editor = True
                                st.session_state.reset_other_views('show_product_editor')
                            st.rerun()

                    if st.session_state.feature_toggles['product_priority']:
                        if st.button("Product Priority", key="btn_product_priority", type="secondary", use_container_width=True):
                            st.session_state.show_product_priority = True
                            st.session_state.reset_other_views('show_product_priority')
                            st.rerun()

                    # Add Pruning Config button
                    # Only show Pruning Configuration if it's enabled in feature toggles
                    if st.session_state.feature_toggles.get('pruning_config', False):
                        if st.button("Pruning Configuration", key="btn_pruning_config", type="secondary", use_container_width=True):
                            st.session_state.show_pruning_config = True
                            st.session_state.reset_other_views('show_pruning_config')
                            st.rerun()
                    
                    # Add Channel Selection button (only when enabled)
                    if st.session_state.feature_toggles.get('channel_selection', False):
                        if st.button("Channel Selection", key="btn_channel_selection", type="secondary", use_container_width=True):
                            st.session_state.show_channel_selection = True
                            st.session_state.reset_other_views('show_channel_selection')
                            st.rerun()
                            
                    # Add Popup Configuration button (only when enabled)
                    if st.session_state.feature_toggles.get('popup_configuration', False):
                        if st.button("Popup Configuration", key="btn_popup_configuration", type="secondary", use_container_width=True):
                            st.session_state.show_popup_configuration = True
                            st.session_state.reset_other_views('show_popup_configuration')
                            st.rerun()
                            
                    # Add Image Pool button (only when enabled)
                    if st.session_state.feature_toggles.get('image_pool', False):
                        if st.button("Image Pool", key="btn_image_pool", type="secondary", use_container_width=True):
                            st.session_state.show_image_pool = True
                            st.session_state.reset_other_views('show_image_pool')
                            st.rerun()

                    # Moved from Campaigns section
                    if st.session_state.feature_toggles.get('sending_config', False):
                        if st.button("Sending Channel Configuration", key="btn_sending_config", type="secondary", use_container_width=True):
                            st.session_state.show_sending_config = True
                            st.session_state.reset_other_views('show_sending_config')
                            st.rerun()

                    # Moved from Journey Management section
                    if st.session_state.feature_toggles['trigger_points']:
                        if st.button("Trigger Points", key="btn_trigger_points", type="secondary", use_container_width=True):
                            st.session_state.show_trigger_points = True
                            st.session_state.reset_other_views('show_trigger_points')
                            st.rerun()

                with st.expander("📝 Templates"):
                    if st.session_state.feature_toggles['template_generator']:
                        if st.button("Templates Generator", key="btn_templates_generator", type="secondary", use_container_width=True):
                            st.session_state.show_templates_generator = True
                            st.session_state.reset_other_views('show_templates_generator')
                            st.rerun()

                    if st.session_state.feature_toggles['template_verification']:
                        if st.button("Template Verification", key="btn_template_verification", type="secondary", use_container_width=True):
                            st.session_state.show_template_verification = True
                            st.session_state.reset_other_views('show_template_verification')
                            st.rerun()

                    # Add WhatsApp Template Addition button
                    if st.session_state.feature_toggles.get('whatsapp_template', True):
                        if st.button("WhatsApp Template Addition", key="btn_whatsapp_template", type="secondary", use_container_width=True):
                            st.session_state.show_whatsapp_template_addition = True
                            st.session_state.reset_other_views('show_whatsapp_template_addition')
                            st.rerun()

                # Journey Management section removed as items were moved to other sections

                with st.expander("📧 Campaigns"):
                    if st.session_state.feature_toggles['mass_campaign']:
                        if st.button("Mass Campaign Generator", key="btn_mass_campaign", type="secondary", use_container_width=True):
                            st.session_state.show_mass_generator = True
                            st.session_state.reset_other_views('show_mass_generator')
                            st.rerun()

                        if st.session_state.feature_toggles.get('personalized_email', True):
                            if st.button("Personalized Email Generator", key="btn_personalized_email", type="secondary", use_container_width=True):
                                st.session_state.show_personalized_email_generator = True
                                st.session_state.reset_other_views('show_personalized_email_generator')
                                st.rerun()

                        # Add Funnel View button
                        if st.button("Funnel View", key="btn_funnel_view", type="secondary", use_container_width=True):
                            st.session_state.show_funnel_view = True
                            st.session_state.reset_other_views('show_funnel_view')
                            st.rerun()

                    # Moved from Journey Management section
                    if st.session_state.feature_toggles['journey_builder']:
                        if st.button("Journey Builder", key="btn_journey_builder", type="secondary", use_container_width=True):
                            st.session_state.show_journey_builder = True
                            st.session_state.reset_other_views('show_journey_builder')
                            st.rerun()

                with st.expander("📈 Analytics & Insights"):
                    if st.session_state.feature_toggles['analytics_dashboard']:
                        if st.button("Analytics Dashboard", key="btn_analytics", type="secondary", use_container_width=True):
                            st.session_state.show_analytics = True
                            st.session_state.reset_other_views('show_analytics')
                            st.rerun()

                    # Add Analytics Story button
                    if st.session_state.feature_toggles.get('analytics_story', True):
                        if st.button("Analytics Story", key="btn_analytics_story", type="secondary", use_container_width=True):
                            st.session_state.show_analytics_story = True
                            st.session_state.reset_other_views('show_analytics_story')
                            st.rerun()

                    if st.session_state.feature_toggles['competitive_analysis']:
                        if st.button("Competitive Analysis", key="btn_competitive_analysis", type="secondary", use_container_width=True):
                            st.session_state.show_competitive_analysis = True
                            st.session_state.reset_other_views('show_competitive_analysis')
                            st.rerun()

                    # Add Trend Analysis button
                    if st.session_state.feature_toggles.get('trend_analysis', True):
                        if st.button("Trend Analysis", key="btn_trend_analysis", type="secondary", use_container_width=True):
                            st.session_state.show_trend_analysis = True
                            st.session_state.reset_other_views('show_trend_analysis')
                            st.rerun()
                            
                    # Add Insights Chatbot button
                    if st.session_state.feature_toggles.get('insights_chatbot', True):
                        if st.button("Insights Chatbot", key="btn_insights_chatbot", type="secondary", use_container_width=True):
                            st.session_state.show_insights_chatbot = True
                            st.session_state.reset_other_views('show_insights_chatbot')
                            st.rerun()

                    # Pruning Configuration button removed from here (only kept in Configuration section)

                # Settings gear at bottom when menu is closed
                st.write("---")
                if st.button("⚙️", key="settings_button", help="Toggle Settings", type="secondary"):
                    st.session_state.settings_open = True
                    st.rerun()

                st.write("---")
                if st.button("🚪 Logout", key="btn_logout", type="secondary", use_container_width=True):
                    # Clear all session state properly
                    st.session_state.is_logged_in = False
                    st.session_state.current_user = None
                    st.session_state.organization_url = ""
                    st.session_state.organization_name = "OpenEngage Team"
                    # Reset view states
                    for view in ['show_dashboard', 'show_templates_generator', 'show_template_verification',
                        'show_journey_builder', 'show_analytics', 'show_analytics_story', 'show_mass_email_generator',
                        'show_personalized_email_generator', 'show_funnel_view', 'show_editor', 'show_competitive_analysis',
                        'show_sending_configuration', 'show_product_priority', 'show_popup_configuration', 'show_image_pool',
                        'show_pruning_config']:
                        if view in st.session_state:
                            st.session_state[view] = False
                    st.rerun()
    # Main content
    if not st.session_state.is_logged_in:
        display_login_signup()
    elif not st.session_state.show_dashboard:
        display_login_signup()
    else:
        # Check if organization setup should be shown first (prioritize this over other views)
        if st.session_state.show_org_editor:
            # For organization setup, completely replace the layout
            # Import and display organization editor
            from ui.organization import display_organization_editor
            display_organization_editor()
        elif st.session_state.show_template_verification:
            display_template_verification()
        elif st.session_state.show_whatsapp_template_addition:
            display_whatsapp_template_addition()
        elif st.session_state.show_journey_builder:
            display_journey_builder()
        elif st.session_state.show_templates_generator:
            display_templates_generator()
        elif st.session_state.show_mass_generator:
            display_mass_email_generator()
        elif st.session_state.show_personalized_email_generator:
            display_personalized_email_generator()
        elif st.session_state.show_funnel_view:
            display_funnel_view()
        elif st.session_state.show_trigger_points:
            display_trigger_points()
        elif st.session_state.show_analytics:
            display_analytics()
        elif st.session_state.show_analytics_story:
            display_analytics_story()
        elif st.session_state.show_competitive_analysis:
            display_competitive_analysis()
        elif st.session_state.show_sending_config:
            display_sending_configuration()
        elif st.session_state.show_product_priority:
            display_product_priority()
        elif st.session_state.show_trend_analysis:
            display_trend_analysis()
        elif st.session_state.show_popup_configuration:
            display_popup_configuration()
            
        # Display Image Pool UI
        elif st.session_state.show_image_pool:
            display_image_pool()
        # Display Pruning Configuration UI
        elif st.session_state.show_pruning_config:
            render_pruning_config()
        # Display Channel Selection UI
        elif st.session_state.show_channel_selection:
            display_channel_selection()
        # Display Insights Chatbot UI
        elif st.session_state.show_insights_chatbot:
            display_insights_chatbot()
        else:
            # Check API key first
            check_api_key()

            st.markdown("""
                <style>
                    .block-container {
                        padding-top: 1rem;
                    }
                </style>
            """, unsafe_allow_html=True)

            # Create a two-column layout with custom widths
            chat_col, editor_col = st.columns([0.6, 0.4])

            # Chat interface in the left column
            with chat_col:
                st.markdown("""
                    <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;'>
                        <h2 style='color: #FFFFFF; margin: 0; font-size: 1.2rem;'>OpenEngage Chat</h2>
                        <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                            I'm here to help you with your marketing automation needs.
                        </p>
                    </div>
                """, unsafe_allow_html=True)

                # Check if there are any products and prompt if none found

                # Display chat messages from history on app rerun
                for message in st.session_state.messages:
                    if message["role"] == "assistant":
                        with st.chat_message(message["role"], avatar="data/OEShortLogo.png"):
                            st.markdown(message["content"])
                    else:
                        with st.chat_message(message["role"], avatar="👤"):
                            st.markdown(message["content"])

                # Accept user input
                if prompt := st.chat_input("Type your message here"):
                    # Add user message to chat history
                    st.session_state.messages.append({"role": "user", "content": prompt})
                    # Display user message in chat message container
                    with st.chat_message("user", avatar="👤"):
                        st.markdown(prompt)

                    # Check if this is the first message
                    is_first_message = len([msg for msg in st.session_state.messages if msg["role"] == "user"]) == 1

                    # Check if the input looks like a URL
                    url_pattern = r'https?://[^\s,]+'
                    is_url_input = bool(re.findall(url_pattern, prompt))

                    # Check if it's a simple greeting
                    greeting_pattern = r'^(hi|hello|hey|greetings|howdy|hi there|hello there)[\s!.]*$'
                    is_greeting = bool(re.match(greeting_pattern, prompt.lower()))

                    # Display assistant response in chat message container
                    with st.chat_message("assistant", avatar="data/OEShortLogo.png"):
                        with st.spinner("Thinking..."):
                            try:
                                # If it's the first message and a greeting, add a welcome message
                                if is_first_message and is_greeting:
                                    from datetime import datetime
                                    current_date = datetime.now().strftime("%A, %B %d, %Y")

                                    # Create a friendly welcome message
                                    welcome_message = f"Hello! Welcome to OpenEngage. Today is {current_date}. How can I assist you today? If you'd like to analyze products, feel free to share their URLs with me."
                                    st.markdown(welcome_message)
                                    st.session_state.messages.append({"role": "assistant", "content": welcome_message})
                                else:
                                    # Process the user request normally
                                    response = st.session_state.crew.process_user_request(prompt)
                                    if response is None:
                                        response = "I'm sorry, I couldn't process your request. Please try again or ask a different question."
                                    st.markdown(response)
                                    st.session_state.messages.append({"role": "assistant", "content": response})
                            except Exception as e:
                                error_message = f"I'm sorry, I encountered an error: {str(e)}"
                                st.markdown(error_message)
                                st.session_state.messages.append({"role": "assistant", "content": error_message})

                        # Only update product data if a URL was provided and analyzed
                        if is_url_input and hasattr(st.session_state.crew, 'products_list') and st.session_state.crew.products_list:
                            st.session_state.product_data = st.session_state.crew.products_list
                            st.session_state.show_editor = True
                            st.rerun()

                # Organization analysis is now handled in organization.py

                # Show product options if available
                if hasattr(st.session_state, 'show_product_options') and st.session_state.show_product_options:
                    st.markdown("---")
                    st.markdown("### Product Configuration")

                    # Get current organization URL from session state
                    org_url = st.session_state.organization_url if hasattr(st.session_state, 'organization_url') else None

                    if not org_url:
                        st.error("Organization URL not found. Please complete organization setup first.")
                    else:
                        # Make sure we're only showing products for the current organization
                        if hasattr(st.session_state, 'existing_products'):
                            # Check if organization products visibility is enabled
                            org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

                            if org_filter_enabled:
                                # Filter products for current organization
                                current_org_products = [p for p in st.session_state.existing_products
                                                        if p.get("Company_URL", "") == org_url or
                                                        p.get("organization_url", "") == org_url]

                                # Update existing_products with filtered list
                                st.session_state.existing_products = current_org_products
                            else:
                                # Even if filter is disabled, still filter by organization URL for this specific section
                                # as it's showing products for a specific organization
                                current_org_products = [p for p in st.session_state.existing_products
                                                        if p.get("Company_URL", "") == org_url or
                                                        p.get("organization_url", "") == org_url]

                                # Update existing_products with filtered list
                                st.session_state.existing_products = current_org_products

                        # Show existing products
                        if not st.session_state.existing_products:
                            st.info("No products found for your organization. Please add products.")
                            st.session_state.show_product_options = False
                            st.rerun()
                        else:
                            st.write("We found the following products configured for your organization:")
                            for i, product in enumerate(st.session_state.existing_products, 1):
                                st.write(f"{i}. {product['Product_Name']}")

                            # Ask user what they want to do
                            action = st.radio(
                                "What would you like to do?",
                                ["Update existing product configuration", "Add new product"],
                                key="product_action"
                            )

                            if action == "Update existing product configuration":
                                # Show product selection
                                selected_product = st.selectbox(
                                    "Select a product to update:",
                                    st.session_state.existing_products,
                                    format_func=lambda x: x['Product_Name']
                                )

                                if st.button("Edit Selected Product", key="btn_edit_product"):
                                    # Set the product data for editing
                                    st.session_state.product_data = selected_product
                                    st.session_state.crew.product_data = selected_product
                                    st.session_state.show_editor = True
                                    st.session_state.show_product_options = False
                                    st.rerun()
                            else:
                                st.write("Please enter the URLs of the new products you'd like to add (multiple URLs can be separated by commas) or ask any other questions:")
                                if prompt := st.chat_input("Enter product URLs (comma-separated) or ask a question"):
                                    # Check if the input looks like a URL
                                    url_pattern = r'https?://[^\s,]+'
                                    is_url_input = bool(re.findall(url_pattern, prompt))

                                    # Check if it's a simple greeting
                                    greeting_pattern = r'^(hi|hello|hey|greetings|howdy|hi there|hello there)[\s!.]*$'
                                    is_greeting = bool(re.match(greeting_pattern, prompt.lower()))

                                    with st.spinner("Processing your input..."):
                                        try:
                                            # If it's a greeting, provide a direct friendly response
                                            if is_greeting:
                                                from datetime import datetime
                                                current_date = datetime.now().strftime("%A, %B %d, %Y")

                                                # Create a friendly welcome message
                                                welcome_message = f"Hello! Welcome to OpenEngage. Today is {current_date}. How can I assist you today? If you'd like to analyze products, feel free to share their URLs with me."
                                                st.markdown(welcome_message)
                                            else:
                                                # Process the user request normally
                                                response = st.session_state.crew.process_user_request(prompt)
                                                if response is None:
                                                    response = "I'm sorry, I couldn't process your request. Please try again or ask a different question."
                                                st.markdown(response)

                                            # Only proceed with product setup if a URL was provided and analyzed
                                            if is_url_input and hasattr(st.session_state.crew, 'products_list') and st.session_state.crew.products_list:
                                                # Get the latest product
                                                new_product = st.session_state.crew.products_list[-1]
                                                # Set it for editing
                                                st.session_state.product_data = new_product
                                                st.session_state.crew.product_data = new_product
                                                st.session_state.show_editor = True
                                                st.session_state.show_product_options = False
                                                st.rerun()
                                            elif not is_url_input:
                                                # For non-URL inputs, just keep the chat interface open
                                                pass
                                            else:
                                                st.session_state.show_product_options = False
                                                st.rerun()
                                        except Exception as e:
                                            error_message = f"I'm sorry, I encountered an error: {str(e)}"
                                            st.markdown(error_message)

            # For normal views, use the layout with chat and editor columns
            # Editor in the right column
            with editor_col:
                # Show product editor if selected
                if st.session_state.show_product_editor:
                    # Import here to avoid circular imports
                    from ui.product import display_product_editor

                    # Check if we have product data
                    if not hasattr(st.session_state, 'product_data') or st.session_state.product_data is None:
                        # Try to get product data from existing_products
                        if hasattr(st.session_state, 'existing_products') and st.session_state.existing_products:
                            st.session_state.product_data = st.session_state.existing_products[0]
                            if hasattr(st.session_state.crew, 'product_data'):
                                st.session_state.crew.product_data = st.session_state.existing_products[0]

                    # Now display the product editor
                    display_product_editor()

                # Show legacy editor if needed (for backward compatibility)
                elif st.session_state.show_editor and st.session_state.product_data:
                    # Import here to avoid circular imports
                    from ui.product import display_product_editor
                    display_product_editor()

                # Show product priority management
                elif hasattr(st.session_state, 'show_product_priority') and st.session_state.show_product_priority:
                    display_product_priority()

                # Organization update option is now handled in organization.py

if __name__ == "__main__":
    main()
