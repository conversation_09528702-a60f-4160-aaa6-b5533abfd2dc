"""
Data Integration Module for OpenEngage.

This module integrates data retrieval functionality with data processing capabilities.
It serves as a bridge between data sources and the user data processor.
"""
import os
import pandas as pd
import logging
import boto3
import psycopg2 as pcg
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from dotenv import load_dotenv
import datetime
from dateutil.relativedelta import relativedelta
from pathlib import Path
import time
from typing import Optional,Callable


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class DataIntegration:
    """
    Integrates data retrieval with data processing for OpenEngage.
    
    This class provides methods to fetch data from various sources and prepare it
    for processing by the UserDataProcessor.
    """
    
    def __init__(self, base_path='Sample Data For Mass Generation'):
        """
        Initialize the DataIntegration with necessary configuration.
        
        Args:
            base_path (str): Base path for data files
        """
        self.base_path = base_path
        
        # Ensure base directory exists
        Path(self.base_path).mkdir(parents=True, exist_ok=True)
        
        # Define file paths
        self.target_audience_path = f'{self.base_path}/TargetAudience.csv'
        self.unsubscribers_path = f'{self.base_path}/Unsubscribers.csv'
        self.brahma_data_path = f'{self.base_path}/brahma_data.csv'
        
        # Buyer files
        self.bb_buyers_path = f'{self.base_path}/BBBuyers.csv'
        self.genai_buyers_path = f'{self.base_path}/GenAI Buyers.csv'
        self.agenticai_buyers_path = f'{self.base_path}/AgenticAI Buyers.csv'
        
        # Lead files
        self.bb_leads_path = f'{self.base_path}/BB_Leads.csv'
        self.genai_leads_path = f'{self.base_path}/Pinnacle_Leads.csv'
        self.agenticai_leads_path = f'{self.base_path}/AgenticAI_Leads.csv'
        
        # Initialize empty DataFrames for files that might not exist yet
        self._initialize_empty_files()
    
    def _initialize_empty_files(self):
        """Initialize empty files with correct structure if they don't exist."""
        # Unsubscribers structure
        if not os.path.exists(self.unsubscribers_path):
            pd.DataFrame(columns=['created_at', 'email', 'Promotional']).to_csv(self.unsubscribers_path, index=False)
            logger.info(f"Created empty file: {self.unsubscribers_path}")
        
        # Buyers files structure
        if not os.path.exists(self.bb_buyers_path):
            pd.DataFrame(columns=['Date', 'Email_ID', 'Product']).to_csv(self.bb_buyers_path, index=False)
            logger.info(f"Created empty file: {self.bb_buyers_path}")
        
        if not os.path.exists(self.genai_buyers_path):
            pd.DataFrame(columns=['date_placed', 'email', 'Title']).to_csv(self.genai_buyers_path, index=False)
            logger.info(f"Created empty file: {self.genai_buyers_path}")
        
        if not os.path.exists(self.agenticai_buyers_path):
            pd.DataFrame(columns=['Date', 'Email_ID', 'Product']).to_csv(self.agenticai_buyers_path, index=False)
            logger.info(f"Created empty file: {self.agenticai_buyers_path}")
        
        # Leads files structure
        leads_columns = ['id', 'created_at', 'name', 'email', 'country_code', 'phone', 
                         'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 
                         'utm_content', 'additional_data']
        
        if not os.path.exists(self.bb_leads_path):
            pd.DataFrame(columns=leads_columns).to_csv(self.bb_leads_path, index=False)
            logger.info(f"Created empty file: {self.bb_leads_path}")
        
        if not os.path.exists(self.genai_leads_path):
            pd.DataFrame(columns=leads_columns).to_csv(self.genai_leads_path, index=False)
            logger.info(f"Created empty file: {self.genai_leads_path}")
        
        if not os.path.exists(self.agenticai_leads_path):
            pd.DataFrame(columns=leads_columns).to_csv(self.agenticai_leads_path, index=False)
            logger.info(f"Created empty file: {self.agenticai_leads_path}")
    
    def import_from_sheets(self, sheet_name, worksheet_name, method='records'):
        """
        Import data from Google Sheets.
        
        Args:
            sheet_name (str): Name of the Google Sheet
            worksheet_name (str): Name of the worksheet
            method (str): Method to get data ('records' or 'values')
            
        Returns:
            pd.DataFrame: DataFrame containing the sheet data
        """
        try:
            scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
            creds = ServiceAccountCredentials.from_json_keyfile_name('config/client_secret.json', scope)
            client = gspread.authorize(creds)
            
            sheet = client.open(sheet_name)
            worksheet = sheet.worksheet(worksheet_name)
            
            if method == 'values':
                list_of_hashes = worksheet.get_all_values()
            else:
                list_of_hashes = worksheet.get_all_records()
            
            logger.info(f"Imported data from Google Sheet: {sheet_name}, worksheet: {worksheet_name}")
            return pd.DataFrame(list_of_hashes)
        
        except Exception as e:
            logger.error(f"Error importing from Google Sheets: {str(e)}")
            return pd.DataFrame()
    
    def get_user_data_brahma(self, query, database):
        """
        Get user data from Brahma (AWS Athena).
        
        Args:
            query (str): SQL query to execute
            database (str): Database name
            
        Returns:
            pd.DataFrame: DataFrame containing the query results
        """
        try:
            # Get AWS credentials from environment variables
            aws_access_key = os.getenv("AWS_ACCESS_KEY")
            aws_secret_key = os.getenv("AWS_SECRET_KEY")
            aws_region = os.getenv("AWS_REGION")
            
            if not all([aws_access_key, aws_secret_key, aws_region]):
                logger.error("AWS credentials not found in environment variables")
                return pd.DataFrame()
            
            # Create Athena client
            athena_client = boto3.client(
                "athena",
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key,
                region_name=aws_region,
            )
            logger.info("Brahma connection established")
            
            # Execute query
            query_response = athena_client.start_query_execution(
                QueryString=query,
                QueryExecutionContext={"Database": database},
                ResultConfiguration={
                    "OutputLocation": "s3://clickstream-athena-query/",
                },
            )
            logger.info("Query sent to Brahma")
            
            # Wait for query to complete
            while True:
                try:
                    athena_client.get_query_results(
                        QueryExecutionId=query_response["QueryExecutionId"]
                    )
                    break
                except Exception as err:
                    if "not yet finished" in str(err):
                        time.sleep(0.001)
                    else:
                        raise err
            
            # Download results from S3
            s3_bucket_name = "clickstream-athena-query"
            temp_file_location = "athenaqueryresult.csv"
            
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key,
                region_name=aws_region
            )
            
            s3_client.download_file(
                s3_bucket_name,
                f"{query_response['QueryExecutionId']}.csv",
                temp_file_location,
            )
            logger.info("Data obtained from Brahma")
            
            # Read and process the data
            df_query = pd.read_csv(temp_file_location)
            os.remove(temp_file_location)
            df_query = df_query.drop_duplicates()
            
            return df_query
        
        except Exception as e:
            logger.error(f"Error getting data from Brahma: {str(e)}")
            return pd.DataFrame()
    
    def get_signup_data(self):
        """
        Get signup data from the database.
        
        Returns:
            pd.DataFrame: DataFrame containing signup data
        """
        try:
            connection_string = os.getenv("SIGNUP_DATA_CONNECTION_LINK")
            if not connection_string:
                logger.error("Signup data connection string not found in environment variables")
                return pd.DataFrame()
            
            # Connect to database
            identity_connection = pcg.connect(connection_string)
            identity_cursor = identity_connection.cursor()
            
            # Execute query
            identity_cursor.execute("SELECT date_joined, first_name, email, signup_source_name, signup_source_url FROM users_user;")
            rows = identity_cursor.fetchall()
            identity_connection.close()
            
            # Process data
            signup_data = pd.DataFrame(rows)
            signup_data.columns = ["date_joined", "first_name", "email", "signup_source_name", "signup_source_url"]
            signup_data["email"] = signup_data["email"].str.lower().str.strip()
            signup_data["date_joined"] = pd.to_datetime(signup_data["date_joined"], format='mixed').dt.tz_localize(None)
            signup_data = signup_data.sort_values(by="date_joined")
            signup_data = signup_data.drop_duplicates(subset=["email"])
            
            logger.info(f"Fetched signup data for {len(signup_data)} users")
            return signup_data
        
        except Exception as e:
            logger.error(f"Error getting signup data: {str(e)}")
            return pd.DataFrame(columns=["date_joined", "first_name", "email", "signup_source_name", "signup_source_url"])
    
    def update_target_audience(self, signup_data: pd.DataFrame):
        """
        Update target audience data by mapping first_name from signup data.
        
        This method specifically maps the first_name field from signup data to 
        target audience records based on matching email addresses. It ensures 
        that the target audience has proper names associated with each user.
        
        Args:
            signup_data (pd.DataFrame): DataFrame containing signup data with 
                                       columns including 'first_name' and 'email'
        
        Returns:
            None: Writes updated data to target_audience_path
        """
        try:
            # Check if signup_data is empty
            if signup_data.empty:
                logger.warning("No signup data provided, target audience not updated")
                return
            
            # Essential columns for mapping
            required_columns = ["email", "first_name"]
            missing_columns = [col for col in required_columns if col not in signup_data.columns]
            
            if missing_columns:
                logger.error(f"Missing required columns in signup data: {missing_columns}")
                return
                
            # Create mapping dictionary from email to first_name
            name_mapping = dict(zip(signup_data["email"].str.lower().str.strip(), signup_data["first_name"]))
            logger.info(f"Created name mapping for {len(name_mapping)} users from signup data")
            
            # Check if target audience file exists
            if os.path.exists(self.target_audience_path):
                try:
                    # Read existing target audience data
                    target_audience = pd.read_csv(self.target_audience_path)
                    logger.info(f"Loaded existing target audience with {len(target_audience)} records")
                    
                    # Check if user_email column exists, if not rename or create it
                    if "email" in target_audience.columns and "user_email" not in target_audience.columns:
                        target_audience.rename(columns={"email": "user_email"}, inplace=True)
                    elif "user_email" not in target_audience.columns:
                        # No email column at all, create one
                        target_audience["user_email"] = ""
                        logger.warning("No email column found in target audience, created empty user_email column")
                    
                    # Ensure first_name column exists
                    if "first_name" not in target_audience.columns:
                        target_audience["first_name"] = ""
                    
                    # Track how many names will be updated
                    update_count = 0
                    
                    # Map first_name to target audience based on email
                    for index, row in target_audience.iterrows():
                        email = row["user_email"].lower().strip() if pd.notna(row["user_email"]) else ""
                        
                        # Skip if no email or already has a name
                        if not email:
                            continue
                            
                        # Apply mapping if the email exists in our mapping dictionary
                        if email in name_mapping:
                            # Only update if current first_name is empty or different
                            current_name = row["first_name"] if pd.notna(row["first_name"]) else ""
                            if not current_name or current_name != name_mapping[email]:
                                target_audience.at[index, "first_name"] = name_mapping[email]
                                update_count += 1
                    
                    # Add date_added column if it doesn't exist
                    if "date_added" not in target_audience.columns:
                        target_audience["date_added"] = datetime.datetime.now()
                    
                    # Save updated target audience
                    target_audience.to_csv(self.target_audience_path, index=False)
                    logger.info(f"Updated first_name for {update_count} users in target audience")
                    
                except Exception as e:
                    logger.error(f"Error updating target audience: {str(e)}")
            else:
                # Target audience file doesn't exist, create it with mapped data
                logger.info("Target audience file does not exist, creating new file")
                
                # Prepare new target audience DataFrame
                target_data = signup_data.copy()
                target_data.rename(columns={"email": "user_email"}, inplace=True)
                
                # Ensure we have date_added
                if "date_joined" in target_data.columns:
                    target_data.rename(columns={"date_joined": "date_added"}, inplace=True)
                elif "date_added" not in target_data.columns:
                    target_data["date_added"] = datetime.datetime.now()
                
                # Select and organize columns
                available_columns = ["user_email", "first_name", "date_added"]
                target_columns = [col for col in available_columns if col in target_data.columns]
                target_data = target_data[target_columns]
                
                # Save new target audience file
                target_data.to_csv(self.target_audience_path, index=False)
                logger.info(f"Created new target audience file with {len(target_data)} records")
        
        except Exception as e:
            logger.error(f"Error mapping first_name to target audience: {str(e)}")
    
    def update_bb_buyers(self):
        """
        Update BlackBelt buyers data.
        
        Returns:
            pd.DataFrame: Updated BlackBelt buyers data
        """
        try:
            # Read existing data
            bb_pur = pd.read_csv(self.bb_buyers_path) if os.path.exists(self.bb_buyers_path) else pd.DataFrame(columns=["Date", "Email_ID", "Product"])
            
            # Get sales data from Google Sheets
            sales = self.import_from_sheets("AV Sales", "Data")
            if sales.empty:
                logger.warning("No sales data found in Google Sheets")
                return bb_pur
            
            # Filter and process BlackBelt sales
            bb_sales = sales[sales["Product"].isin(["BB plus"])][["Date", "Email", "Product"]].drop_duplicates()
            bb_sales = bb_sales.assign(Email=bb_sales['Email'].str.split(',')).explode('Email')
            bb_sales = bb_sales.drop_duplicates(subset=["Email", "Product"])
            bb_sales.columns = ["Date", "Email_ID", "Product"]
            
            # Combine with existing data
            bb_pur = pd.concat([bb_pur, bb_sales])
            bb_pur = bb_pur.drop_duplicates(subset=["Email_ID"])
            
            # Save updated data
            bb_pur.to_csv(self.bb_buyers_path, index=False)
            logger.info(f"Updated BlackBelt buyers list with {len(bb_sales)} new entries")
            
            return bb_pur
        
        except Exception as e:
            logger.error(f"Error updating BlackBelt buyers: {str(e)}")
            return pd.DataFrame(columns=["Date", "Email_ID", "Product"])
    
    def update_genai_buyers(self):
        """
        Update GenAI Pinnacle buyers data.
        
        Returns:
            pd.DataFrame: Updated GenAI buyers data
        """
        try:
            # Read existing data
            genai_pur = pd.read_csv(self.genai_buyers_path) if os.path.exists(self.genai_buyers_path) else pd.DataFrame(columns=["date_placed", "email", "Title"])
            
            # Get sales data from Google Sheets
            sales = self.import_from_sheets("AV Sales", "Data")
            if sales.empty:
                logger.warning("No sales data found in Google Sheets")
                return genai_pur
            
            # Filter and process GenAI sales
            genai_sales = sales[sales["Product"].str.contains('pinnacle', case=False, na=False)][["Date", "Email", "Product"]].drop_duplicates()
            genai_sales = genai_sales.assign(Email=genai_sales['Email'].str.split(',')).explode('Email')
            genai_sales = genai_sales.drop_duplicates(subset=["Email", "Product"])
            genai_sales.columns = ["date_placed", "email", "Title"]
            
            # Combine with existing data
            genai_pur = pd.concat([genai_pur, genai_sales])
            genai_pur = genai_pur.drop_duplicates(subset=["email"])
            
            # Save updated data
            genai_pur.to_csv(self.genai_buyers_path, index=False)
            logger.info(f"Updated GenAI buyers list with {len(genai_sales)} new entries")
            
            return genai_pur
        
        except Exception as e:
            logger.error(f"Error updating GenAI buyers: {str(e)}")
            return pd.DataFrame(columns=["date_placed", "email", "Title"])
    
    def update_agenticai_buyers(self):
        """
        Update AgenticAI buyers data.
        
        Returns:
            pd.DataFrame: Updated AgenticAI buyers data
        """
        try:
            # Read existing data
            agenticai_pur = pd.read_csv(self.agenticai_buyers_path) if os.path.exists(self.agenticai_buyers_path) else pd.DataFrame(columns=["Date", "Email_ID", "Product"])
            
            # Get sales data from Google Sheets
            sales = self.import_from_sheets("AV Sales", "Data")
            if sales.empty:
                logger.warning("No sales data found in Google Sheets")
                return agenticai_pur
            
            # Filter and process AgenticAI sales
            agenticai_sales = sales[sales["Product"].isin(["Agentic AI"])][["Date", "Email", "Product"]].drop_duplicates()
            agenticai_sales = agenticai_sales.assign(Email=agenticai_sales['Email'].str.split(',')).explode('Email')
            agenticai_sales = agenticai_sales.drop_duplicates(subset=["Email", "Product"])
            agenticai_sales.columns = ["Date", "Email_ID", "Product"]
            
            # Combine with existing data
            agenticai_pur = pd.concat([agenticai_pur, agenticai_sales])
            agenticai_pur = agenticai_pur.drop_duplicates(subset=["Email_ID", "Product"])
            
            # Save updated data
            agenticai_pur.to_csv(self.agenticai_buyers_path, index=False)
            logger.info(f"Updated AgenticAI buyers list with {len(agenticai_sales)} new entries")
            
            return agenticai_pur
        
        except Exception as e:
            logger.error(f"Error updating AgenticAI buyers: {str(e)}")
            return pd.DataFrame(columns=["Date", "Email_ID", "Product"])
    
    def update_leads_data(self):
        """
        Update leads data for all products.
        
        Returns:
            tuple: DataFrames for BB, GenAI, and AgenticAI leads
        """
        try:
            connection_string = os.getenv("LEADS_DATA_CONNECTION_LINK")
            if not connection_string:
                logger.error("Leads data connection string not found in environment variables")
                return (pd.DataFrame(), pd.DataFrame(), pd.DataFrame())
            
            # Connect to database
            nmp_connection = pcg.connect(connection_string)
            nmp_cursor = nmp_connection.cursor()
            
            # Get contact form data
            nmp_cursor.execute("""
                SELECT id, created_at, fullname, email, country_code, phone, 
                       utm_source, utm_medium, utm_campaign, utm_term, utm_content, 
                       additional_data, form_type_id 
                FROM newmarketingpages_contactform;
            """)
            rows = nmp_cursor.fetchall()
            leads_data = pd.DataFrame(rows)
            leads_data.columns = ["id", "created_at", "name", "email", "country_code", "phone", 
                                 "utm_source", "utm_medium", "utm_campaign", "utm_term", 
                                 "utm_content", "additional_data", "form_type_id"]
            
            # Get form type data
            nmp_cursor.execute("SELECT id, form_name, platform_id FROM newmarketingpages_formtype;")
            rows = nmp_cursor.fetchall()
            form_data = pd.DataFrame(rows)
            form_data.columns = ["form_type_id", "form_name", "platform_id"]
            
            # Get platform data
            nmp_cursor.execute("SELECT id, platform_name FROM newmarketingpages_platform;")
            rows = nmp_cursor.fetchall()
            platform_data = pd.DataFrame(rows)
            platform_data.columns = ["platform_id", "platform_name"]
            
            nmp_connection.close()
            
            # Merge data
            leads_data = leads_data.merge(
                form_data.merge(platform_data, on="platform_id", how="left"), 
                on="form_type_id", 
                how="left"
            )
            
            # Filter by product
            bb_leads = leads_data[leads_data["platform_name"].astype(str).str.contains("blackbelt")]
            genai_leads = leads_data[leads_data["platform_name"].astype(str).str.contains("pinnacle")]
            agenticai_leads = leads_data[leads_data["platform_name"].astype(str).str.contains("agenticai")]
            
            # Select relevant columns
            columns = ["id", "created_at", "name", "email", "country_code", "phone", 
                      "utm_source", "utm_medium", "utm_campaign", "utm_term", 
                      "utm_content", "additional_data"]
            
            bb_leads = bb_leads[columns]
            genai_leads = genai_leads[columns]
            agenticai_leads = agenticai_leads[columns]
            
            # Read existing data
            existing_bb_leads = pd.read_csv(self.bb_leads_path) if os.path.exists(self.bb_leads_path) else pd.DataFrame(columns=columns)
            existing_genai_leads = pd.read_csv(self.genai_leads_path) if os.path.exists(self.genai_leads_path) else pd.DataFrame(columns=columns)
            existing_agenticai_leads = pd.read_csv(self.agenticai_leads_path) if os.path.exists(self.agenticai_leads_path) else pd.DataFrame(columns=columns)
            
            # Combine with existing data
            bb_leads = pd.concat([existing_bb_leads, bb_leads])
            bb_leads = bb_leads.drop_duplicates(subset=["id", "created_at", "email"])
            
            genai_leads = pd.concat([existing_genai_leads, genai_leads])
            genai_leads = genai_leads.drop_duplicates(subset=["id", "created_at", "email"])
            
            agenticai_leads = pd.concat([existing_agenticai_leads, agenticai_leads])
            agenticai_leads = agenticai_leads.drop_duplicates(subset=["id", "created_at", "email"])
            
            # Save updated data
            bb_leads.to_csv(self.bb_leads_path, index=False)
            genai_leads.to_csv(self.genai_leads_path, index=False)
            agenticai_leads.to_csv(self.agenticai_leads_path, index=False)
            
            logger.info(f"Updated leads data: BB={len(bb_leads)}, GenAI={len(genai_leads)}, AgenticAI={len(agenticai_leads)}")
            
            return (bb_leads, genai_leads, agenticai_leads)
        
        except Exception as e:
            logger.error(f"Error updating leads data: {str(e)}")
            return (pd.DataFrame(), pd.DataFrame(), pd.DataFrame())
    
    def update_unsubscribers(self):
        """
        Update unsubscribers data.
        
        Returns:
            pd.DataFrame: Updated unsubscribers data
        """
        try:
            connection_string = os.getenv("UNSUBSCRIBERS_DATA_CONNECTION_LINK")
            if not connection_string:
                logger.error("Unsubscribers connection string not found in environment variables")
                return pd.DataFrame()
            
            # Connect to database
            unsubscribe_connection = pcg.connect(connection_string)
            unsubscribe_cursor = unsubscribe_connection.cursor()
            
            # Execute query
            unsubscribe_cursor.execute("SELECT created_at, email, promotional FROM newnotificationmanager_unsubscribeemail;")
            rows = unsubscribe_cursor.fetchall()
            unsubscribe_connection.close()
            
            # Process data
            unsubscribe_data = pd.DataFrame(rows)
            unsubscribe_data.columns = ["created_at", "email", "Promotional"]
            unsubscribe_data = unsubscribe_data[unsubscribe_data["Promotional"]]
            unsubscribe_data=unsubscribe_data[["email"]]
            unsubscribe_data.columns=["User ID"]
            
            # Read existing data
            existing_unsubscribe = pd.read_csv(self.unsubscribers_path) if os.path.exists(self.unsubscribers_path) else pd.DataFrame(columns=["created_at", "email", "Promotional"])
            # Combine with existing data
            unsubscribe_data = pd.concat([existing_unsubscribe, unsubscribe_data])
            unsubscribe_data["User ID"] = unsubscribe_data["User ID"].str.lower().str.strip()
            unsubscribe_data = unsubscribe_data.drop_duplicates(subset=["User ID"])
            
            # Save updated data
            unsubscribe_data.to_csv(self.unsubscribers_path, index=False)
            logger.info(f"Updated unsubscribers list with {len(unsubscribe_data)} entries")
            
            return unsubscribe_data
        
        except Exception as e:
            logger.error(f"Error updating unsubscribers: {str(e)}")
            return pd.DataFrame(columns=["created_at", "email", "Promotional"])

    def update_brahma_data(self, query=None, database=None):
        """
        Update Brahma data.
        
        Args:
            query (str, optional): Custom query to use. If None, will use default.
            database (str, optional): Database to query. If None, will use default.
            
        Returns:
            pd.DataFrame: Updated Brahma data
        """
        try:

            # Use default query if not provided
            if query is None:
                today = datetime.datetime.now()

                # Calculate first day of current month and 5 months ago
                start_date = (today - relativedelta(months=5)).replace(day=1)
                end_date = today.replace(day=1)

                # Convert to YYYYMM format for compact comparison
                start_val = start_date.year * 100 + start_date.month
                end_val = end_date.year * 100 + end_date.month

                # Construct Hive query
                query = f"""
                SELECT *
                            FROM clickstream_part
                            WHERE (year * 100 + month) BETWEEN {start_val} AND {end_val}
                            AND event_type = 'pageload'
                            """
                print(query)
            # Use default database if not provided
            if database is None:
                database = "clickstream_db"
            
            # Get data from Brahma
            brahma_data = self.get_user_data_brahma(query, database)
            
            if brahma_data.empty:
                logger.warning("No Brahma data retrieved")
                return pd.DataFrame()
            
            # Save updated data
            brahma_data.to_csv(self.brahma_data_path, index=False)
            logger.info(f"Updated Brahma data with {len(brahma_data)} entries")
            
            return brahma_data
        
        except Exception as e:
            logger.error(f"Error updating Brahma data: {str(e)}")
            return pd.DataFrame()
    
    def update_all_data(self, progress_callback=None):
        """
        Update all data files required for user data processing.
        
        Args:
            progress_callback (callable, optional): Callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update progress
            if progress_callback:
                progress_callback("Starting data update process", 0)
            
            # 3. Update unsubscribers
            if progress_callback:
                progress_callback("Updating unsubscribers", 30)
            self.update_unsubscribers()
            
            # 4. Update buyers data
            if progress_callback:
                progress_callback("Updating buyers data", 40)
            self.update_bb_buyers()
            self.update_genai_buyers()
            self.update_agenticai_buyers()
            
            # 5. Update leads data
            if progress_callback:
                progress_callback("Updating leads data", 60)
            self.update_leads_data()
            
            # 6. Update Brahma data
            if progress_callback:
                progress_callback("Updating Brahma data", 80)
            self.update_brahma_data()
            
            # Complete
            if progress_callback:
                progress_callback("Data update complete", 100)
            
            logger.info("All data files updated successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error updating all data: {str(e)}")
            if progress_callback:
                progress_callback(f"Error: {str(e)}", 100)
            return False
    
    def get_file_paths(self):
        """
        Get all file paths for data processing.
        
        Returns:
            dict: Dictionary of file paths
        """
        return {
            'target_audience_path': self.target_audience_path, 
            'unsubscribers_path': self.unsubscribers_path,
            'brahma_data_path': self.brahma_data_path,
            'buyers_files': [
                self.bb_buyers_path,
                self.genai_buyers_path,
                self.agenticai_buyers_path
            ],
            'leads_files': [
                self.agenticai_leads_path,
                self.bb_leads_path,
                self.genai_leads_path
            ]
        }
    
    def refresh_data(self, 
                    fetch_signup: bool = True,
                    fetch_buyers: bool = True, 
                    fetch_leads: bool = True,
                    fetch_unsubscribers: bool = True,
                    fetch_brahma: bool = True,
                    progress_callback: Optional[Callable] = None) -> bool:
        """
        Refresh data from external sources.
        
        Args:
            fetch_signup (bool): Whether to fetch signup data
            fetch_buyers (bool): Whether to fetch buyers data
            fetch_leads (bool): Whether to fetch leads data
            fetch_unsubscribers (bool): Whether to fetch unsubscribers data
            fetch_brahma (bool): Whether to fetch Brahma data
            progress_callback (callable, optional): Callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Track progress
            progress = 0
            progress_step = 100 / (
                fetch_signup + 
                (fetch_buyers * 3) +  # 3 buyer files
                fetch_leads + 
                fetch_unsubscribers + 
                fetch_brahma
            )
            
            # Update progress
            if progress_callback:
                progress_callback("Starting data refresh", progress)
            
            # 1. Update signup data and target audience
            if fetch_signup:
                if progress_callback:
                    progress_callback("Updating signup data", progress)
                
                signup_data = self.get_signup_data()
                self.update_target_audience(signup_data)
                del signup_data
                
                progress += progress_step
                if progress_callback:
                    progress_callback("Signup data updated", progress)
            
            # 2. Update buyers data
            if fetch_buyers:
                if progress_callback:
                    progress_callback("Updating BlackBelt buyers", progress)
                
                self.update_bb_buyers()
                
                progress += progress_step
                if progress_callback:
                    progress_callback("BlackBelt buyers updated", progress)
                
                if progress_callback:
                    progress_callback("Updating GenAI buyers", progress)
                
                self.update_genai_buyers()
                
                progress += progress_step
                if progress_callback:
                    progress_callback("GenAI buyers updated", progress)
                
                if progress_callback:
                    progress_callback("Updating AgenticAI buyers", progress)
                
                self.update_agenticai_buyers()
                
                progress += progress_step
                if progress_callback:
                    progress_callback("AgenticAI buyers updated", progress)
            
            # 3. Update leads data
            if fetch_leads:
                if progress_callback:
                    progress_callback("Updating leads data", progress)
                
                self.update_leads_data()
                
                progress += progress_step
                if progress_callback:
                    progress_callback("Leads data updated", progress)
            
            # 4. Update unsubscribers
            if fetch_unsubscribers:
                if progress_callback:
                    progress_callback("Updating unsubscribers", progress)
                
                self.update_unsubscribers()
                
                progress += progress_step
                if progress_callback:
                    progress_callback("Unsubscribers updated", progress)
            
            # 5. Update Brahma data
            if fetch_brahma:
                if progress_callback:
                    progress_callback("Updating Brahma data", progress)
                
                self.update_brahma_data()
                
                progress += progress_step
                if progress_callback:
                    progress_callback("Brahma data updated", progress)
            
            # Complete
            if progress_callback:
                progress_callback("Data refresh complete", 100)
            
            logger.info("All requested data files updated successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error refreshing data: {str(e)}")
            if progress_callback:
                progress_callback(f"Error: {str(e)}", 100)
            return False
    
