"""
WhatsApp campaign generation functionality for OpenEngage.
"""
import os
import json
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
from dotenv import load_dotenv

# Import template manager
from .whatsapp_template_manager import WhatsAppTemplateManager
from .whatsapp_sender import create_whatsapp_sender

class WhatsAppCampaignGenerator:
    """Generator for WhatsApp campaigns"""
    
    def __init__(self, provider: str = "gupshup"):
        """
        Initialize the WhatsApp campaign generator.
        
        Args:
            provider: WhatsApp service provider (default: gupshup)
        """
        self.provider = provider.lower()
        self.logger = self._setup_logger()
        self.template_manager = WhatsAppTemplateManager()
        
        # Load environment variables
        load_dotenv()
        
        # Get API key based on provider
        if self.provider == "gupshup":
            self.api_key = os.getenv("GUPSHUP_API_KEY", "")
        elif self.provider == "twilio":
            self.api_key = os.getenv("TWILIO_API_KEY", "")
        elif self.provider == "messagebird":
            self.api_key = os.getenv("MESSAGEBIRD_API_KEY", "")
        else:
            self.api_key = ""
            self.logger.error(f"Unsupported WhatsApp provider: {provider}")
        
        # Get sender details
        self.sender_name = os.getenv("WHATSAPP_SENDER_NAME", "OpenEngage")
        self.phone_number = os.getenv("WHATSAPP_PHONE_NUMBER", "")
        
        # Initialize sender if API key is available
        if self.api_key:
            try:
                self.sender = create_whatsapp_sender(self.provider, self.api_key)
                self.sender.set_sender_details(self.sender_name, self.phone_number)
            except Exception as e:
                self.logger.error(f"Error initializing WhatsApp sender: {str(e)}")
                self.sender = None
        else:
            self.sender = None
            self.logger.warning(f"No API key found for {self.provider}. WhatsApp sender not initialized.")
    
    def _setup_logger(self):
        """Set up logger for the campaign generator"""
        logger = logging.getLogger("openengage.whatsapp.campaign_generator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def generate_campaign(self, audience_data: pd.DataFrame, product_name: str, 
                          variable_values: Optional[Dict[str, List[str]]] = None) -> Dict[str, Any]:
        """
        Generate and send a WhatsApp campaign.
        
        Args:
            audience_data: DataFrame containing audience data with columns:
                - phone_number: Recipient's phone number
                - first_name: Recipient's first name (optional)
            product_name: Name of the product to use for template selection
            variable_values: Optional dictionary of variable values to use for each recipient
                             If not provided, will use first_name as the first variable
        
        Returns:
            Dict with results of the campaign generation
        """
        self.logger.info(f"Generating WhatsApp campaign for product: {product_name}")
        
        # Check if sender is initialized
        if not self.sender:
            error_msg = f"WhatsApp sender not initialized. Cannot generate campaign."
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        
        # Get template ID for the product
        template_id = self.template_manager.get_product_template(product_name)
        if not template_id:
            error_msg = f"No template mapped for product: {product_name}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        
        # Get template details
        template = self.template_manager.get_template(template_id)
        if not template:
            error_msg = f"Template not found: {template_id}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        
        # Prepare message data
        message_data = pd.DataFrame()
        message_data["phone_number"] = audience_data["phone_number"]
        message_data["template_id"] = template_id
        
        # Prepare parameters for each recipient
        params_list = []
        for idx, row in audience_data.iterrows():
            # Initialize params with default values
            params = []
            
            # If variable_values is provided, use those values
            if variable_values:
                for var_name, values in variable_values.items():
                    if idx < len(values):
                        params.append(values[idx])
                    else:
                        params.append("")
            else:
                # Use first_name as the first parameter if available
                if "first_name" in row:
                    params.append(row["first_name"])
                else:
                    params.append("there")  # Default greeting
                
                # Add brand name as the last parameter
                params.append(self.sender_name)
            
            # Ensure we have enough parameters for the template
            while len(params) < template.get("variable_count", 0):
                params.append("")
            
            params_list.append(params)
        
        message_data["params"] = params_list
        
        # Send the messages
        try:
            results = self.sender.send_messages(message_data)
            return {
                "success": True,
                "results": results
            }
        except Exception as e:
            error_msg = f"Error sending WhatsApp campaign: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def generate_personalized_campaign(self, audience_data: pd.DataFrame, product_name: str,
                                      content_generator: callable) -> Dict[str, Any]:
        """
        Generate and send a personalized WhatsApp campaign using a content generator function.
        
        Args:
            audience_data: DataFrame containing audience data
            product_name: Name of the product to use for template selection
            content_generator: Function that takes a row of audience data and returns a list of template parameters
        
        Returns:
            Dict with results of the campaign generation
        """
        self.logger.info(f"Generating personalized WhatsApp campaign for product: {product_name}")
        
        # Generate personalized content for each recipient
        variable_values = {}
        for i in range(10):  # Support up to 10 variables
            variable_values[f"var_{i}"] = []
        
        for idx, row in audience_data.iterrows():
            # Generate content for this recipient
            params = content_generator(row)
            
            # Store in variable_values
            for i, param in enumerate(params):
                variable_values[f"var_{i}"].append(param)
        
        # Generate and send the campaign
        return self.generate_campaign(audience_data, product_name, variable_values)
