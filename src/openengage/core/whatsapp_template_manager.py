"""
WhatsApp template management functionality for OpenEngage.
"""
import os
import json
import logging
from typing import Dict, Any, List, Optional

class WhatsAppTemplateManager:
    """Manager for WhatsApp templates"""

    def __init__(self, templates_file: str = 'data/whatsapp_templates.json'):
        """
        Initialize the WhatsApp template manager.

        Args:
            templates_file: Path to the templates JSON file
        """
        self.templates_file = templates_file
        self.logger = self._setup_logger()
        self.templates = self._load_templates()

    def _setup_logger(self):
        """Set up logger for the template manager"""
        logger = logging.getLogger("openengage.whatsapp.template_manager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _load_templates(self) -> Dict[str, Any]:
        """
        Load templates from the JSON file.

        Returns:
            Dict containing templates data
        """
        if os.path.exists(self.templates_file):
            try:
                with open(self.templates_file, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                self.logger.warning(f"Error parsing {self.templates_file}. Using empty templates.")
                return {"templates": [], "product_templates": {}}
        else:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.templates_file), exist_ok=True)
            # Return empty templates structure
            return {"templates": [], "product_templates": {}}

    def _save_templates(self) -> bool:
        """
        Save templates to the JSON file.

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.templates_file, 'w') as f:
                json.dump(self.templates, f, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"Error saving templates: {str(e)}")
            return False

    def add_template(self, template_id: str, template_name: str, description: str,
                     variable_count: int = 2, example_text: str = None, button_data: dict = None) -> bool:
        """
        Add a new template.

        Args:
            template_id: ID of the template from Gupshup
            template_name: Name of the template
            description: Description of the template
            variable_count: Number of variables in the template (default: 2)
            example_text: Example text with variables (default: standard template)
            button_data: Optional button configuration data

        Returns:
            True if successful, False otherwise
        """
        # Import datetime here to avoid circular import issues
        from datetime import datetime

        # Always use 2 variables ({{1}} and {{2}})
        variable_count = 2

        # Use default example text if none provided
        if not example_text:
            example_text = f"Hi {{{{1}}}},\n\n{{{{2}}}}\n\nBest regards,\nAnalytics Vidhya"

        # Check if template already exists
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                # Update existing template
                template.update({
                    "template_name": template_name,
                    "description": description,
                    "variable_count": variable_count,
                    "example_text": example_text
                })

                # Add button data if provided
                if button_data:
                    template["button_data"] = button_data

                return self._save_templates()

        # Add new template
        if "templates" not in self.templates:
            self.templates["templates"] = []

        # Create new template data
        template_data = {
            "template_id": template_id,
            "template_name": template_name,
            "description": description,
            "variable_count": variable_count,
            "example_text": example_text,
            "created_at": str(datetime.now())
        }

        # Add button data if provided
        if button_data:
            template_data["button_data"] = button_data

        self.templates["templates"].append(template_data)

        return self._save_templates()

    def get_templates(self) -> List[Dict[str, Any]]:
        """
        Get all templates.

        Returns:
            List of template dictionaries
        """
        return self.templates.get("templates", [])

    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific template by ID.

        Args:
            template_id: ID of the template

        Returns:
            Template dictionary or None if not found
        """
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                return template
        return None

    def map_product_template(self, product_name: str, template_id: str) -> bool:
        """
        Map a product to a template. Each product can only have one template.
        If a product already has a template, it will be updated.

        Args:
            product_name: Name of the product
            template_id: ID of the template

        Returns:
            True if successful, False otherwise
        """
        # Check if template exists
        template_exists = False
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                template_exists = True
                break

        if not template_exists:
            self.logger.error(f"Template {template_id} does not exist")
            return False

        # Add or update product-template mapping
        if "product_templates" not in self.templates:
            self.templates["product_templates"] = {}

        # Update the mapping for this product (overwriting any previous mapping)
        self.templates["product_templates"][product_name] = template_id

        self.logger.info(f"Mapped template {template_id} to product {product_name}")
        return self._save_templates()

    def add_product_template_mapping(self, product_name: str, template_id: str) -> bool:
        """
        Add a template mapping for a product. Multiple templates can be mapped to a product.

        Args:
            product_name: Name of the product
            template_id: ID of the template

        Returns:
            True if successful, False otherwise
        """
        # Check if template exists
        template_exists = False
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                template_exists = True
                break

        if not template_exists:
            self.logger.error(f"Template {template_id} does not exist")
            return False

        # Initialize product_template_mappings if it doesn't exist
        if "product_template_mappings" not in self.templates:
            self.templates["product_template_mappings"] = {}

        # Initialize list for this product if it doesn't exist
        if product_name not in self.templates["product_template_mappings"]:
            self.templates["product_template_mappings"][product_name] = []

        # Add template ID to the list if not already present
        if template_id not in self.templates["product_template_mappings"][product_name]:
            self.templates["product_template_mappings"][product_name].append(template_id)
            self.logger.info(f"Added template {template_id} to product {product_name} mappings")
        else:
            self.logger.info(f"Template {template_id} already mapped to product {product_name}")

        # Also update the single template mapping for backward compatibility
        # This is the primary mapping that will be used for sending messages
        if "product_templates" not in self.templates:
            self.templates["product_templates"] = {}
        self.templates["product_templates"][product_name] = template_id

        return self._save_templates()

    def get_product_template(self, product_name: str) -> Optional[str]:
        """
        Get the template ID for a product.

        Args:
            product_name: Name of the product

        Returns:
            Template ID or None if not mapped
        """
        return self.templates.get("product_templates", {}).get(product_name)

    def get_template_id_for_product(self, product_name: str) -> str:
        """
        Get the template ID for a product. If multiple templates are mapped to the product,
        returns the primary template ID. If no template is mapped, randomly selects a template.

        Args:
            product_name: Name of the product

        Returns:
            Template ID or a randomly selected template ID if not mapped
        """
        # First try to get from product_templates (primary mapping)
        template_id = self.templates.get("product_templates", {}).get(product_name)
        if template_id:
            return template_id

        # If not found, try to get from product_template_mappings
        template_ids = self.templates.get("product_template_mappings", {}).get(product_name, [])
        if template_ids:
            return template_ids[0]  # Return the first template ID

        # If still not found, randomly select a template from all available templates
        all_templates = self.get_templates()
        if all_templates:
            import random
            random_template = random.choice(all_templates)
            template_id = random_template.get("template_id", "")
            self.logger.info(f"No template mapped for product '{product_name}'. Randomly selected template: {template_id}")
            return template_id

        # If no templates available at all, return empty string
        return ""

    def get_product_templates(self) -> Dict[str, str]:
        """
        Get all product-template mappings (single template per product).

        Returns:
            Dict of product names to template IDs
        """
        return self.templates.get("product_templates", {})

    def get_all_product_template_mappings(self) -> Dict[str, List[str]]:
        """
        Get all product-template mappings (multiple templates per product).

        Returns:
            Dict of product names to lists of template IDs
        """
        return self.templates.get("product_template_mappings", {})

    def get_product_templates_list(self, product_name: str) -> List[str]:
        """
        Get all template IDs for a specific product.

        Args:
            product_name: Name of the product

        Returns:
            List of template IDs
        """
        return self.templates.get("product_template_mappings", {}).get(product_name, [])

    def get_unmapped_products(self, all_product_names: List[str]) -> List[str]:
        """
        Get a list of products that don't have any template mappings.

        Args:
            all_product_names: List of all product names

        Returns:
            List of unmapped product names
        """
        mapped_products = set(self.templates.get("product_template_mappings", {}).keys())
        return [product for product in all_product_names if product not in mapped_products]

    def delete_template(self, template_id: str) -> bool:
        """
        Delete a template.

        Args:
            template_id: ID of the template

        Returns:
            True if successful, False otherwise
        """
        # Remove template from templates list
        templates = self.templates.get("templates", [])
        self.templates["templates"] = [t for t in templates if t.get("template_id") != template_id]

        # Remove template from product mappings
        product_templates = self.templates.get("product_templates", {})
        for product, tid in list(product_templates.items()):
            if tid == template_id:
                del product_templates[product]

        return self._save_templates()

# No need for global datetime import as it's now imported locally in methods
