"""
Simplified WhatsApp mass campaign generator for OpenEngage.
"""
import os
import json
import random
import pandas as pd
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize OpenAI client if API key is available
openai_api_key = os.getenv("OPENAI_API_KEY")
client = None
if openai_api_key:
    try:
        client = OpenAI(api_key=openai_api_key)
    except Exception as e:
        print(f"Error initializing OpenAI client: {str(e)}")
else:
    print("Warning: OPENAI_API_KEY not found in environment variables. AI message generation will be disabled.")

# Import WhatsApp template manager and sender
try:
    # Try direct import first
    from core.whatsapp_template_manager import WhatsAppTemplateManager
    from core.gupshup_whatsapp_sender import get_esp_api_keys, get_sender_details
except ImportError:
    # Try package import
    from openengage.core.whatsapp_template_manager import WhatsAppTemplateManager
    from openengage.core.gupshup_whatsapp_sender import get_esp_api_keys, get_sender_details

def clean_phone_number(phone: str) -> str:
    """
    Clean phone number by removing non-numeric characters and ensuring it has country code.

    Args:
        phone: Phone number to clean

    Returns:
        Cleaned phone number
    """
    # Convert to string if not already
    phone = str(phone)

    # Remove all non-numeric characters
    phone = ''.join(filter(str.isdigit, phone))

    # Add default country code (91 for India) if not present
    if not phone.startswith('91') and len(phone) <= 10:
        phone = '91' + phone

    return phone

def process_simplified_whatsapp_data(data_df: pd.DataFrame, progress_callback: Optional[Callable] = None) -> pd.DataFrame:
    """
    Process WhatsApp data for generating personalized campaigns with a simplified approach.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data with columns:
            - phone_number: Recipient's phone number
            - first_name: Recipient's first name
            - user_behaviour: Description of user's behavior or preferences
            - product (optional): Product name
        progress_callback (callable, optional): Function to report progress

    Returns:
        pd.DataFrame: DataFrame with generated WhatsApp content
    """
    # Initialize template manager
    template_manager = WhatsAppTemplateManager()

    # Load products from product_details.json
    products = []
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
            if not isinstance(products, list):
                products = [products]
    except Exception as e:
        print(f"Error loading product details: {str(e)}")
        # Continue with empty products list

    # Create product name to product mapping for quick lookup
    product_map = {p.get('Product_Name', ''): p for p in products}

    # If no products found, log a warning
    if not product_map:
        print("Warning: No products found in product_details.json")

    # Report initial progress
    if progress_callback:
        progress_callback(0, len(data_df), "Starting WhatsApp campaign generation...")

    # Process each row in the DataFrame
    for idx, row in data_df.iterrows():
        # Update progress
        if progress_callback and idx % 10 == 0:
            progress_callback(idx, len(data_df), f"Processing {idx}/{len(data_df)} users...")

        # Clean phone number
        if 'phone_number' in row:
            data_df.at[idx, 'phone_number'] = clean_phone_number(row['phone_number'])

        # Get product name - either from CSV or assign based on user behavior
        product_name = None

        # If product column exists and has a value, use it
        if 'product' in row and row['product']:
            product_name = row['product']
            # Verify the product exists in our product map
            if product_name not in product_map:
                print(f"Warning: Product '{product_name}' not found in product_details.json")
        else:
            # Assign product based on similarity (simplified to random selection)
            # In a real implementation, you might want to use a more sophisticated approach
            if product_map:
                # Simple random selection for demonstration
                product_name = random.choice(list(product_map.keys()))
            else:
                product_name = 'Default Product'

        # Get all template IDs for this product
        template_ids = template_manager.get_product_templates_list(product_name)

        # If no templates are mapped, try to get the primary template
        if not template_ids:
            template_id = template_manager.get_product_template(product_name)
            if template_id:
                template_ids = [template_id]

        # If still no templates, log an error and continue
        if not template_ids:
            print(f"No template found for product: {product_name}")
            data_df.at[idx, 'Template_ID'] = ""
            data_df.at[idx, 'WhatsApp_Content'] = "No template available for this product."
            continue

        # Randomly select a template from the available templates
        template_id = random.choice(template_ids)

        # Generate a personalized message for {{2}} using OpenAI
        # Get product details if available
        product_details = product_map.get(product_name, {})

        # Get user behavior if available
        user_behavior = row.get('user_behaviour', '')

        # Get user first name
        first_name = row.get('first_name', 'Customer')

        try:
            # Check if OpenAI client is available
            if client is None:
                raise ValueError("OpenAI client not available")

            # Create a prompt for generating WhatsApp content
            product_features = ""
            if product_details and 'Product_Features' in product_details and product_details['Product_Features']:
                product_features = "\n".join([f"- {feature}" for feature in product_details['Product_Features']])

            product_description = product_details.get('Product_Description', f"A product called {product_name}")

            # Get email template for this product to use as reference
            email_template = None
            email_template_content = ""

            # Try to find an email template for this product
            template_stages = ["new_visitor", "product_page_viewed", "cart_abandoned", "product_purchased"]
            for stage in template_stages:
                template_file = f'data/templates/{stage}.json'
                if os.path.exists(template_file):
                    try:
                        with open(template_file, 'r') as f:
                            templates = json.load(f)
                            # Find a template for this product
                            matching_templates = [t for t in templates if t.get('product_data', {}).get('Product_Name') == product_name]
                            if matching_templates:
                                email_template = matching_templates[0]
                                if email_template and 'template' in email_template:
                                    email_template_content = email_template['template'].get('body', '')
                                    # We found a template, no need to check other stages
                                    break
                    except Exception as e:
                        print(f"Error loading email template from {template_file}: {str(e)}")

            # If no specific template was found, try to find any template
            if not email_template_content:
                for stage in template_stages:
                    template_file = f'data/templates/{stage}.json'
                    if os.path.exists(template_file):
                        try:
                            with open(template_file, 'r') as f:
                                templates = json.load(f)
                                if templates and len(templates) > 0:
                                    email_template = templates[0]
                                    if email_template and 'template' in email_template:
                                        email_template_content = email_template['template'].get('body', '')
                                        # We found a template, no need to check other stages
                                        break
                        except Exception as e:
                            print(f"Error loading email template from {template_file}: {str(e)}")

            # System prompt for WhatsApp message generation
            system_prompt = """
            You are an expert WhatsApp marketing specialist. Your task is to create personalized WhatsApp messages
            based on user behavior, product information, and existing email templates.

            Generate a short, concise WhatsApp message for the specified product. The message should be 2-3 lines maximum.

            Your response should be in JSON format with the following structure:
            {
                "content": "Your personalized WhatsApp message content here"
            }

            IMPORTANT INSTRUCTIONS:
            1. DO NOT include any greetings (like "Hi", "Hello", "Dear") or salutations (like "Thanks", "Regards", etc.)
            2. The template already has greeting with {{1}} for the name and closing text
            3. Your message will be inserted as {{2}} in the middle of the template
            4. Start directly with the personalized content about the product
            5. Focus only on the key benefits relevant to the user's behavior
            6. ONLY generate the message content - no greeting, no salutation, no signature
            7. The message should be ONLY the core content about the product benefits
            8. Keep it under 160 characters total
            9. Use the email template as a reference for tone, style, and key selling points
            10. ONLY mention features that are actually mentioned in the product details or email template
            11. DO NOT invent or make up features that aren't mentioned in the provided information
            12. Focus on personalization based on user behavior if available

            Example of what NOT to do:
            "Hi {{1}}! Check out our amazing product with features X and Y. Thanks, Team Z"

            Example of what TO do:
            "Our product offers features X and Y that solve your specific problem with impressive results."
            """

            # User message with product and user details
            user_message = f"""
            Generate a short, personalized WhatsApp message for {first_name} about {product_name}.

            Product Details:
            Product Name: {product_name}
            Product Description: {product_description}
            Product Features:
            {product_features}

            User Details:
            First Name: {first_name}
            User Behavior: {user_behavior}

            Reference Email Template:
            {email_template_content}

            Instructions:
            1. Write a short, engaging WhatsApp message (2-3 lines maximum)
            2. Focus on the most relevant product benefits for this user based on their behavior
            3. Keep it personal and conversational
            4. Do not include any links or formatting in the WhatsApp message
            5. Use the email template as reference for accurate product information and tone
            6. Only mention features that are actually part of the product (from product details or email template)
            7. Ensure the message is personalized and relevant to the user's specific interests or behavior
            """

            # Update progress to show we're generating AI content
            if progress_callback:
                progress_callback(idx, len(data_df), f"Generating AI message for user {idx+1}/{len(data_df)}...")

            # Call OpenAI API
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                temperature=0.7,
                response_format={"type": "json_object"},
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_message
                    }
                ]
            )

            # Extract content from response
            content = response.choices[0].message.content
            response_json = json.loads(content)
            whatsapp_content = response_json.get("content", "")

            # If no content was generated, use a fallback message
            if not whatsapp_content:
                raise ValueError("No content generated by AI")

        except Exception as e:
            print(f"Error generating AI message for user {idx}: {str(e)}")
            # Fallback to a simple message if AI generation fails

            # Try to extract key selling points from email template if available
            key_selling_point = ""
            if email_template_content:
                # Look for key features or benefits in the email template
                lines = email_template_content.split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in ['feature', 'benefit', 'offer', 'exclusive', 'special']):
                        # Found a potential selling point
                        key_selling_point = line.strip()
                        # Remove any greetings or salutations
                        for greeting in ['hi', 'hello', 'dear', 'thanks', 'thank you', 'regards', 'best']:
                            if key_selling_point.lower().startswith(greeting):
                                key_selling_point = key_selling_point[key_selling_point.find(' ')+1:]
                        # Keep it short
                        if len(key_selling_point) > 100:
                            key_selling_point = key_selling_point[:97] + "..."
                        break

            if key_selling_point:
                # Use the extracted selling point
                whatsapp_content = key_selling_point
            elif product_details and 'Product_Features' in product_details and product_details['Product_Features']:
                # Use a random feature from the product
                feature = random.choice(product_details['Product_Features'])
                whatsapp_content = f"Our {product_name} includes {feature} that perfectly matches your needs. Check it out today for exclusive benefits."
            else:
                # Default message if no specific details available
                whatsapp_content = f"Our {product_name} offers great features that match your needs. Check it out today for exclusive benefits."

        # Update DataFrame with WhatsApp content
        data_df.at[idx, 'WhatsApp_Content'] = whatsapp_content
        data_df.at[idx, 'Matched_Product'] = product_name
        data_df.at[idx, 'Template_ID'] = template_id

        # Set the parameters for WhatsApp template
        data_df.at[idx, 'param_1'] = row['first_name']  # {{1}} is first name
        data_df.at[idx, 'param_2'] = whatsapp_content   # {{2}} is personalized content

    # Final progress update
    if progress_callback:
        progress_callback(len(data_df), len(data_df), "Completed WhatsApp campaign generation.")

    return data_df

def send_whatsapp_campaign(campaign_df: pd.DataFrame, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Send WhatsApp campaign using the gupshup_whatsapp_sender.

    Args:
        campaign_df (pd.DataFrame): DataFrame containing campaign data
        progress_callback (callable, optional): Function to report progress

    Returns:
        Dict with results of the send operation
    """
    # Import the WhatsApp sender
    try:
        from core.whatsapp_sender import create_whatsapp_sender
    except ImportError:
        from openengage.core.whatsapp_sender import create_whatsapp_sender

    # Get API keys and sender details
    api_keys = get_esp_api_keys()
    sender_details = get_sender_details()

    # Find active WhatsApp sender configuration
    active_config = None
    for config in sender_details:
        if config.get("channel") == "WhatsApp" and config.get("active"):
            active_config = config
            break

    if not active_config:
        return {
            "success": False,
            "error": "No active WhatsApp sender configuration found."
        }

    # Get the appropriate API key
    esp = active_config.get("esp")
    api_key = None

    if esp == "Gupshup":
        api_key = api_keys.get("gupshup")
    elif esp == "Twilio":
        api_key = api_keys.get("twilio")
    elif esp == "MessageBird":
        api_key = api_keys.get("messagebird")

    if not api_key:
        return {
            "success": False,
            "error": f"No API key found for {esp}."
        }

    # Create WhatsApp sender
    sender = create_whatsapp_sender(esp.lower(), api_key)

    # Set sender details (for WhatsApp, sender_email field contains the phone number)
    sender.set_sender_details(
        active_config.get("sender_name"),
        active_config.get("sender_email")  # This contains the phone number
    )

    # Check if required columns exist
    required_columns = ['phone_number', 'Template_ID', 'param_1', 'param_2']
    missing_columns = [col for col in required_columns if col not in campaign_df.columns]

    if missing_columns:
        return {
            "success": False,
            "error": f"Missing required columns for WhatsApp campaign: {', '.join(missing_columns)}"
        }

    # Create a new DataFrame with the required columns for WhatsApp
    # Convert param_1 and param_2 to strings and create a proper list for params
    params_list = []
    for _, row in campaign_df.iterrows():
        # Ensure both parameters are strings
        param_1 = str(row['param_1']) if 'param_1' in row else ""
        param_2 = str(row['param_2']) if 'param_2' in row else ""
        params_list.append([param_1, param_2])

    whatsapp_df = pd.DataFrame({
        'phone_number': campaign_df['phone_number'],
        'template_id': campaign_df['Template_ID'],
        'params': params_list
    })

    # Report progress
    if progress_callback:
        progress_callback(0, 1, f"Sending WhatsApp campaign via {esp}...")

    # Send the messages
    results = sender.send_messages(whatsapp_df)

    # Report completion
    if progress_callback:
        progress_callback(1, 1, "WhatsApp campaign sending completed.")

    return results

def send_whatsapp_messages_one_by_one(campaign_df: pd.DataFrame, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Send WhatsApp messages one by one using the gupshup_whatsapp_sender.

    Args:
        campaign_df (pd.DataFrame): DataFrame containing campaign data
        progress_callback (callable, optional): Function to report progress

    Returns:
        Dict with results of the send operation
    """
    # Import the WhatsApp sender module
    try:
        from core.gupshup_whatsapp_sender import send_test_whatsapp_message
    except ImportError:
        from openengage.core.gupshup_whatsapp_sender import send_test_whatsapp_message

    # Check if required columns exist
    required_columns = ['phone_number', 'Template_ID', 'param_1', 'param_2']
    missing_columns = [col for col in required_columns if col not in campaign_df.columns]

    if missing_columns:
        return {
            "success": False,
            "error": f"Missing required columns for WhatsApp campaign: {', '.join(missing_columns)}"
        }

    # Initialize results
    results = {
        "total_accepted": 0,
        "total_rejected": 0,
        "errors": [],
        "message_data": pd.DataFrame(columns=['Phone_Number', 'Template_ID', 'Status', 'Message_ID', 'Time_Send'])
    }

    # Get total number of messages to send
    total_messages = len(campaign_df)

    # Report initial progress
    if progress_callback:
        progress_callback(0, total_messages, f"Preparing to send {total_messages} WhatsApp messages...")

    # Process each message one by one
    for idx, row in campaign_df.iterrows():
        # Update progress
        if progress_callback:
            progress_callback(idx, total_messages, f"Sending message to {row['phone_number']} ({idx+1}/{total_messages})...")

        try:
            # Get message parameters
            phone_number = str(row['phone_number'])
            template_id = str(row['Template_ID'])
            param_1 = str(row['param_1']) if 'param_1' in row else ""
            param_2 = str(row['param_2']) if 'param_2' in row else ""

            # Create variable values list
            variable_values = [param_1, param_2]

            # Send the message using gupshup_whatsapp_sender
            message_result = send_test_whatsapp_message(
                recipient_phone=phone_number,
                selected_template_id=template_id,
                variable_values=variable_values,
                selected_esp="Gupshup"  # Default to Gupshup
            )

            # Process result
            if message_result.get("success", False) or message_result.get("total_accepted", 0) > 0:
                results["total_accepted"] += 1

                # Add message data if available
                if "message_data" in message_result and message_result["message_data"] is not None:
                    message_data = message_result["message_data"].copy()
                    message_data['Phone_Number'] = phone_number
                    message_data['Template_ID'] = template_id
                    results["message_data"] = pd.concat([results["message_data"], message_data])
            else:
                results["total_rejected"] += 1
                error_msg = message_result.get("error", "Unknown error")
                results["errors"].append(f"Error sending to {phone_number}: {error_msg}")

            # Add a small delay between messages to avoid rate limiting
            import time
            time.sleep(0.5)

        except Exception as e:
            # Handle any exceptions
            results["total_rejected"] += 1
            results["errors"].append(f"Error sending to {row['phone_number']}: {str(e)}")

    # Report completion
    if progress_callback:
        progress_callback(total_messages, total_messages,
                         f"Completed! Successfully sent {results['total_accepted']} out of {total_messages} messages.")

    return results
