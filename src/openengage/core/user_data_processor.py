"""
Process user data to generate mass email campaign data.
"""
import pandas as pd
import json
import ast
import asyncio
import aiohttp
from urllib.parse import urlparse, urlunparse
from collections import defaultdict
from bs4 import BeautifulSoup
import re
import os
import glob
import datetime
import logging
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UserDataProcessor:
    def __init__(self):
        """
        Initialize the UserDataProcessor with necessary configuration.
        """
        # Sample product details since we don't have access to the actual JSON files
        self.product_details = [
            {"name": "Certified AI/ML BlackBelt Plus Program", "url": "/blackbelt"},
            {"name": "GenAI Pinnacle Plus Program", "url": "/genai-pinnacle"},
            {"name": "Agentic AI Pioneer Program", "url": "/agentic-ai"}
        ]

        # Sample user journey (not used directly in current implementation)
        self.user_journey = {
            "stages": [
                {"name": "Product Purchased", "s_no": 4},
                {"name": "Product Lead Generated", "s_no": 3},
                {"name": "Product Page Viewed", "s_no": 2},
                {"name": "New Visitor", "s_no": 1}
            ]
        }

        # Cache for product purchases and leads to avoid repeated file reads
        self.buyers_cache = {}
        self.leads_cache = {}

    def _load_json(self, path):
        """Load and parse JSON file."""
        try:
            with open(path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load JSON file {path}: {str(e)}")
            return {}

    def _get_first_name(self, email):
        """Extract first name from email address."""
        return email.split('@')[0] if email else ''

    def _find_mail_performance_files(self):
        """Find mail performance files in the data/mail_performance/combined folder."""
        try:
            # Determine the base path
            base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

            performance_path = os.path.join(base_path, 'data', 'mail_performance', 'combined')


            # Check if directory exists
            if not os.path.exists(performance_path):
                logger.warning(f"Mail performance directory not found: {performance_path}")

                data_path = os.path.join(base_path, 'data')
                mail_perf_path = os.path.join(base_path, 'data', 'mail_performance')


                return []

            # Find all performance files matching the pattern
            pattern = os.path.join(performance_path, 'all_performance_*.csv')
            performance_files = sorted(glob.glob(pattern))

            logger.info(f"Found {len(performance_files)} mail performance files: {performance_files}")

            # Check if files are accessible and not empty
            for file in performance_files:
                try:
                    file_size = os.path.getsize(file)

                except Exception as e:
                    logger.warning(f"Cannot access file {file}: {str(e)}")

            return performance_files
        except Exception as e:
            logger.error(f"Error finding mail performance files: {str(e)}")
            import traceback

            return []

    def _extract_mail_performance_data(self):
        """Extract email engagement metrics from mail performance data."""


        performance_files = self._find_mail_performance_files()
        if not performance_files:
            logger.info("No mail performance files found. Skipping engagement metrics extraction.")

            return {}, {}, {}, {}, {}, {}, {}, {}, {}

        # Initialize dictionaries to track metrics per email
        last_open_times = {}
        last_click_times = {}
        last_send_times = {}
        all_subjects_opened = {}
        all_preheaders_opened = {}
        last_mail_content_clicked = {}
        total_mails_sent = {}
        last_product_sent = {}
        most_frequent_open_times = {}

        try:
            for file_path in performance_files:
                logger.info(f"Processing mail performance file: {file_path}")

                # Load the performance data
                perf_df = pd.read_csv(file_path)

                # Check for required columns - using correct column names
                required_cols = ['user_email', 'Send_Time']
                expected_cols = ['Open_Time', 'Click_Time', 'Subject', 'Preheader', 'Mail_Content', 'Matched_Product']

                if not all(col in perf_df.columns for col in required_cols):
                    available_cols = list(perf_df.columns)
                    logger.warning(f"Mail performance file {file_path} missing required columns. "
                                 f"Required: {required_cols}, Available: {available_cols}")
                    continue

                # Log warning for any expected columns that are missing
                missing_expected = [col for col in expected_cols if col not in perf_df.columns]
                if missing_expected:
                    logger.warning(f"Mail performance file {file_path} missing some expected columns: {missing_expected}")

                # Convert timestamp columns to datetime
                for time_col in ['Open_Time', 'Click_Time', 'Send_Time']:
                    if time_col in perf_df.columns:
                        perf_df[time_col] = pd.to_datetime(perf_df[time_col], errors='coerce')

                # Sort by most recent Send_Time to ensure we get the latest interactions
                perf_df = perf_df.sort_values('Send_Time', ascending=False)

                # Process total emails sent
                for _, row in perf_df.groupby('user_email').size().reset_index().iterrows():
                    email = row['user_email'].lower()
                    count = row[0]  # The count is in the 0 column after reset_index

                    # Add to the total count
                    if email in total_mails_sent:
                        total_mails_sent[email] += count
                    else:
                        total_mails_sent[email] = count

                # Process last send time (for all emails)
                for _, row in perf_df.groupby('user_email')['Send_Time'].first().reset_index().iterrows():
                    email = row['user_email'].lower()
                    send_time = row['Send_Time']

                    # Update if this is the latest send time
                    if email not in last_send_times or send_time > last_send_times[email]:
                        last_send_times[email] = send_time

                        # Also track the product from the latest email
                        if 'Matched_Product' in perf_df.columns:
                            # Find the product associated with this send time
                            product_rows = perf_df[(perf_df['user_email'].str.lower() == email) &
                                                  (perf_df['Send_Time'] == send_time)]
                            if not product_rows.empty and pd.notna(product_rows.iloc[0].get('Matched_Product')):
                                last_product_sent[email] = product_rows.iloc[0]['Matched_Product']

                # Process open times - for each user, get the most recent open time
                if 'Open_Time' in perf_df.columns:
                    open_data = perf_df[perf_df['Open_Time'].notna()]

                    # Group by user_email and get the most recent Open_Time
                    if not open_data.empty:
                        # Get latest open time
                        latest_opens = open_data.groupby('user_email')['Open_Time'].max()
                        for email, open_time in latest_opens.items():
                            last_open_times[email.lower()] = open_time

                        # Get all subjects and preheaders from opened emails
                        if 'Subject' in perf_df.columns:
                            for email, group in open_data.groupby('user_email'):
                                email = email.lower()
                                subjects = group['Subject'].dropna().unique().tolist()
                                if email not in all_subjects_opened:
                                    all_subjects_opened[email] = subjects
                                else:
                                    # Append new subjects not already in the list
                                    all_subjects_opened[email].extend([s for s in subjects if s not in all_subjects_opened[email]])

                        if 'Preheader' in perf_df.columns:
                            for email, group in open_data.groupby('user_email'):
                                email = email.lower()
                                preheaders = group['Preheader'].dropna().unique().tolist()
                                if email not in all_preheaders_opened:
                                    all_preheaders_opened[email] = preheaders
                                else:
                                    # Append new preheaders not already in the list
                                    all_preheaders_opened[email].extend([p for p in preheaders if p not in all_preheaders_opened[email]])

                # Process click times - for each user, get the most recent click time and mail content
                if 'Click_Time' in perf_df.columns:
                    click_data = perf_df[perf_df['Click_Time'].notna()]

                    # Group by user_email and get the most recent Click_Time
                    if not click_data.empty:
                        # Get latest click time
                        latest_clicks = click_data.groupby('user_email')['Click_Time'].max().reset_index()

                        for _, row in latest_clicks.iterrows():
                            email = row['user_email'].lower()
                            click_time = row['Click_Time']
                            last_click_times[email] = click_time

                            # Get the mail content for the most recent click
                            if 'Mail_Content' in perf_df.columns:
                                # Find the row with the latest click time for this user
                                latest_click_row = click_data[
                                    (click_data['user_email'].str.lower() == email) &
                                    (click_data['Click_Time'] == click_time)
                                ].iloc[0] if not click_data[
                                    (click_data['user_email'].str.lower() == email) &
                                    (click_data['Click_Time'] == click_time)
                                ].empty else None

                                if latest_click_row is not None and pd.notna(latest_click_row.get('Mail_Content')):
                                    last_mail_content_clicked[email] = latest_click_row['Mail_Content']

            # Log summary of what we extracted
            logger.info(f"Extracted last open times for {len(last_open_times)} emails")
            logger.info(f"Extracted last click times for {len(last_click_times)} emails")
            logger.info(f"Extracted last send times for {len(last_send_times)} emails")
            logger.info(f"Extracted opened subjects for {len(all_subjects_opened)} emails")
            logger.info(f"Extracted opened preheaders for {len(all_preheaders_opened)} emails")
            logger.info(f"Extracted last clicked mail content for {len(last_mail_content_clicked)} emails")
            logger.info(f"Extracted total emails sent for {len(total_mails_sent)} emails")

            # Calculate most frequent open times for each user
            try:
                # Find the openers file
                openers_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                                          'data', 'mail_performance', 'openers', 'all_openers.csv')

                if os.path.exists(openers_file):
                    openers_df = pd.read_csv(openers_file)

                    # Ensure Open_Time is in datetime format
                    if 'Open_Time' in openers_df.columns:
                        openers_df['Open_Time'] = pd.to_datetime(openers_df['Open_Time'], errors='coerce')

                        # Extract hour from Open_Time
                        openers_df['Open_Hour'] = openers_df['Open_Time'].dt.hour

                        # Group by user_email and find most frequent open hour
                        for email, group in openers_df.groupby('user_email'):
                            if not group.empty and 'Open_Hour' in group.columns:
                                # Get the most common hour
                                hour_counts = group['Open_Hour'].value_counts()
                                if not hour_counts.empty:
                                    most_frequent_hour = hour_counts.index[0]
                                    # Format as HH:00
                                    most_frequent_open_times[email.lower()] = f"{most_frequent_hour:02d}:00"

                    logger.info(f"Found most frequent open times for {len(most_frequent_open_times)} users")
                else:
                    logger.warning(f"Openers file not found: {openers_file}")
            except Exception as e:
                logger.error(f"Error calculating most frequent open times: {str(e)}")

            return last_open_times, last_click_times, last_send_times, all_subjects_opened, all_preheaders_opened, last_mail_content_clicked, total_mails_sent, last_product_sent, most_frequent_open_times

        except Exception as e:
            logger.error(f"Error extracting mail performance data: {str(e)}")
            return {}, {}, {}, {}, {}, {}, {}, {}, {}


    def _preload_buyers_and_leads(self, buyers_files, leads_files):
        """Preload all buyers and leads data into memory to avoid repeated file reads."""
        # Load buyers data
        all_buyers = {}
        for i, file in enumerate(buyers_files):
            try:
                df = pd.read_csv(file)
                email_col = 'Email_ID' if 'Email_ID' in df.columns else 'email'

                if email_col in df.columns:
                    product_name = ""
                    if i == 0:  # BBBuyers.csv
                        product_name = 'Certified AI/ML BlackBelt Plus Program'
                    elif i == 1:  # GenAI Buyers.csv
                        product_name = 'GenAI Pinnacle Plus Program'
                    elif i == 2:  # AgenticAI Buyers.csv
                        product_name = 'Agentic AI Pioneer Program'

                    for email in df[email_col].values:
                        if email not in all_buyers:
                            all_buyers[email] = []
                        if product_name and product_name not in all_buyers[email]:
                            all_buyers[email].append(product_name)
            except Exception as e:
                logger.warning(f"Error loading buyer file {file}: {str(e)}")

        # Load leads data
        all_leads = {}
        for i, file in enumerate(leads_files):
            try:
                df = pd.read_csv(file)

                if 'email' in df.columns:
                    product_name = ""
                    if i == 0:  # AgenticAI_Leads.csv
                        product_name = 'Agentic AI Pioneer Program'
                    elif i == 1:  # BB_Leads.csv
                        product_name = 'Certified AI/ML BlackBelt Plus Program'
                    elif i == 2:  # Pinnacle_Leads.csv
                        product_name = 'GenAI Pinnacle Plus Program'

                    for email in df['email'].values:
                        if email not in all_leads:
                            all_leads[email] = []
                        if product_name and product_name not in all_leads[email]:
                            all_leads[email].append(product_name)
            except Exception as e:
                logger.warning(f"Error loading lead file {file}: {str(e)}")

        self.buyers_cache = all_buyers
        self.leads_cache = all_leads

        logger.info(f"Preloaded data for {len(all_buyers)} buyers and {len(all_leads)} leads")

    def _get_purchased_products(self, email):
        """Get list of products purchased by user using preloaded cache."""
        return self.buyers_cache.get(email, [])
        
    def _get_purchase_dates(self, email):
        """Get purchase dates for products purchased by user.
        
        Returns:
            dict: A dictionary mapping product names to purchase dates (datetime objects)
        """
        purchase_dates = {}
        
        try:
            # This is a placeholder for actual purchase date retrieval
            # In a real system, this would come from order history or transaction records
            # For now, we'll use a simple approach based on the buyers data
            
            products = self._get_purchased_products(email)
            if products:
                # Get current date to calculate relative purchase dates
                current_date = datetime.datetime.now()
                
                for product in products:
                    # For demo purposes, assign random purchase dates within the last 30 days
                    # In production, this should be replaced with actual purchase date data
                    days_ago = random.randint(1, 30)  # Random purchase within last 30 days
                    purchase_date = current_date - datetime.timedelta(days=days_ago)
                    purchase_dates[product] = purchase_date
            
        except Exception as e:
            logger.error(f"Error retrieving purchase dates for {email}: {str(e)}")
        
        return purchase_dates
        
    def _is_cooloff_period_over(self, product_name, purchase_date, product_cooloff_periods):
        """Check if the cool-off period has elapsed since purchase.
        
        Args:
            product_name: Name of the purchased product
            purchase_date: Date when the product was purchased
            product_cooloff_periods: Dictionary mapping product names to cool-off periods in days
            
        Returns:
            bool: True if cool-off period has elapsed, False otherwise
        """
        if product_name not in product_cooloff_periods:
            # If no cool-off period is defined, default to 7 days
            cooloff_days = 7
        else:
            cooloff_days = product_cooloff_periods[product_name]
        
        current_date = datetime.datetime.now()
        cooloff_end_date = purchase_date + datetime.timedelta(days=cooloff_days)
        
        # Return True if current date is after cooloff end date
        return current_date > cooloff_end_date

    def _get_lead_products(self, email):
        """Get list of products for which user is a lead using preloaded cache."""
        return self.leads_cache.get(email, [])

    def _determine_user_stage(self, email):
        """Determine user stage based on preloaded cache data."""
        # Check if user has purchased any product
        if email in self.buyers_cache and self.buyers_cache[email]:
            return 'Product Purchased'

        # Check if user is a lead for any product
        if email in self.leads_cache and self.leads_cache[email]:
            return 'Product Lead Generated'

        # Check if user has viewed product pages (will be determined in batch later)
        # For now, return a placeholder
        return 'TBD'

    def _clean_url(self, url):
        """Remove query parameters from URL."""
        if not url or not isinstance(url, str):
            return ""

        try:
            parsed_url = urlparse(url)
            clean_url = urlunparse((parsed_url.scheme, parsed_url.netloc,
                                  parsed_url.path, '', '', ''))
            return clean_url
        except Exception as e:
            logger.warning(f"Error cleaning URL {url}: {str(e)}")
            return url

    def _parse_client_data(self, data):
        """Parse the custom format of client_side_data column in brahma_data.csv."""
        try:
            if not isinstance(data, str):
                return {}

            # Remove the outer braces
            if data.startswith('{') and data.endswith('}'):
                data = data[1:-1]

            result = {}

            # Custom parser for the format key=value, handling nested structures
            def parse_pairs(s, start=0, depth=0):
                results = {}
                i = start
                key = None
                while i < len(s):
                    if s[i] == '=' and key is None:
                        key = s[start:i].strip()
                        start = i + 1
                    elif s[i] == '{' and key is not None:
                        # Start of a nested object
                        nested_dict, new_i = parse_pairs(s, i + 1, depth + 1)
                        results[key] = nested_dict
                        i = new_i
                        key = None
                        start = i + 1
                    elif s[i] == '}' and depth > 0:
                        # End of the current nested object
                        if key is not None:
                            results[key] = s[start:i].strip()
                        return results, i
                    elif s[i] == ',' and key is not None:
                        # End of a key-value pair
                        results[key] = s[start:i].strip()
                        key = None
                        start = i + 1
                    i += 1

                # Handle the last key-value pair if any
                if key is not None:
                    results[key] = s[start:].strip()

                return results, i

            # Parse the main string
            result, _ = parse_pairs(data)
            return result

        except Exception as e:
            logger.warning(f"Error parsing client data: {str(e)}")
            # Fallback to regex-based extraction for the most important fields
            try:
                email_match = re.search(r'useremail=([^,}]+)', data)
                url_match = re.search(r'currentpageuri=([^,}]+)', data)

                result = {}
                if email_match:
                    result['useremail'] = email_match.group(1).strip()
                if url_match:
                    result['currentpageuri'] = url_match.group(1).strip()

                return result
            except:
                return {}

    def _find_most_frequent_activity_time(self, brahma_data_path):
        """
        Find the most frequent activity time for each user from brahma_data.csv.

        Args:
            brahma_data_path (str): Path to brahma_data.csv file

        Returns:
            dict: Mapping of email to most frequent activity hour (format: "HH:00")
        """
        logger.info("Finding most frequent activity times from brahma_data.csv...")

        activity_times = {}

        try:
            # Check if file exists
            if not os.path.exists(brahma_data_path):
                logger.warning(f"Brahma data file not found: {brahma_data_path}")
                return {}

            # Read brahma data in chunks to handle large files
            chunk_size = 100000
            email_hour_counts = defaultdict(lambda: defaultdict(int))

            # Process in chunks
            for chunk in pd.read_csv(brahma_data_path, chunksize=chunk_size):
                # Check if current_server_time column exists
                if 'current_server_time' not in chunk.columns:
                    logger.warning("No current_server_time column found in brahma_data.csv")
                    return {}

                # Convert current_server_time to datetime
                chunk['current_server_time'] = pd.to_datetime(chunk['current_server_time'], errors='coerce')

                # Extract data from each row
                for _, row in chunk.iterrows():
                    # Skip rows with invalid current_server_time
                    if pd.isna(row['current_server_time']):
                        continue

                    # Get client data to extract email
                    client_data = self._parse_client_data(row['client_side_data'])
                    user_email = client_data.get('useremail', None)

                    # Skip if email is missing or invalid
                    if not user_email or not isinstance(user_email, str) or '@' not in user_email:
                        continue

                    # Extract hour from current_server_time
                    hour = row['current_server_time'].hour

                    # Increment count for this hour for this user
                    email_hour_counts[user_email.lower()][hour] += 1

            # Find most frequent hour for each user
            for email, hour_counts in email_hour_counts.items():
                if hour_counts:
                    # Get hour with highest count
                    most_frequent_hour = max(hour_counts.items(), key=lambda x: x[1])[0]
                    # Format as HH:00
                    activity_times[email] = f"{most_frequent_hour:02d}:00"

            logger.info(f"Found most frequent activity times for {len(activity_times)} users")
            return activity_times

        except Exception as e:
            logger.error(f"Error finding most frequent activity times: {str(e)}")
            return {}

    def _extract_email_urls_from_brahma_data(self, brahma_data_path):
        """
        Extract user emails, visited URLs, and page views from brahma_data.csv.
        Process in batch rather than per-user.
        """
        logger.info("Extracting email, URL, and page view data from brahma_data.csv...")

        try:
            # Load URL patterns to ignore
            ignore_patterns = []
            ignore_patterns_path = os.path.join(os.path.dirname(brahma_data_path), "urls_to_ignore.csv")

            # Compile a single regex pattern from all ignore patterns
            ignore_regex = None
            if os.path.exists(ignore_patterns_path):
                try:
                    # Read the ignore patterns file, accounting for header row
                    ignore_df = pd.read_csv(ignore_patterns_path)
                    # Get the column name (should be 'patterns_to_ignore')
                    column_name = ignore_df.columns[0]
                    # Extract patterns from the column
                    ignore_patterns = ignore_df[column_name].dropna().tolist()

                    # Escape special regex characters and create a single pattern with OR (|)
                    if ignore_patterns:
                        import re
                        escaped_patterns = [re.escape(pattern) for pattern in ignore_patterns]
                        ignore_regex = re.compile('|'.join(escaped_patterns))
                        logger.info(f"Compiled regex from {len(ignore_patterns)} URL patterns to ignore")
                except Exception as e:
                    logger.warning(f"Error loading URL ignore patterns: {str(e)}")

            # Read brahma data in chunks to handle large files
            chunk_size = 100000
            email_url_map = defaultdict(set)  # email -> set of URLs
            email_country_map = {}  # email -> country
            product_page_viewers = defaultdict(list)  # product URL -> list of emails
            processed_rows = 0
            unique_emails = set()
            unique_urls = set()
            ignored_urls_count = 0

            # Process in chunks
            for chunk in pd.read_csv(brahma_data_path, chunksize=chunk_size):
                logger.info(f"Processing {len(chunk)} rows in brahma_data.csv...")

                # Product URLs from product_details
                product_urls = [product['url'] for product in self.product_details if 'url' in product]

                # Extract data from each row
                for _, row in chunk.iterrows():
                    client_data = self._parse_client_data(row['client_side_data'])

                    # Get user email and current page
                    user_email = client_data.get('useremail', None)
                    current_page = client_data.get('currentpageuri', None)

                    # Skip if email or URL is missing
                    if not user_email or not current_page:
                        continue

                    # Skip emails that look invalid
                    if not isinstance(user_email, str) or '@' not in user_email:
                        continue

                    # Clean URL
                    clean_url = self._clean_url(current_page)
                    if not clean_url:
                        continue

                    # Check if URL matches any ignore pattern using regex
                    if ignore_regex and ignore_regex.search(clean_url):
                        ignored_urls_count += 1
                        continue

                    # Add to email->URL mapping
                    email_url_map[user_email].add(clean_url)
                    unique_emails.add(user_email)
                    unique_urls.add(clean_url)

                    # Check if user viewed any product pages
                    for product_url in product_urls:
                        if product_url in clean_url.lower():
                            product_page_viewers[product_url].append(user_email)

                    # Get country information from location_from_ip
                    if 'location_from_ip' in row and row['location_from_ip']:
                        try:
                            location_data = self._parse_client_data(row['location_from_ip'])
                            country = location_data.get('country_name', None)
                            if country and user_email not in email_country_map:
                                email_country_map[user_email] = country
                        except:
                            pass

                processed_rows += len(chunk)
                logger.info(f"Processed {processed_rows} rows, found {len(unique_emails)} unique users and {len(unique_urls)} URLs, ignored {ignored_urls_count} URLs")

            # Create DataFrame for all extracted data
            data_rows = []
            for email in email_url_map:
                urls = list(email_url_map[email])
                country = email_country_map.get(email, "Unknown")

                # Determine if user viewed any product pages
                viewed_products = []
                for product_url, viewers in product_page_viewers.items():
                    if email in viewers:
                        # Find the product name for this URL
                        for product in self.product_details:
                            if product.get('url', '') == product_url:
                                viewed_products.append(product.get('name', ''))

                # Generate row data
                row_data = {
                    'user_email': email,
                    'country_name': country,  # Changed from 'country' to 'country_name'
                    'visited_pages': urls,
                    'viewed_products': viewed_products,
                    'purchased_products': self._get_purchased_products(email),
                    'lead_products': self._get_lead_products(email)
                }
                data_rows.append(row_data)

            # Create DataFrame
            brahma_user_df = pd.DataFrame(data_rows)

            # If no data was extracted, log a warning
            if len(brahma_user_df) == 0:
                logger.warning("No user data extracted from brahma_data.csv")

            logger.info(f"Completed extraction: {len(brahma_user_df)} users, {len(unique_urls)} unique URLs, ignored {ignored_urls_count} URLs matching patterns")
            return brahma_user_df

        except Exception as e:
            logger.error(f"Error extracting data from brahma_data.csv: {str(e)}")
            return pd.DataFrame()

    async def _fetch_meta_data(self, session, url):
        """Fetch meta title and description for a URL."""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            async with session.get(url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    # Extract title
                    title_tag = soup.find('title')
                    title = title_tag.text.strip() if title_tag else f"Page about {url.split('/')[-1] if '/' in url else url}"

                    # Extract meta description
                    meta_desc = soup.find('meta', attrs={'name': 'description'})
                    description = meta_desc['content'].strip() if meta_desc and 'content' in meta_desc.attrs else f"Information related to {url.split('/')[-1] if '/' in url else url}"

                    return {
                        "title": title,
                        "description": description
                    }
                else:
                    return {
                        "title": f"Page about {url.split('/')[-1] if '/' in url else url}",
                        "description": f"Information related to {url.split('/')[-1] if '/' in url else url}"
                    }
        except Exception as e:
            logger.warning(f"Error fetching {url}: {str(e)}")
            return {
                "title": f"Page about {url.split('/')[-1] if '/' in url else url}",
                "description": f"Information related to {url.split('/')[-1] if '/' in url else url}"
            }

    async def _fetch_meta_data_batch(self, urls):
        """Fetch meta data for multiple URLs in parallel, with caching support."""
        # Path for metadata cache file
        meta_cache_path = "Sample Data For Mass Generation/url_metadata_cache.csv"

        # Load existing cache if available
        url_metadata_cache = {}
        urls_to_fetch = set(urls)

        try:
            if os.path.exists(meta_cache_path):
                cache_df = pd.read_csv(meta_cache_path)

                # Convert to dictionary for faster lookup
                for _, row in cache_df.iterrows():
                    url = row['url']
                    if url in urls_to_fetch:
                        url_metadata_cache[url] = {
                            "title": row['title'],
                            "description": row['description']
                        }
                        urls_to_fetch.remove(url)

                logger.info(f"Loaded metadata for {len(url_metadata_cache)} URLs from cache")
                logger.info(f"Remaining URLs to fetch: {len(urls_to_fetch)}")
        except Exception as e:
            logger.warning(f"Error loading metadata cache: {str(e)}")

        # If all URLs are cached, return immediately
        if not urls_to_fetch:
            logger.info("All URLs found in cache, no need to fetch")
            return url_metadata_cache

        # Create batches of URLs to avoid overwhelming the server
        batch_size = 100  # Increased to 1000 as requested
        urls_to_fetch_list = list(urls_to_fetch)
        batches = [urls_to_fetch_list[i:i + batch_size] for i in range(0, len(urls_to_fetch_list), batch_size)]

        all_meta_data = url_metadata_cache.copy()  # Start with cached data
        url_count = len(url_metadata_cache)

        # Process each batch
        for batch in batches:
            async with aiohttp.ClientSession() as session:
                tasks = []
                for url in batch:
                    tasks.append((url, self._fetch_meta_data(session, url)))

                # Execute tasks concurrently
                for url, task in tasks:
                    try:
                        result = await task
                        all_meta_data[url] = result
                    except Exception as e:
                        logger.warning(f"Error fetching metadata for URL: {url} - {str(e)}")
                        # Add placeholder for failed fetch
                        all_meta_data[url] = {
                            "title": f"Page about {url.split('/')[-1] if '/' in url else url}",
                            "description": f"Information related to {url.split('/')[-1] if '/' in url else url}"
                        }

                # Update progress
                url_count += len(batch)
                logger.info(f"Fetched metadata for {url_count}/{len(urls)} URLs")

        # Save updated cache to CSV
        try:
            # Convert dictionary to dataframe and save
            cache_data = []
            for url, metadata in all_meta_data.items():
                cache_data.append({
                    'url': url,
                    'title': metadata['title'],
                    'description': metadata['description']
                })

            cache_df = pd.DataFrame(cache_data)
            cache_df.to_csv(meta_cache_path, index=False)
            logger.info(f"Saved metadata cache for {len(all_meta_data)} URLs")
        except Exception as e:
            logger.warning(f"Error saving metadata cache: {str(e)}")

        return all_meta_data

    def _create_user_metadata_map(self, email_url_map, meta_data):
        """Create mappings from emails to aggregated metadata."""
        email_titles_map = defaultdict(list)
        email_descriptions_map = defaultdict(list)

        for email, urls in email_url_map.items():
            if not isinstance(urls, (list, set)):  # Skip if urls is not iterable
                continue

            for url in urls:
                if url in meta_data:
                    title = meta_data[url]["title"]
                    desc = meta_data[url]["description"]

                    if title and title not in email_titles_map[email]:
                        email_titles_map[email].append(title)

                    if desc and desc not in email_descriptions_map[email]:
                        email_descriptions_map[email].append(desc)

        return dict(email_titles_map), dict(email_descriptions_map)

    def _get_product_visitors(self, product_page_viewers):
        """Get mapping of emails to visited products."""
        # Create mapping from email to list of visited products
        email_visited_products = defaultdict(list)

        # Map product URLs to names
        product_url_to_name = {
            product['url']: product['name']
            for product in self.product_details
            if 'url' in product and 'name' in product
        }

        # For each product URL that has viewers
        for product_url, viewers in product_page_viewers.items():
            # Find the matching product name
            for url_pattern, product_name in product_url_to_name.items():
                if url_pattern in product_url:
                    # Add this product to the list for each viewer
                    for email in viewers:
                        if product_name not in email_visited_products[email]:
                            email_visited_products[email].append(product_name)

        return dict(email_visited_products)

    def _process_brahma_data(self, brahma_data_path):
        """
        Process brahma_data.csv to create a user-centric CSV file.
        """
        logger.info("Creating brahma_data_by_user.csv...")

        try:
            # Extract emails, URLs, and page views
            brahma_user_df = self._extract_email_urls_from_brahma_data(brahma_data_path)

            if brahma_user_df.empty:
                logger.warning("No user data extracted from brahma_data.csv")
                return pd.DataFrame(columns=['user_email', 'country_name', 'Meta title', 'Meta description',
                                             'Purchased_product_list', 'Lead_product_list', 'Visited_product_list'])

            # Collect all unique URLs
            all_urls = set()
            for urls in brahma_user_df['visited_pages'].values:
                all_urls.update(urls)

            unique_urls = list(all_urls)
            logger.info(f"Found {len(unique_urls)} unique URLs")

            # Fetch meta data for ALL URLs, not just a sample
            logger.info(f"Fetching metadata for {len(unique_urls)} URLs")
            meta_data = asyncio.run(self._fetch_meta_data_batch(unique_urls))

            # Create email to URLs mapping
            email_url_map = {}
            for _, row in brahma_user_df.iterrows():
                email_url_map[row['user_email']] = row['visited_pages']

            # Create mappings from emails to aggregated metadata
            email_titles_map, email_descriptions_map = self._create_user_metadata_map(email_url_map, meta_data)

            # Get product visitors mapping
            email_visited_products = {}
            for _, row in brahma_user_df.iterrows():
                email_visited_products[row['user_email']] = row['viewed_products']

            # Create DataFrame with all the data
            user_data = []

            # Use all emails from brahma_user_df
            for _, row in brahma_user_df.iterrows():
                email = row['user_email']
                user_data.append({
                    'user_email': email,
                    'country_name': row['country_name'],
                    'Meta title': email_titles_map.get(email, []),
                    'Meta description': email_descriptions_map.get(email, []),
                    'Purchased_product_list': row['purchased_products'],
                    'Lead_product_list': row['lead_products'],
                    'Visited_product_list': row['viewed_products']
                })

            result_df = pd.DataFrame(user_data)

            # Save the file
            out_path = os.path.join(os.path.dirname(brahma_data_path), 'brahma_data_by_user.csv')
            result_df.to_csv(out_path, index=False)

            return result_df

        except Exception as e:
            logger.error(f"Error creating brahma_data_by_user: {str(e)}")
            return pd.DataFrame()

    def _generate_user_behaviour_batch(self, target_emails, brahma_user_df, progress_callback=None,
                                engagement_data=None):
        """
        Generate user_behaviour strings for multiple users in batch.

        Args:
            target_emails (list): List of user emails to generate behavior for
            brahma_user_df (pandas.DataFrame): User-centric brahma data
            progress_callback (callable, optional): Function to call for progress updates
            engagement_data (dict, optional): Dict containing email engagement metrics data

        Returns:
            dict: Mapping of emails to behavior strings
        """
        logger.info(f"Generating user behavior for {len(target_emails)} users...")

        # Check if brahma_user_df is empty or doesn't have the expected columns
        if brahma_user_df.empty or 'user_email' not in brahma_user_df.columns:
            logger.warning("No behavior data available - using placeholder for all users")
            # Instead of just returning a placeholder, we'll create behavior strings with engagement metrics
            return self._generate_basic_behavior_with_engagement(target_emails, engagement_data)

        # Create a dictionary for fast lookups
        behavior_dict = {}

        # Get all emails that exist in the brahma data
        existing_emails = set(brahma_user_df['user_email'].values)

        # For emails not in brahma data, generate basic behavior with engagement data
        missing_emails = set(target_emails) - existing_emails
        missing_behaviors = self._generate_basic_behavior_with_engagement(missing_emails, engagement_data)
        behavior_dict.update(missing_behaviors)

        # Process emails that exist in brahma data
        existing_target_emails = set(target_emails) & existing_emails

        # Required columns for generating behavior
        required_columns = ['country_name', 'Meta title', 'Meta description', 'Purchased_product_list', 'Lead_product_list', 'Visited_product_list']

        # Check if all required columns exist in the dataframe
        missing_columns = [col for col in required_columns if col not in brahma_user_df.columns]
        if missing_columns:
            logger.warning(f"Missing columns in brahma_user_df: {missing_columns}")
            # Add placeholder data for emails in brahma_user_df
            for email in existing_target_emails:
                behavior_dict[email] = "Behaviour data incomplete due to missing columns"
            return behavior_dict

        # For each email in both sets, generate behavior
        for email in existing_target_emails:
            try:
                # Get user row from brahma_user_df
                user_row = brahma_user_df[brahma_user_df['user_email'] == email]

                if user_row.empty:
                    behavior_dict[email] = "Behaviour data not found"
                    continue

                # Extract data with error handling
                try:
                    country = user_row['country_name'].iloc[0] if 'country_name' in user_row.columns else "Unknown"
                    meta_titles = user_row['Meta title'].iloc[0] if 'Meta title' in user_row.columns else []
                    meta_descriptions = user_row['Meta description'].iloc[0] if 'Meta description' in user_row.columns else []
                    purchased_products = user_row['Purchased_product_list'].iloc[0] if 'Purchased_product_list' in user_row.columns else []
                    lead_products = user_row['Lead_product_list'].iloc[0] if 'Lead_product_list' in user_row.columns else []
                    visited_products = user_row['Visited_product_list'].iloc[0] if 'Visited_product_list' in user_row.columns else []

                    # Handle case where any of these might be strings instead of lists
                    if isinstance(meta_titles, str):
                        try:
                            meta_titles = ast.literal_eval(meta_titles)
                        except:
                            meta_titles = [meta_titles]

                    if isinstance(meta_descriptions, str):
                        try:
                            meta_descriptions = ast.literal_eval(meta_descriptions)
                        except:
                            meta_descriptions = [meta_descriptions]

                    if isinstance(purchased_products, str):
                        try:
                            purchased_products = ast.literal_eval(purchased_products)
                        except:
                            purchased_products = [purchased_products] if purchased_products else []

                    if isinstance(lead_products, str):
                        try:
                            lead_products = ast.literal_eval(lead_products)
                        except:
                            lead_products = [lead_products] if lead_products else []

                    if isinstance(visited_products, str):
                        try:
                            visited_products = ast.literal_eval(visited_products)
                        except:
                            visited_products = [visited_products] if visited_products else []

                    # Ensure all are lists
                    meta_titles = meta_titles if isinstance(meta_titles, list) else []
                    meta_descriptions = meta_descriptions if isinstance(meta_descriptions, list) else []
                    purchased_products = purchased_products if isinstance(purchased_products, list) else []
                    lead_products = lead_products if isinstance(lead_products, list) else []
                    visited_products = visited_products if isinstance(visited_products, list) else []

                except Exception as e:
                    logger.error(f"Error extracting data for {email}: {str(e)}")
                    behavior_dict[email] = "Error extracting user data"
                    continue

                # Build comprehensive user_behaviour string
                user_behaviour = ""

                # Add name and country information
                user_first_name = self._get_first_name(email)
                if country and country != "Unknown":
                    user_behaviour += f"{user_first_name} is from {country}. "

                # Add purchased products information (highest priority)
                if purchased_products:
                    products_str = ", ".join(purchased_products)
                    user_behaviour += f"{user_first_name} has purchased {products_str}. "

                # Add lead products information (second priority)
                if lead_products:
                    products_str = ", ".join(lead_products)
                    if purchased_products:
                        user_behaviour += f"{user_first_name} has also shown interest in {products_str}. "
                    else:
                        user_behaviour += f"{user_first_name} has shown interest in {products_str}. "

                # Add product page visit information (third priority)
                if visited_products:
                    products_str = ", ".join(visited_products)
                    if purchased_products or lead_products:
                        user_behaviour += f"{user_first_name} has also visited the product pages for {products_str}. "
                    else:
                        user_behaviour += f"{user_first_name} has visited the product pages for {products_str}. "

                # Add browsing content information
                if meta_titles:
                    # Select up to 5 most relevant titles
                    relevant_titles = meta_titles[:5]
                    titles_str = ", ".join([f'"{title}"' for title in relevant_titles])
                    user_behaviour += f"{user_first_name} has been browsing content about {titles_str}. "

                # Add engagement status if available from target audience data
                engagement_status = None
                user_row = brahma_user_df[brahma_user_df['user_email'] == email]
                if not user_row.empty and 'engagement_status' in brahma_user_df.columns:
                    engagement_status = user_row['engagement_status'].iloc[0]

                # Add email engagement metrics if available
                if engagement_data and all(key in engagement_data for key in ['last_open_times', 'last_click_times', 'last_send_times']):
                    email_lower = email.lower()
                    current_time = datetime.datetime.now()

                    # Add engagement status to behavior string if available
                    if engagement_status:
                        if engagement_status == "active":
                            status_str = "an active subscriber"
                        elif engagement_status == "non_active":
                            status_str = "a non-active subscriber"
                        else:  # new
                            status_str = "a new subscriber who hasn't received any emails yet"
                        user_behaviour += f"{user_first_name} is {status_str}. "

                    # Add last open information
                    if email_lower in engagement_data['last_open_times'] and engagement_data['last_open_times'][email_lower]:
                        last_open = engagement_data['last_open_times'][email_lower]
                        try:
                            # Handle different timestamp types
                            if isinstance(last_open, str):
                                last_open_dt = datetime.datetime.strptime(last_open, '%Y-%m-%d %H:%M:%S')
                            elif isinstance(last_open, pd.Timestamp):
                                last_open_dt = last_open.to_pydatetime()
                            elif isinstance(last_open, datetime.datetime):
                                last_open_dt = last_open
                            else:
                                raise ValueError(f"Unexpected timestamp type: {type(last_open)}")

                            # Ensure timezone-naive
                            if last_open_dt.tzinfo is not None:
                                last_open_dt = last_open_dt.replace(tzinfo=None)

                            time_diff = current_time - last_open_dt
                            days_diff = time_diff.days

                            if days_diff == 0:
                                if time_diff.seconds < 3600:
                                    time_msg = f"less than an hour ago"
                                else:
                                    hours = time_diff.seconds // 3600
                                    time_msg = f"{hours} hour{'s' if hours > 1 else ''} ago"
                                user_behaviour += f"{user_first_name} last opened an email {time_msg}. "
                            elif days_diff == 1:
                                user_behaviour += f"{user_first_name} last opened an email yesterday. "
                            else:
                                user_behaviour += f"{user_first_name} last opened an email {days_diff} days ago. "
                        except Exception as e:
                            logger.warning(f"Error processing last open time for {email}: {str(e)}")
                    # Add last click information
                    if email_lower in engagement_data['last_click_times'] and engagement_data['last_click_times'][email_lower]:
                        last_click = engagement_data['last_click_times'][email_lower]
                        try:
                            # Handle different timestamp types
                            if isinstance(last_click, str):
                                last_click_dt = datetime.datetime.strptime(last_click, '%Y-%m-%d %H:%M:%S')
                            elif isinstance(last_click, pd.Timestamp):
                                last_click_dt = last_click.to_pydatetime()
                            elif isinstance(last_click, datetime.datetime):
                                last_click_dt = last_click
                            else:
                                raise ValueError(f"Unexpected timestamp type: {type(last_click)}")

                            # Ensure timezone-naive
                            if last_click_dt.tzinfo is not None:
                                last_click_dt = last_click_dt.replace(tzinfo=None)

                            time_diff = current_time - last_click_dt
                            days_diff = time_diff.days

                            if days_diff == 0:
                                if time_diff.seconds < 3600:
                                    time_msg = f"less than an hour ago"
                                else:
                                    hours = time_diff.seconds // 3600
                                    time_msg = f"{hours} hour{'s' if hours > 1 else ''} ago"
                                user_behaviour += f"{user_first_name} last clicked on an email link {time_msg}. "
                            elif days_diff == 1:
                                user_behaviour += f"{user_first_name} last clicked on an email link yesterday. "
                            else:
                                user_behaviour += f"{user_first_name} last clicked on an email link {days_diff} days ago. "
                        except Exception as e:
                            logger.warning(f"Error processing last click time for {email}: {str(e)}")

                    # Add email content clicked if available
                    if email_lower in engagement_data['last_mail_content_clicked'] and engagement_data['last_mail_content_clicked'][email_lower]:
                        content_clicked = engagement_data['last_mail_content_clicked'][email_lower]
                        user_behaviour += f"They last clicked on content about '{content_clicked}'. "

                    # Add total emails sent if available
                    if email_lower in engagement_data['total_mails_sent'] and engagement_data['total_mails_sent'][email_lower]:
                        total_sent = engagement_data['total_mails_sent'][email_lower]
                        user_behaviour += f"{user_first_name} has received {total_sent} emails from us. "

                # Add recommendation component based on behavior
                if lead_products:
                    # If user has shown interest but not purchased, encourage purchase
                    lead_set = set(lead_products)
                    top_recommendation = next(iter(lead_set)) if lead_set else "our AI/ML programs"
                    user_behaviour += f"We should encourage {user_first_name} to complete their purchase of {top_recommendation}. "
                elif visited_products:
                    # If user has only visited product pages, suggest more information
                    visited_set = set(visited_products)
                    top_visited = next(iter(visited_set)) if visited_set else "our AI/ML programs"
                    user_behaviour += f"We should provide {user_first_name} with more detailed information about {top_visited}. "

                behavior_dict[email] = user_behaviour

            except Exception as e:
                logger.error(f"Error generating user behavior for {email}: {str(e)}")
                behavior_dict[email] = "Error generating behavior data"

        return behavior_dict

    def assign_send_times(self, target_df, campaign_settings, most_frequent_open_times, most_frequent_activity_times):
        """
        Assign send times to users based on campaign settings and user activity patterns.

        Args:
            target_df (pd.DataFrame): DataFrame containing target audience data
            campaign_settings (dict): Campaign settings from campaignSettings.json
            most_frequent_open_times (dict): Mapping of email to most frequent open time
            most_frequent_activity_times (dict): Mapping of email to most frequent activity time

        Returns:
            pd.DataFrame: DataFrame with Send_Time column added
        """
        logger.info("Assigning send times based on campaign settings and user activity patterns")

        # Make a copy of the DataFrame to avoid modifying the original
        result_df = target_df.copy()

        # Get time settings from campaign settings
        time_settings_active = campaign_settings.get('time_settings_active', {'mode': 'best_time', 'custom_times': []})
        time_settings_non_active = campaign_settings.get('time_settings_non_active', {'mode': 'custom', 'custom_times': []})

        # Ensure custom_times is not empty
        if not time_settings_active.get('custom_times') and time_settings_active.get('mode') == 'custom':
            time_settings_active['custom_times'] = ['09:00', '18:00']

        if not time_settings_non_active.get('custom_times'):
            time_settings_non_active['custom_times'] = ['09:00', '18:00']

        # Get current date
        current_date = datetime.datetime.now().date()

        # Add Send_Time column
        result_df['Send_Time'] = None

        # Process each user
        for idx, row in result_df.iterrows():
            email = row['user_email'].lower()

            # Determine if user is active based on engagement_status
            is_active = row.get('engagement_status') == 'active'

            # Assign time based on user status and settings
            if is_active and time_settings_active.get('mode') == 'best_time':
                # For active users with best_time mode, use most frequent open time if available
                if email in most_frequent_open_times:
                    time_str = most_frequent_open_times[email]
                    logger.debug(f"Using most frequent open time for {email}: {time_str}")
                # Otherwise, try to use most frequent activity time from brahma_data
                elif email in most_frequent_activity_times:
                    time_str = most_frequent_activity_times[email]
                    logger.debug(f"Using most frequent activity time for {email}: {time_str}")
                # If neither is available, use a random custom time
                else:
                    custom_times = time_settings_active.get('custom_times', ['09:00', '18:00'])
                    time_str = random.choice(custom_times)
                    logger.debug(f"Using random custom time for active user {email}: {time_str}")
            else:
                # For non-active users or active users with custom mode, use a random custom time
                if is_active:
                    custom_times = time_settings_active.get('custom_times', ['09:00', '18:00'])
                else:
                    custom_times = time_settings_non_active.get('custom_times', ['09:00', '18:00'])

                time_str = random.choice(custom_times)
                logger.debug(f"Using random custom time for {'active' if is_active else 'non-active'} user {email}: {time_str}")

            # Convert time string to datetime
            try:
                hour, minute = map(int, time_str.split(':'))
                send_time = datetime.datetime.combine(current_date, datetime.time(hour, minute))
                result_df.at[idx, 'Send_Time'] = send_time
            except Exception as e:
                logger.error(f"Error setting send time for {email}: {str(e)}")
                # Use default time (9 AM)
                send_time = datetime.datetime.combine(current_date, datetime.time(9, 0))
                result_df.at[idx, 'Send_Time'] = send_time

        logger.info(f"Assigned send times for {len(result_df)} users")
        return result_df

    def process_target_audience(self, target_audience, buyers_files, leads_files, brahma_data_path, progress_callback=None, engagement_data=None, campaign_settings_path=None):
        """
        Process target audience data.

        Args:
            target_audience: Either a file path to the CSV or a pre-filtered DataFrame
            buyers_files: List of buyers files
            leads_files: List of leads files
            brahma_data_path: Path to brahma data
            progress_callback: Callback function for progress updates
            engagement_data: Dict containing email engagement metrics
            campaign_settings_path: Path to campaign settings JSON file

        Returns:
            DataFrame with processed user data
        """
        # Load product details to check for cross-sell availability
        product_details_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'data', 'product_details.json')
        data_dir = os.path.dirname(product_details_path)
        product_cross_sell_map = {}
        product_cooloff_periods = {}
        
        try:
            if os.path.exists(product_details_path):
                with open(product_details_path, 'r') as f:
                    product_details = json.load(f)
                    
                # Create a map of products with cross-sell available
                for product in product_details:
                    product_name = product.get('Product_Name')
                    cross_sell_product = product.get('Cross_Sell_Product')
                    if product_name and cross_sell_product:
                        product_cross_sell_map[product_name] = cross_sell_product
                        
                        # Load user journey to get cool-off period
                        journey_file = os.path.join(data_dir, 'user_journeys', f'{product_name}_user_journey.json')
                        try:
                            if os.path.exists(journey_file):
                                with open(journey_file, 'r') as jf:
                                    journey_data = json.load(jf)
                                    
                                # Find the Cool Off Period stage
                                for stage in journey_data:
                                    if stage.get('current_stage') == 'Cool Off Period':
                                        product_cooloff_periods[product_name] = stage.get('days_to_wait', 7)  # Default to 7 days if not specified
                                        break
                            else:
                                logger.warning(f"User journey file not found for product: {product_name}")
                        except Exception as e:
                            logger.error(f"Error reading user journey for {product_name}: {str(e)}")
                
                logger.info(f"Loaded cross-sell information for {len(product_cross_sell_map)} products with {len(product_cooloff_periods)} cool-off periods defined")
        except Exception as e:
            logger.error(f"Error loading product details for cross-sell check: {str(e)}")
            product_cross_sell_map = {}
            product_cooloff_periods = {}

        logger.info("Processing target audience...")

        try:
            # If campaign_settings_path is provided, apply engagement-based filtering
            if campaign_settings_path:
                if progress_callback:
                    progress_callback("Filtering audience based on engagement", 15)

                # Select audience based on engagement patterns
                target_audience_df = self.select_audience_by_engagement(target_audience, campaign_settings_path)
                if target_audience_df.empty:
                    logger.warning("No users selected after engagement filtering")
                    return pd.DataFrame(columns=['user_email', 'first_name', 'user_behaviour', 'user_stage', 'engagement_status'])

                logger.info(f"Using engagement-filtered audience with {len(target_audience_df)} records")
            else:
                # Read target audience if a string (file path) is provided without engagement filtering
                if isinstance(target_audience, str):
                    target_audience_df = pd.read_csv(target_audience)
                    logger.info(f"Loaded {len(target_audience_df)} records from {target_audience}")
                else:
                    # Use the pre-filtered DataFrame directly
                    target_audience_df = target_audience.copy()
                    logger.info(f"Using pre-filtered target audience with {len(target_audience_df)} records")

            # Continue with the rest of the processing...

            # Update progress
            if progress_callback:
                progress_callback("Reading target audience data", 25)

            # Read target audience
            target_emails = target_audience_df['user_email'].values
            logger.info(f"Found {len(target_emails)} target users")

            # Preload buyers and leads data
            self._preload_buyers_and_leads(buyers_files, leads_files)

            # Create brahma_data_by_user.csv with batch processing
            brahma_user_df = self._process_brahma_data(brahma_data_path)

            # Generate user behavior for all targets in batch
            user_behaviors = self._generate_user_behaviour_batch(target_emails, brahma_user_df, engagement_data=engagement_data, progress_callback=progress_callback)

            # Create result dataframe efficiently
            result_data = []
            for email in target_emails:
                # Get pre-computed data
                # Check if first_name is already available in target_audience_df
                first_name = ""
                if 'first_name' in target_audience_df.columns:
                    user_row = target_audience_df[target_audience_df['user_email'] == email]
                    if not user_row.empty and pd.notna(user_row['first_name'].iloc[0]) and user_row['first_name'].iloc[0].strip() != "":
                        first_name = user_row['first_name'].iloc[0]
                
                # Only extract from email if first_name is not already present
                if not first_name:
                    first_name = self._get_first_name(email)
                    
                user_behaviour = user_behaviors.get(email, "Behaviour data not found")

                # Determine user stage based on preloaded data
                user_stage = self._determine_user_stage(email)

                # Override user_stage if it's still TBD (could not be determined from cache)
                if user_stage == 'TBD':
                    # Check if user viewed product pages (from brahma_user_df)
                    if not brahma_user_df.empty and 'user_email' in brahma_user_df.columns:
                        user_row = brahma_user_df[brahma_user_df['user_email'] == email]
                        if not user_row.empty and 'Visited_product_list' in user_row.columns:
                            visited_products = user_row['Visited_product_list'].iloc[0]
                            if isinstance(visited_products, str):
                                try:
                                    visited_products = ast.literal_eval(visited_products)
                                except:
                                    visited_products = []

                            if visited_products:
                                user_stage = 'Product Page Viewed'
                            else:
                                user_stage = 'New Visitor'
                        else:
                            user_stage = 'New Visitor'
                    else:
                        user_stage = 'New Visitor'

                # Get engagement status if available
                engagement_status = target_audience_df.loc[target_audience_df['user_email'] == email, 'engagement_status'].iloc[0] \
                    if 'engagement_status' in target_audience_df.columns and not target_audience_df[target_audience_df['user_email'] == email].empty else None

                # Get the last product sent to this user if available
                last_product = None
                if 'last_product_sent' in engagement_data and email.lower() in engagement_data['last_product_sent']:
                    last_product = engagement_data['last_product_sent'][email.lower()]
                
                # Check if user has purchased any products with cross-sell available
                # Get all purchased products for this user
                purchased_products = self._get_purchased_products(email)
                cross_sell_available = False
                cross_sell_product = None
                
                # Get purchase dates for tracking cool-off periods
                purchase_dates = self._get_purchase_dates(email)
                
                # For each purchased product, check if cross-sell is available and cool-off period is over
                for product_name in purchased_products:
                    if product_name in product_cross_sell_map:
                        # Get purchase date for this product
                        purchase_date = purchase_dates.get(product_name)
                        
                        if purchase_date and self._is_cooloff_period_over(product_name, purchase_date, product_cooloff_periods):
                            # Cool-off period is over, can proceed with cross-sell
                            cross_sell_available = True
                            cross_sell_product = product_cross_sell_map[product_name]
                            logger.debug(f"User {email} eligible for cross-sell from {product_name} to {cross_sell_product}")
                            break
                        elif purchase_date:
                            # Still in cool-off period
                            cooloff_days = product_cooloff_periods.get(product_name, 7)
                            cooloff_end = purchase_date + datetime.timedelta(days=cooloff_days)
                            logger.debug(f"User {email} still in cool-off period for {product_name} until {cooloff_end.strftime('%Y-%m-%d')}")
                        else:
                            logger.debug(f"Could not determine purchase date for {product_name} purchased by {email}")


                # For users with purchased products but not eligible for cross-sell (still in cool-off),
                # don't include them in the result
                skip_user = False
                
                # If user has purchased products but cross_sell_available is False,
                # it means they're still in cool-off period, so skip them
                if purchased_products and not cross_sell_available:
                    logger.info(f"User {email} has purchased products but is still in cool-off period - excluding from audience")
                    skip_user = True
                
                # Only add eligible users to results
                if not skip_user:
                    result_data.append({
                        'user_email': email,
                        'first_name': first_name,
                        'user_behaviour': user_behaviour,
                        'user_stage': user_stage,
                        'engagement_status': engagement_status,
                        'last_product_sent': last_product,
                        'Cross_Sell': cross_sell_available,
                        'Cross_Sell_Product': cross_sell_product
                    })

            return pd.DataFrame(result_data)

        except Exception as e:
            logger.error(f"Error processing target audience: {str(e)}")
            return pd.DataFrame(columns=['user_email', 'first_name', 'user_behaviour', 'user_stage', 'engagement_status', 'last_product_sent', 'Cross_Sell', 'Cross_Sell_Product'])

    def select_audience_by_engagement(self, target_audience, campaign_settings_path=None):
        """
        Select target audience based on email engagement patterns and campaign settings.

        Args:
            target_audience: Either a file path to the CSV or a pre-filtered DataFrame
            campaign_settings_path (str, optional): Path to campaign settings JSON file

        Returns:
            DataFrame: Combined audience with engagement_status column
        """
        logger.info("Selecting audience based on engagement patterns...")

        try:
            # Load target audience
            if isinstance(target_audience, str):
                if not os.path.exists(target_audience):
                    logger.error(f"Target audience file not found: {target_audience}")
                    return pd.DataFrame()

                target_df = pd.read_csv(target_audience)
                logger.info(f"Loaded {len(target_df)} records from {target_audience}")
            else:
                # Use the pre-filtered DataFrame directly
                target_df = target_audience.copy()
                logger.info(f"Using pre-filtered target audience with {len(target_df)} records")

            if 'user_email' not in target_df.columns:
                logger.error(f"'user_email' column not found in target audience file")
                return pd.DataFrame()

            # Standardize email format
            target_df['user_email'] = target_df['user_email'].str.lower()

            # Load campaign settings
            campaign_settings = {}
            if campaign_settings_path and os.path.exists(campaign_settings_path):
                try:
                    with open(campaign_settings_path, 'r') as f:
                        campaign_settings = json.load(f)
                except Exception as e:
                    logger.warning(f"Error loading campaign settings: {str(e)}")

            # Set default frequency values if not in settings
            active_frequency = campaign_settings.get('active_audience_frequency', 'moderate')
            non_active_frequency = campaign_settings.get('non_active_audience_frequency', 'defensive')

            # Map frequency to days between emails
            frequency_map = {
                'aggressive': 2,  # 3 mails/week
                'moderate': 3,   # 2 mails/week
                'defensive': 7    # 1 mail/week
            }

            active_days = frequency_map.get(active_frequency, 3)
            non_active_days = frequency_map.get(non_active_frequency, 7)

            logger.info(f"Using active frequency: {active_frequency} ({active_days} days)")
            logger.info(f"Using non-active frequency: {non_active_frequency} ({non_active_days} days)")

            # Get email performance data
            last_open_times, last_click_times, last_send_times, _, _, _, _, last_product_sent, most_frequent_open_times = self._extract_mail_performance_data()

            logger.info(f"Found {len(last_open_times)} emails with open data")
            logger.info(f"Found {len(last_click_times)} emails with click data")
            logger.info(f"Found {len(last_send_times)} emails with send data")

            # We already have current_time set above (as timezone-naive)

            # Convert string timestamps to datetime objects (ensuring timezone consistency)
            current_time = datetime.datetime.now()
            # Make sure current_time is timezone-naive for consistent comparisons
            current_time = current_time.replace(tzinfo=None)

            parsed_open_times = {}
            parsing_errors = 0
            for email, timestamp in last_open_times.items():
                try:
                    # Handle different timestamp types
                    if isinstance(timestamp, str):
                        # Parse string timestamps
                        dt = datetime.datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    elif isinstance(timestamp, pd.Timestamp):
                        # Convert pandas Timestamp to datetime
                        dt = timestamp.to_pydatetime()
                    elif isinstance(timestamp, datetime.datetime):
                        # Already a datetime
                        dt = timestamp
                    else:
                        # Skip unknown types
                        parsing_errors += 1
                        logger.warning(f"Unexpected type for open time: {type(timestamp)}")
                        continue

                    # Ensure it's timezone-naive
                    if dt.tzinfo is not None:
                        dt = dt.replace(tzinfo=None)

                    parsed_open_times[email] = dt

                except Exception as e:
                    parsing_errors += 1
                    logger.warning(f"Error parsing open time for {email}: {str(e)}. Value: {timestamp}")
                    # Skip this email if we can't parse the timestamp
                    continue

            logger.info(f"Parsed {len(parsed_open_times)} open times with {parsing_errors} errors")

            parsed_send_times = {}
            parsing_errors = 0
            for email, timestamp in last_send_times.items():
                try:
                    # Handle different timestamp types
                    if isinstance(timestamp, str):
                        # Parse string timestamps
                        dt = datetime.datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    elif isinstance(timestamp, pd.Timestamp):
                        # Convert pandas Timestamp to datetime
                        dt = timestamp.to_pydatetime()
                    elif isinstance(timestamp, datetime.datetime):
                        # Already a datetime
                        dt = timestamp
                    else:
                        # Skip unknown types
                        parsing_errors += 1
                        logger.warning(f"Unexpected type for send time: {type(timestamp)}")
                        continue

                    # Ensure it's timezone-naive
                    if dt.tzinfo is not None:
                        dt = dt.replace(tzinfo=None)

                    parsed_send_times[email] = dt

                except Exception as e:
                    parsing_errors += 1
                    logger.warning(f"Error parsing send time for {email}: {str(e)}. Value: {timestamp}")
                    # Skip this email if we can't parse the timestamp
                    continue

            logger.info(f"Parsed {len(parsed_send_times)} send times with {parsing_errors} errors")

            # Initialize empty DataFrames for active and non-active users
            active_audience = []
            non_active_audience = []

            # Identify emails that have never been sent any emails
            all_target_emails = set(target_df['user_email'].unique())
            all_sent_emails = set(parsed_send_times.keys())
            logger.info(f"Total target emails: {len(all_target_emails)}")
            logger.info(f"Total emails with send data: {len(all_sent_emails)}")

            never_emailed = all_target_emails - all_sent_emails
            never_emailed_audience = list(never_emailed)

            logger.info(f"Found {len(never_emailed_audience)} users who have never been sent an email")

            # Process each user in target audience who has received emails before
            processed_count = 0
            error_count = 0
            active_count_temp = 0
            non_active_count_temp = 0

            for email in target_df['user_email'].unique():
                try:
                    # If no send data for this user, they'll be handled in the never_emailed group
                    if email not in parsed_send_times:
                        continue

                    processed_count += 1
                    last_send = parsed_send_times[email]

                    # Both timestamps are guaranteed to be timezone-naive datetime objects at this point
                    # from our parsing code above

                    # Determine if user is active (opened last email)
                    is_active = False
                    if email in parsed_open_times:
                        last_open = parsed_open_times[email]

                        # User is active if their last open time is after their last send time
                        # or if it's within a reasonable window (1 day) of the last send
                        # This accounts for possible timing issues in the data
                        try:
                            is_active = last_open >= last_send or (last_send - last_open).total_seconds() < 86400
                        except Exception as e:
                            logger.error(f"Comparison failed for {email}: {str(e)}")
                            continue

                    # Calculate days since last email
                    days_since_last_email = (current_time - last_send).days

                    # Apply frequency rules
                    if is_active:
                        if days_since_last_email >= active_days:
                            active_audience.append(email)
                            active_count_temp += 1
                    else:
                        if days_since_last_email >= non_active_days:
                            non_active_audience.append(email)
                            non_active_count_temp += 1

                except Exception as e:
                    error_count += 1
                    logger.error(f"Error processing email {email}: {str(e)}")

            logger.info(f"Processed {processed_count} emails with {error_count} errors")
            logger.info(f"Found {active_count_temp} active users and {non_active_count_temp} non-active users")

            # Create a new DataFrame with engagement status column including never emailed users
            combined_emails = active_audience + non_active_audience + never_emailed_audience
            result_df = target_df[target_df['user_email'].isin(combined_emails)].copy()

            # Add engagement status for reference
            result_df['engagement_status'] = 'non_active'  # Default status
            result_df.loc[result_df['user_email'].isin(active_audience), 'engagement_status'] = 'active'
            result_df.loc[result_df['user_email'].isin(never_emailed_audience), 'engagement_status'] = 'new'

            # Add last engagement times for reference
            result_df['last_open_time'] = result_df['user_email'].map(last_open_times)
            result_df['last_send_time'] = result_df['user_email'].map(last_send_times)

            # Add days since last email for reference
            result_df['days_since_last_email'] = result_df['last_send_time'].apply(
                lambda x: (current_time - pd.to_datetime(x).replace(tzinfo=None)).days if pd.notna(x) else None
            )

            active_count = len(result_df[result_df['engagement_status'] == 'active'])
            non_active_count = len(result_df[result_df['engagement_status'] == 'non_active'])
            new_count = len(result_df[result_df['engagement_status'] == 'new'])

            logger.info(f"Selected {active_count} active users based on {active_frequency} frequency")
            logger.info(f"Selected {non_active_count} non-active users based on {non_active_frequency} frequency")
            logger.info(f"Selected {new_count} new users who have never been sent an email")
            logger.info(f"Total selected audience: {len(result_df)} users")

            return result_df

        except Exception as e:
            logger.error(f"Error selecting audience: {str(e)}")
            return pd.DataFrame()

    def _generate_basic_behavior_with_engagement(self, emails, engagement_data=None):
        """
        Generate basic user behavior information with email engagement metrics
        for users not found in brahma_data.csv.

        Args:
            emails (list or set): List of user emails to generate behavior for
            engagement_data (dict, optional): Dict containing email engagement metrics data

        Returns:
            dict: Mapping of emails to behavior strings
        """
        behavior_dict = {}

        for email in emails:
            user_first_name = self._get_first_name(email)

            # Create basic user behavior string with first name
            user_behaviour = f"Behaviour data not found for {user_first_name}. "

            # Add engagement metrics if available
            if engagement_data and all(key in engagement_data for key in ['last_open_times', 'last_click_times', 'last_send_times']):
                email_lower = email.lower()
                current_time = datetime.datetime.now().replace(tzinfo=None)  # Ensure timezone-naive

                # Add engagement status to behavior string if available
                if email_lower in engagement_data.get('engagement_status', {}):
                    engagement_status = engagement_data['engagement_status'].get(email_lower)
                    if engagement_status == "active":
                        status_str = "an active subscriber"
                    elif engagement_status == "non_active":
                        status_str = "a non-active subscriber"
                    else:  # new
                        status_str = "a new subscriber who hasn't received any emails yet"
                    user_behaviour += f"However, {user_first_name} is {status_str}. "

                # Add last open information
                if email_lower in engagement_data['last_open_times'] and engagement_data['last_open_times'][email_lower]:
                    last_open = engagement_data['last_open_times'][email_lower]
                    try:
                        # Handle different timestamp types
                        if isinstance(last_open, str):
                            last_open_dt = datetime.datetime.strptime(last_open, '%Y-%m-%d %H:%M:%S')
                        elif isinstance(last_open, pd.Timestamp):
                            last_open_dt = last_open.to_pydatetime()
                        elif isinstance(last_open, datetime.datetime):
                            last_open_dt = last_open
                        else:
                            raise ValueError(f"Unexpected timestamp type: {type(last_open)}")

                        # Ensure timezone-naive
                        if last_open_dt.tzinfo is not None:
                            last_open_dt = last_open_dt.replace(tzinfo=None)

                        time_diff = current_time - last_open_dt
                        days_diff = time_diff.days

                        if days_diff == 0:
                            if time_diff.seconds < 3600:
                                time_msg = f"less than an hour ago"
                            else:
                                hours = time_diff.seconds // 3600
                                time_msg = f"{hours} hour{'s' if hours > 1 else ''} ago"
                            user_behaviour += f"{user_first_name} last opened an email {time_msg}. "
                        elif days_diff == 1:
                            user_behaviour += f"{user_first_name} last opened an email yesterday. "
                        else:
                            user_behaviour += f"{user_first_name} last opened an email {days_diff} days ago. "
                    except Exception as e:
                        logger.warning(f"Error processing last open time for {email}: {str(e)}")

                # Add last click information
                if email_lower in engagement_data['last_click_times'] and engagement_data['last_click_times'][email_lower]:
                    last_click = engagement_data['last_click_times'][email_lower]
                    try:
                        # Handle different timestamp types
                        if isinstance(last_click, str):
                            last_click_dt = datetime.datetime.strptime(last_click, '%Y-%m-%d %H:%M:%S')
                        elif isinstance(last_click, pd.Timestamp):
                            last_click_dt = last_click.to_pydatetime()
                        elif isinstance(last_click, datetime.datetime):
                            last_click_dt = last_click
                        else:
                            raise ValueError(f"Unexpected timestamp type: {type(last_click)}")

                        # Ensure timezone-naive
                        if last_click_dt.tzinfo is not None:
                            last_click_dt = last_click_dt.replace(tzinfo=None)

                        time_diff = current_time - last_click_dt
                        days_diff = time_diff.days

                        if days_diff == 0:
                            if time_diff.seconds < 3600:
                                time_msg = f"less than an hour ago"
                            else:
                                hours = time_diff.seconds // 3600
                                time_msg = f"{hours} hour{'s' if hours > 1 else ''} ago"
                            user_behaviour += f"{user_first_name} last clicked on an email link {time_msg}. "
                        elif days_diff == 1:
                            user_behaviour += f"{user_first_name} last clicked on an email link yesterday. "
                        else:
                            user_behaviour += f"{user_first_name} last clicked on an email link {days_diff} days ago. "
                    except Exception as e:
                        logger.warning(f"Error processing last click time for {email}: {str(e)}")

                # Add email content clicked if available
                if email_lower in engagement_data['last_mail_content_clicked'] and engagement_data['last_mail_content_clicked'][email_lower]:
                    content_clicked = engagement_data['last_mail_content_clicked'][email_lower]
                    user_behaviour += f"They last clicked on content about '{content_clicked}'. "

                # Add total emails sent if available
                if email_lower in engagement_data['total_mails_sent'] and engagement_data['total_mails_sent'][email_lower]:
                    total_sent = engagement_data['total_mails_sent'][email_lower]
                    user_behaviour += f"{user_first_name} has received {total_sent} emails from us. "

                # Add last product sent information if available
                if email_lower in engagement_data['last_product_sent'] and engagement_data['last_product_sent'][email_lower]:
                    last_product = engagement_data['last_product_sent'][email_lower]
                    user_behaviour += f"The last product we emailed {user_first_name} about was {last_product}. "

            behavior_dict[email] = user_behaviour

        return behavior_dict

    def generate_user_behaviour_data(self, progress_callback=None):
        """Main function to process user data."""
        logger.info("Starting user data processing...")

        # File paths
        base_path = 'Sample Data For Mass Generation'
        target_audience_path = f'{base_path}/TargetAudience.csv'
        unsubscribers_path = f'{base_path}/Unsubscribers.csv'
        hard_bounce_path = f'{base_path}/Hard_Bounce_Mails.csv'
        buyers_files = [
            f'{base_path}/BBBuyers.csv',
            f'{base_path}/GenAI Buyers.csv',
            f'{base_path}/AgenticAI Buyers.csv'
        ]
        leads_files = [
            f'{base_path}/AgenticAI_Leads.csv',
            f'{base_path}/BB_Leads.csv',
            f'{base_path}/Pinnacle_Leads.csv'
        ]
        brahma_data_path = f'{base_path}/brahma_data.csv'

        # Ensure all required files exist
        required_files = [target_audience_path, unsubscribers_path, hard_bounce_path, brahma_data_path] + buyers_files + leads_files
        for file_path in required_files:
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                if progress_callback:
                    progress_callback(f"Warning: Missing file {file_path}", 10)

        # Update progress
        if progress_callback:
            progress_callback("Loading data files", 15)

        # 1. Read target audience
        try:
            target_df = pd.read_csv(target_audience_path)
            logger.info(f"Loaded {len(target_df)} target emails from {target_audience_path}")
            if progress_callback:
                progress_callback(f"Loaded {len(target_df)} target emails", 20)
        except Exception as e:
            logger.error(f"Error loading target audience: {str(e)}")
            target_df = pd.DataFrame(columns=['user_email', 'first_name'])
            if progress_callback:
                progress_callback(f"Error loading target audience: {str(e)}", 20)

        # 2. Read unsubscribers and hard bounces
        try:
            # Load unsubscribers
            unsubscribers_df = pd.read_csv(unsubscribers_path)
            # Handle different possible column names
            email_col = next((col for col in unsubscribers_df.columns if col.lower() in ['email', 'user_email', 'mail', 'email_id']), None)
            if email_col:
                unsubscribers = set(unsubscribers_df[email_col].str.lower().dropna())
                logger.info(f"Loaded {len(unsubscribers)} unsubscribed emails with column '{email_col}'")
            else:
                logger.warning(f"No email column found in {unsubscribers_path}. Columns found: {list(unsubscribers_df.columns)}")
                unsubscribers = set()

            # Load hard bounces
            hard_bounce_df = pd.read_csv(hard_bounce_path)
            # Handle different possible column names
            email_col = next((col for col in hard_bounce_df.columns if col.lower() in ['email', 'user_email', 'mail', 'email_id']), None)
            if email_col:
                hard_bounces = set(hard_bounce_df[email_col].str.lower().dropna())
                logger.info(f"Loaded {len(hard_bounces)} hard bounce emails with column '{email_col}'")
            else:
                logger.warning(f"No email column found in {hard_bounce_path}. Columns found: {list(hard_bounce_df.columns)}")
                hard_bounces = set()

            # Combine both exclusion lists
            excluded_emails = unsubscribers.union(hard_bounces)

            if progress_callback:
                progress_callback(f"Loaded {len(excluded_emails)} excluded emails", 25)

        except Exception as e:
            logger.error(f"Error loading exclusion lists: {str(e)}")
            excluded_emails = set()
            if progress_callback:
                progress_callback(f"Error loading exclusion lists: {str(e)}", 25)

        # 3. Filter out excluded emails from target audience
        if 'user_email' in target_df.columns:
            # Case-insensitive filtering
            target_df['email_lower'] = target_df['user_email'].str.lower()

            original_count = len(target_df)
            target_df = target_df[~target_df['email_lower'].isin(excluded_emails)]
            filtered_count = original_count - len(target_df)

            # Remove the temporary lowercase column
            target_df = target_df.drop(columns=['email_lower'])

            logger.info(f"Filtered out {filtered_count} excluded emails, {len(target_df)} eligible emails remaining")

            # Log remaining emails for debugging
            logger.info(f"Remaining emails after filtering: {list(target_df['user_email'].values)}")

            if progress_callback:
                progress_callback(f"Filtered out {filtered_count} excluded emails, {len(target_df)} eligible emails remaining", 30)

        # Update progress
        if progress_callback:
            progress_callback("Processing eligible users", 35)

        # First extract mail performance data (engagement metrics) before processing users
        if progress_callback:
            progress_callback("Extracting email engagement data", 36)


        last_open_times, last_click_times, last_send_times, all_subjects_opened, \
        all_preheaders_opened, last_mail_content_clicked, total_mails_sent, last_product_sent, most_frequent_open_times = self._extract_mail_performance_data()

        # Get most frequent activity times from brahma_data
        most_frequent_activity_times = self._find_most_frequent_activity_time(brahma_data_path)

        # Log what's in the email performance data
        logger.info(f"Found {len(last_open_times)} emails with open data")
        logger.info(f"Found {len(last_click_times)} emails with click data")
        logger.info(f"Found {len(last_send_times)} emails with send data")
        logger.info(f"Found {len(last_product_sent)} emails with product data")

        # Combine engagement metrics into a dictionary
        engagement_data = {
            'last_open_times': last_open_times,
            'last_click_times': last_click_times,
            'last_send_times': last_send_times,
            'all_subjects_opened': all_subjects_opened,
            'all_preheaders_opened': all_preheaders_opened,
            'last_mail_content_clicked': last_mail_content_clicked,
            'total_mails_sent': total_mails_sent,
            'last_product_sent': last_product_sent,
            'most_frequent_open_times': most_frequent_open_times,
            'most_frequent_activity_times': most_frequent_activity_times,
            'engagement_status': {}
        }

        # Get campaign settings path
        base_path_oe = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        campaign_settings_path = os.path.join(base_path_oe, 'data', 'campaignSettings.json')

        # 4. Process data with the filtered audience
        result_df = self.process_target_audience(
            target_df,  # Pass the filtered DataFrame directly instead of the file path
            buyers_files,
            leads_files,
            brahma_data_path,
            progress_callback=progress_callback,
            engagement_data=engagement_data,
            campaign_settings_path=campaign_settings_path
        )

        # Update progress
        if progress_callback:
            progress_callback("Saving processed data", 90)

        # Add mail performance data if available
        has_engagement_data = (last_open_times or last_click_times or last_send_times or
                            all_subjects_opened or all_preheaders_opened or
                            last_mail_content_clicked or total_mails_sent or last_product_sent)

        if has_engagement_data:
            if progress_callback:
                progress_callback("Adding email engagement metrics", 93)

            # Add basic time metrics
            result_df['Last_Open_Time'] = result_df['user_email'].str.lower().map(last_open_times)
            result_df['Last_Click_Time'] = result_df['user_email'].str.lower().map(last_click_times)
            result_df['Last_Send_Time'] = result_df['user_email'].str.lower().map(last_send_times)

            # Load campaign settings
            campaign_settings = {}
            if campaign_settings_path and os.path.exists(campaign_settings_path):
                try:
                    with open(campaign_settings_path, 'r') as f:
                        campaign_settings = json.load(f)
                except Exception as e:
                    logger.warning(f"Error loading campaign settings: {str(e)}")

            # Assign send times based on campaign settings and user activity patterns
            result_df = self.assign_send_times(result_df, campaign_settings, most_frequent_open_times, most_frequent_activity_times)

            # Add content metrics
            result_df['All_Subjects_Opened'] = result_df['user_email'].str.lower().map(all_subjects_opened)
            result_df['All_Preheader_Opened'] = result_df['user_email'].str.lower().map(all_preheaders_opened)
            result_df['Last_Mail_Content_Clicked'] = result_df['user_email'].str.lower().map(last_mail_content_clicked)

            # Add count metric
            result_df['Total_Mails_Sent'] = result_df['user_email'].str.lower().map(total_mails_sent)
            result_df['Last_Product_Sent'] = result_df['user_email'].str.lower().map(last_product_sent)

            # Convert lists to strings for CSV compatibility
            if 'All_Subjects_Opened' in result_df.columns:
                result_df['All_Subjects_Opened'] = result_df['All_Subjects_Opened'].apply(
                    lambda x: '|'.join(x) if isinstance(x, list) else x
                )

            if 'All_Preheader_Opened' in result_df.columns:
                result_df['All_Preheader_Opened'] = result_df['All_Preheader_Opened'].apply(
                    lambda x: '|'.join(x) if isinstance(x, list) else x
                )

            # Log how many records were updated
            metrics = {
                'Last_Open_Time': result_df['Last_Open_Time'].notna().sum(),
                'Last_Click_Time': result_df['Last_Click_Time'].notna().sum(),
                'Last_Send_Time': result_df['Last_Send_Time'].notna().sum(),
                'All_Subjects_Opened': result_df['All_Subjects_Opened'].notna().sum(),
                'All_Preheader_Opened': result_df['All_Preheader_Opened'].notna().sum(),
                'Last_Mail_Content_Clicked': result_df['Last_Mail_Content_Clicked'].notna().sum(),
                'Total_Mails_Sent': result_df['Total_Mails_Sent'].notna().sum(),
                'Last_Product_Sent': result_df['Last_Product_Sent'].notna().sum()
            }

            for metric, count in metrics.items():
                logger.info(f"Added {metric} for {count} users")

            if progress_callback:
                max_count = max(metrics.values()) if metrics else 0
                progress_callback(f"Added engagement data for {max_count} users", 95)


        # Save results
        output_path = f'{base_path}/processed_user_data.csv'
        result_df.to_csv(output_path, index=False)
        logger.info(f"Processed data saved to {output_path}")

        # Final progress update
        if progress_callback:
            progress_callback("Processing complete", 100)

        return result_df
