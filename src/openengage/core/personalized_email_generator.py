"""
Fully personalized email generation functionality for OpenEngage.
This module generates emails without using templates, focusing on complete personalization.
Uses OpenAI's GPT-4.1-mini model for generating personalized content.
"""
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, Callable
import logging
import streamlit as st
from openai import OpenAI
from dotenv import load_dotenv
import time
from core.product_selector import select_product_for_user
from core.email_formatter import text_to_html
from utils.file_utils import load_communication_settings

# Load environment variables
load_dotenv()

# Initialize OpenAI client if API key is available
openai_api_key = os.getenv("OPENAI_API_KEY")
openai_client = None
if openai_api_key:
    try:
        openai_client = OpenAI(api_key=openai_api_key)
    except Exception as e:
        st.error(f"Error initializing OpenAI client: {str(e)}")
else:
    st.warning("OPENAI_API_KEY not found in environment variables.")

# Initialize Claude client if API key is available
claude_api_key = os.getenv("ANTHROPIC_API_KEY")
claude_client = None
if claude_api_key:
    try:
        import anthropic
        claude_client = anthropic.Anthropic(api_key=claude_api_key)
    except ImportError:
        st.error("Anthropic package not found. Please install it with 'pip install anthropic'")
    except Exception as e:
        st.error(f"Error initializing Claude client: {str(e)}")
else:
    st.warning("ANTHROPIC_API_KEY not found in environment variables.")

# Check if at least one AI service is available
if not openai_client and not claude_client:
    st.error("No AI services available. Please configure OpenAI or Claude API keys.")

def test_ai_connections():
    """Test connections to AI services and return status."""
    connection_status = {
        'openai': False,
        'claude': False,
        'errors': []
    }

    # Test OpenAI connection
    if openai_client:
        try:
            # Simple test call
            test_response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Test"}],
                max_tokens=5,
                timeout=10.0
            )
            connection_status['openai'] = True
        except Exception as e:
            connection_status['errors'].append(f"OpenAI: {str(e)}")

    # Test Claude connection
    if claude_client:
        try:
            # Simple test call using the fastest current model
            test_response = claude_client.messages.create(
                model="claude-3-5-haiku-20241022",
                max_tokens=5,
                timeout=10.0,
                messages=[{"role": "user", "content": "Test"}]
            )
            connection_status['claude'] = True
        except Exception as e:
            connection_status['errors'].append(f"Claude: {str(e)}")

    return connection_status

def clean_email_content(content):
    """Remove structural headings and marketing framework terms from email content."""
    if not content:
        return content

    # List of structural headings to remove
    structural_headings = [
        'HOOK:', 'PITCH:', 'TRUST:', 'MOTIVATION:', 'OPENING:', 'BODY:', 'CLOSING:',
        'HOOK', 'PITCH', 'TRUST', 'MOTIVATION', 'OPENING', 'BODY', 'CLOSING',
        'ATTENTION:', 'INTEREST:', 'DESIRE:', 'ACTION:', 'CTA:', 'CALL TO ACTION:',
        'ATTENTION', 'INTEREST', 'DESIRE', 'ACTION', 'CTA', 'CALL TO ACTION',
        'PROBLEM:', 'SOLUTION:', 'BENEFIT:', 'PROOF:', 'URGENCY:',
        'PROBLEM', 'SOLUTION', 'BENEFIT', 'PROOF', 'URGENCY',
        'INTRODUCTION:', 'MAIN MESSAGE:', 'CONCLUSION:', 'SIGNATURE:',
        'INTRODUCTION', 'MAIN MESSAGE', 'CONCLUSION', 'SIGNATURE'
    ]

    lines = content.split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()

        # Skip empty lines
        if not line:
            cleaned_lines.append('')
            continue

        # Check if line is a structural heading
        is_structural_heading = False
        for heading in structural_headings:
            if line.upper().startswith(heading.upper()):
                # If it's just the heading alone, skip it
                if line.upper() == heading.upper() or line.upper() == heading.upper() + ':':
                    is_structural_heading = True
                    break
                # If it has content after the heading, remove just the heading part
                elif line.upper().startswith(heading.upper() + ':'):
                    line = line[len(heading) + 1:].strip()
                    break
                elif line.upper().startswith(heading.upper() + ' '):
                    line = line[len(heading):].strip()
                    break

        if not is_structural_heading and line:
            cleaned_lines.append(line)

    # Join lines and clean up extra whitespace
    cleaned_content = '\n'.join(cleaned_lines)

    # Remove multiple consecutive newlines
    import re
    cleaned_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_content)

    return cleaned_content.strip()

def retry_api_call(func, max_retries=3, delay=1):
    """Retry API calls with exponential backoff."""
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e

            # Check if it's a retryable error
            error_str = str(e).lower()
            if any(keyword in error_str for keyword in ['timeout', 'connection', 'network', 'temporary']):
                wait_time = delay * (2 ** attempt)
                st.warning(f"API call failed (attempt {attempt + 1}/{max_retries}). Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                # Non-retryable error, raise immediately
                raise e

def generate_personalized_emails(data_df, progress_callback=None, selected_model="gpt-4.1-mini"):
    """
    Generate fully personalized emails without using templates.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        progress_callback (callable, optional): Function to report progress (current, total, message)
        selected_model (str): The AI model to use for generation

    Returns:
        pd.DataFrame: DataFrame with generated email content
    """
    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load all products
    products = []
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
            if not isinstance(products, list):
                products = [products]
            if org_filter_enabled and org_url:
                products = [p for p in products if p.get("Company_URL", "") == org_url]
    except Exception as e:
        st.error("Error loading product details")
        return data_df

    if not products:
        if org_filter_enabled and org_url:
            st.error(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
        else:
            st.error("No products found. Please add products first.")
        return data_df

    # Process each user
    total_users = len(data_df)
    for idx, row in data_df.iterrows():
        if progress_callback:
            progress_callback(idx, total_users, f"Processing user: {row['first_name']}")

        # Match product for user
        matched_product, similarity = select_product_for_user(row, products)

        # Load communication settings for this organization
        org_url = matched_product.get('Company_URL', '') or matched_product.get('organization_url', '')
        comm_settings = load_communication_settings(organization_url=org_url)

        try:
            # Create personalized email content
            result = generate_personalized_email_content(
                user_data=row,
                product_data=matched_product,
                communication_settings=comm_settings,
                selected_model=selected_model
            )

            # Update DataFrame with email content
            data_df.at[idx, 'Subject'] = result.get('subject', f"Personalized recommendation for {row.get('first_name', '')}")
            data_df.at[idx, 'Mail_Content'] = result.get('content', '')
            data_df.at[idx, 'Preheader'] = result.get('preheader', '')
            data_df.at[idx, 'Matched_Product'] = matched_product.get('Product_Name', '')
            data_df.at[idx, 'Similarity_Score'] = round(float(similarity) * 100, 2)
        except Exception as e:
            st.error(f"Error generating email for {row.get('first_name', '')}: {str(e)}")
            # Set fallback values
            data_df.at[idx, 'Subject'] = f"Personalized recommendation for {row.get('first_name', '')}"
            data_df.at[idx, 'Mail_Content'] = f"Hi {row.get('first_name', '')},\n\nBased on your recent activity, we thought you might be interested in {matched_product.get('Product_Name', '')}.\n\nBest regards,\n{comm_settings.get('sender_name', 'OpenEngage Team')}"
            data_df.at[idx, 'Preheader'] = "A personalized recommendation just for you"
            data_df.at[idx, 'Matched_Product'] = matched_product.get('Product_Name', '')
            data_df.at[idx, 'Similarity_Score'] = round(float(similarity) * 100, 2)

        # Generate HTML for the email
        if progress_callback:
            progress_callback(idx, total_users, f"Generating HTML for: {row['first_name']}")

        # Create settings for HTML conversion
        settings = {
            "sender_name": comm_settings.get("sender_name", matched_product.get('Company_Name', 'OpenEngage Team')),
            "style": comm_settings.get("style", "friendly"),
            "length": comm_settings.get("length", "100-150 words"),
            "utm_source": "personalized_email",
            "utm_medium": "email",
            "utm_campaign": "fully_personalized",
            "utm_content": row['user_stage'].lower().replace(" ", "_"),
            "brand_personality": comm_settings.get("brand_personality", "Professional, Helpful, Trustworthy"),
            "tone_of_voice": comm_settings.get("tone_of_voice", "Professional, Informative"),
            "organization_url": org_url,
            "template_context": {
                "base_template": {
                    "product_data": matched_product
                }
            }
        }

        # Convert to HTML
        html_content = text_to_html(
            result,
            product_url=matched_product.get('Product_URL'),
            product_name=matched_product.get('Product_Name'),
            communication_settings=settings,
            recipient_email=row.get('user_email'),
            recipient_first_name=row.get('first_name')
        )

        # Add HTML content to the DataFrame
        data_df.at[idx, 'HTML_Content'] = html_content

        # Add prompt, model, and token information to the DataFrame if available
        if 'Prompt' in result:
            data_df.at[idx, 'Prompt'] = result['Prompt']
        if 'Model' in result:
            data_df.at[idx, 'Model'] = result['Model']
        if 'Input_Tokens' in result:
            data_df.at[idx, 'Input_Tokens'] = result['Input_Tokens']
        if 'Output_Tokens' in result:
            data_df.at[idx, 'Output_Tokens'] = result['Output_Tokens']

    # Save HTML emails
    if progress_callback:
        progress_callback(total_users, total_users, "Saving HTML emails")

    os.makedirs("data/html_emails", exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    for idx, row in data_df[:5].iterrows():
        if 'HTML_Content' in row and row['HTML_Content'] and isinstance(row['HTML_Content'], str):
            try:
                email_filename = f"data/html_emails/personalized_email_{timestamp}_{idx}.html"
                with open(email_filename, 'w') as f:
                    f.write(row['HTML_Content'])
            except Exception as e:
                st.error(f"Error saving HTML file for user {row.get('first_name', idx)}: {str(e)}")

    return data_df

def generate_personalized_email_content(user_data, product_data, communication_settings, selected_model="gpt-4.1-mini"):
    """
    Generate fully personalized email content based on user data and product data.

    Args:
        user_data (dict or pd.Series): User data including behavior
        product_data (dict): Product data
        communication_settings (dict): Communication settings
        selected_model (str): The AI model to use for generation
                             Supported models:
                             - OpenAI: gpt-4.1, gpt-4.1-mini, gpt-4o, gpt-4o-mini, o4-mini-2025-04-16, gpt-3.5-turbo
                             - Claude: claude-opus-4-20250514, claude-sonnet-4-20250514, claude-3-7-sonnet-20250219, claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022, claude-3-opus-20240229, claude-3-haiku-20240307
                             - Crew: crew-agent

    Returns:
        dict: Dictionary with subject, content, and preheader
    """
    # Extract user information for fallback content
    first_name = user_data.get('first_name', '')
    user_behavior = user_data.get('user_behaviour', '')
    user_stage = user_data.get('user_stage', '')

    try:
        # Extract product information
        product_name = product_data.get('Product_Name', '')
        product_features = product_data.get('Product_Features', [])
        product_summary = product_data.get('Product_Summary', '')
        product_description = product_data.get('Product_Description', '')
        product_url = product_data.get('Product_URL', '')

        # Extract communication settings
        tone = communication_settings.get('style', 'friendly')
        brand_personality = communication_settings.get('brand_personality', 'Professional, Helpful, Trustworthy')
        tone_of_voice = communication_settings.get('tone_of_voice', 'Professional, Informative')
        sender_name = communication_settings.get('sender_name', 'OpenEngage Team')
        length=communication_settings.get('length', '100-150 words')

        # Create prompt for email generation
        prompt = f"""
        Generate a fully personalized email for {first_name} based on their behavior and stage in the customer journey.

        User Information:
        - Name: {first_name}
        - Behavior: {user_behavior}
        - Stage: {user_stage}

        Product Information:
        - Name: {product_name}
        - Summary: {product_summary if product_summary else product_description}
        - Features: {', '.join(product_features[:5] if product_features else [])}
        - URL: {product_url}

        Communication Guidelines:
        - Tone: {tone}
        - Brand Personality: {brand_personality}
        - Tone of Voice: {tone_of_voice}
        - Sender Name: {sender_name}
        - Length of the Mail: {length}

        Create a completely personalized email that addresses the user's specific behavior and needs.
        Do not use generic templates. Make it feel like it was written specifically for this user.
        Include specific references to their behavior and how the product can help them.

        Structure of the Mail should be:-
        1. It should start with HOOK. The HOOK should be as small as it can be. It should grab the attention of user. You can get an idea of what to write in the hook from the user brhavior.
        2. The second part of mail should be TRUST. By reading this part, user should feel a deep urge to listen to you.
        3. The third part should be PITCH . Here, you will tell about the product and pitch it. In the pitch the user should feel that you are not only selling something. But you are his well wisher.
        4. The fourth part should be MOTIVATION. In this you will motivate the user based on his behavior and what the program contains and the benefits of the program.

        This doesn't mean that the Email Content will contain four paragraphs only.

        The email should include:
        1. A personalized subject line
        2. A preheader text
        3. A personalized greeting
        4. Body content based on structure of the mail.
        5. A clear call to action
        6. A professional sign-off

        Don't add anything irrelevant from your side. Only use the information given to you.

        Format your response as follows:

        Subject: [Your compelling subject line here]
Add         Pre-header: [Your brief pre-header text that complements the subject line]

        [Your email body content here]
        """

        # Determine which AI service to use based on selected model
        if (selected_model.startswith("gpt") or selected_model.startswith("o4") or selected_model.startswith("ft:")) and openai_client:
            try:
                # Use retry mechanism for OpenAI API call
                def make_openai_call():
                    # Define base parameters that are common to all models
                    params = {
                        "model": selected_model,
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are an expert email marketer who creates highly personalized emails based on user behavior and product information. Your emails are tailored to each individual recipient's needs and interests."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        "timeout": 30.0
                    }
                    
                    # Handle special cases for o4-mini-2025-04-16 model
                    if selected_model == "o4-mini-2025-04-16":
                        # o4-mini-2025-04-16 model requires max_completion_tokens and doesn't support custom temperature
                        params["max_completion_tokens"] = 1000
                        # Don't set temperature for this model - it only supports default value (1)
                    elif selected_model.startswith("o4"):
                        # Other o4 models use max_completion_tokens
                        params["max_completion_tokens"] = 1000
                        params["temperature"] = 0.7
                    else:
                        # Other models use max_tokens and support temperature
                        params["max_tokens"] = 1000
                        params["temperature"] = 0.7
                        
                    return openai_client.chat.completions.create(**params)

                completion = retry_api_call(make_openai_call)

                # Extract the response content
                response = completion.choices[0].message.content

                # Get token usage information
                input_tokens = completion.usage.prompt_tokens
                output_tokens = completion.usage.completion_tokens

                # Store the prompt for later use
                stored_prompt = prompt

                # Log success
                st.success(f"Generated personalized email using {selected_model} (Input tokens: {input_tokens}, Output tokens: {output_tokens})")
            except Exception as openai_error:
                st.error(f"Error using OpenAI API: {str(openai_error)}")
                st.error(f"OpenAI error details: {type(openai_error).__name__}")
                # Fall back to crew if available
                if hasattr(st.session_state, 'crew'):
                    st.warning("Falling back to crew for email generation")
                    try:
                        response = st.session_state.crew.delegate_work(
                            task="Generate a fully personalized email",
                            context=prompt,
                            coworker="Campaign Generation Agent"
                        )
                        # Set fallback metadata
                        stored_prompt = prompt
                        input_tokens = 0
                        output_tokens = 0
                    except Exception as crew_error:
                        st.error(f"Crew fallback also failed: {str(crew_error)}")
                        # Use static fallback as last resort
                        response = f"Subject: Personalized recommendation for {first_name}\nPre-header: A special recommendation just for you\n\nHi {first_name},\n\nBased on your recent activity, we thought you might be interested in our latest offerings.\n\nBest regards,\nThe Team"
                        stored_prompt = prompt
                        input_tokens = 0
                        output_tokens = 0
                else:
                    raise Exception(f"Failed to generate email with OpenAI and no crew fallback available: {openai_error}")

        elif selected_model.startswith("claude") and claude_client:
            try:
                # Use the selected model directly (all models are now official API names)
                api_model = selected_model

                # Create Claude-specific prompt that emphasizes no structural headings
                claude_prompt = f"""
                {prompt}

                IMPORTANT FORMATTING INSTRUCTIONS:
                - Do NOT include any structural headings like "HOOK", "PITCH", "TRUST", "MOTIVATION", "OPENING", "BODY", "CLOSING", etc.
                - Do NOT use section labels or marketing framework terms
                - Write the email as natural, flowing content without any meta-labels
                - The email should read like a genuine, personal message
                - Focus on creating seamless, conversational content
                """

                # Use retry mechanism for Claude API call
                def make_claude_call():
                    return claude_client.messages.create(
                        model=api_model,
                        max_tokens=1000,
                        temperature=0.7,
                        timeout=30.0,
                        system="You are an expert email marketer who creates highly personalized emails. Write natural, flowing emails without any structural headings, section labels, or marketing framework terms. Your emails should read like genuine personal messages.",
                        messages=[
                            {
                                "role": "user",
                                "content": claude_prompt
                            }
                        ]
                    )

                completion = retry_api_call(make_claude_call)

                # Extract the response content
                raw_response = completion.content[0].text

                # Clean the response to remove structural headings
                response = clean_email_content(raw_response)

                # Get token usage information
                input_tokens = completion.usage.input_tokens
                output_tokens = completion.usage.output_tokens

                # Store the prompt for later use
                stored_prompt = prompt

                # Log success
                st.success(f"Generated personalized email using {selected_model} (Input tokens: {input_tokens}, Output tokens: {output_tokens})")

                # Log if content was cleaned
                if raw_response != response:
                    st.info("Removed structural headings from Claude response for cleaner email format")
            except Exception as claude_error:
                st.error(f"Error using Claude API: {str(claude_error)}")
                st.error(f"Claude error details: {type(claude_error).__name__}")
                # Fall back to crew if available
                if hasattr(st.session_state, 'crew'):
                    st.warning("Falling back to crew for email generation")
                    try:
                        response = st.session_state.crew.delegate_work(
                            task="Generate a fully personalized email",
                            context=prompt,
                            coworker="Campaign Generation Agent"
                        )
                        # Set fallback metadata
                        stored_prompt = prompt
                        input_tokens = 0
                        output_tokens = 0
                    except Exception as crew_error:
                        st.error(f"Crew fallback also failed: {str(crew_error)}")
                        raise Exception(f"Failed to generate email with Claude ({claude_error}) and crew ({crew_error})")
                else:
                    raise Exception(f"Failed to generate email with Claude and no crew fallback available: {claude_error}")

        # Fall back to crew if selected model is not available or no AI client is available
        elif hasattr(st.session_state, 'crew'):
            st.warning(f"Selected model {selected_model} not available. Using crew for email generation.")
            try:
                response = st.session_state.crew.delegate_work(
                    task="Generate a fully personalized email",
                    context=prompt,
                    coworker="Campaign Generation Agent"
                )
                # Set fallback metadata
                stored_prompt = prompt
                input_tokens = 0
                output_tokens = 0
            except Exception as crew_error:
                st.error(f"Crew generation failed: {str(crew_error)}")
                # Use static fallback as last resort
                response = f"Subject: Personalized recommendation for {first_name}\nPre-header: A special recommendation just for you\n\nHi {first_name},\n\nBased on your recent activity, we thought you might be interested in our latest offerings.\n\nBest regards,\nThe Team"
                stored_prompt = prompt
                input_tokens = 0
                output_tokens = 0
        else:
            # If no AI service is available, use static fallback content
            st.warning("No AI services available. Using static fallback content.")
            response = f"Subject: Personalized recommendation for {first_name}\nPre-header: A special recommendation just for you\n\nHi {first_name},\n\nBased on your recent activity, we thought you might be interested in our latest offerings.\n\nBest regards,\nThe Team"
            stored_prompt = prompt
            input_tokens = 0
            output_tokens = 0

        # Process the response
        if isinstance(response, dict):
            return {
                'subject': response.get('subject', f"Personalized recommendation for {first_name}"),
                'content': response.get('content', ''),
                'preheader': response.get('preheader', '')
            }
        else:
            # Try to parse the response if it's not already a dict
            try:
                if isinstance(response, str) and "Subject:" in response:
                    # Parse the email content format
                    subject = ""
                    preheader = ""
                    content = ""

                    # Extract subject
                    if "Subject:" in response:
                        subject_parts = response.split("Subject:", 1)
                        if len(subject_parts) > 1:
                            subject_line = subject_parts[1].split("\n", 1)[0].strip()
                            subject = subject_line

                    # Extract preheader
                    if "Pre-header:" in response:
                        preheader_parts = response.split("Pre-header:", 1)
                        if len(preheader_parts) > 1:
                            preheader_line = preheader_parts[1].split("\n", 1)[0].strip()
                            preheader = preheader_line

                            # Extract content (everything after preheader)
                            content_parts = preheader_parts[1].split("\n", 1)
                            if len(content_parts) > 1:
                                content = content_parts[1].strip()
                    else:
                        # If no preheader, content is everything after subject
                        content_parts = response.split("Subject:", 1)[1].split("\n", 1)
                        if len(content_parts) > 1:
                            content = content_parts[1].strip()

                    # Clean the content to remove any structural headings
                    content = clean_email_content(content)

                    # Create result dictionary with email content and metadata
                    result = {
                        'subject': subject or f"Personalized recommendation for {first_name}",
                        'content': content,
                        'preheader': preheader or f"A personalized recommendation for {first_name}"
                    }

                    # Add metadata if available
                    if 'input_tokens' in locals():
                        result['Input_Tokens'] = input_tokens
                    if 'output_tokens' in locals():
                        result['Output_Tokens'] = output_tokens
                    if 'stored_prompt' in locals():
                        result['Prompt'] = stored_prompt
                    elif 'prompt' in locals():
                        result['Prompt'] = prompt

                    # Add model information
                    if 'selected_model' in locals():
                        result['Model'] = selected_model
                    elif openai_client:
                        result['Model'] = "gpt-4.1-mini"
                    else:
                        result['Model'] = "crew-agent"

                    return result

                # Also try JSON parsing if the response contains JSON
                elif isinstance(response, str) and '{' in response and '}' in response:
                    # Extract JSON part
                    json_str = response[response.find('{'):response.rfind('}')+1]
                    parsed_response = json.loads(json_str)

                    # Create result dictionary with email content
                    result = {
                        'subject': parsed_response.get('subject', f"Personalized recommendation for {first_name}"),
                        'content': parsed_response.get('content', ''),
                        'preheader': parsed_response.get('preheader', '')
                    }

                    # Add metadata if available
                    if 'input_tokens' in locals():
                        result['Input_Tokens'] = input_tokens
                    if 'output_tokens' in locals():
                        result['Output_Tokens'] = output_tokens
                    if 'stored_prompt' in locals():
                        result['Prompt'] = stored_prompt
                    elif 'prompt' in locals():
                        result['Prompt'] = prompt

                    # Add model information
                    if 'selected_model' in locals():
                        result['Model'] = selected_model
                    elif openai_client:
                        result['Model'] = "gpt-4.1-mini"
                    else:
                        result['Model'] = "crew-agent"

                    return result
            except Exception as parse_error:
                st.error(f"Error parsing response: {str(parse_error)}")
                # Create result dictionary with raw response as content
                result = {
                    'subject': f"Personalized recommendation for {first_name}",
                    'content': response,
                    'preheader': f"A personalized recommendation for {first_name}"
                }

                # Add metadata if available
                if 'input_tokens' in locals():
                    result['Input_Tokens'] = input_tokens
                if 'output_tokens' in locals():
                    result['Output_Tokens'] = output_tokens
                if 'stored_prompt' in locals():
                    result['Prompt'] = stored_prompt
                elif 'prompt' in locals():
                    result['Prompt'] = prompt

                # Add model information
                if 'selected_model' in locals():
                    result['Model'] = selected_model
                elif openai_client:
                    result['Model'] = "gpt-4.1-mini"
                else:
                    result['Model'] = "crew-agent"

                return result

        # Fallback if LLM generation fails
        product_name = product_data.get('Product_Name', '')
        product_description = product_data.get('Product_Description', '')
        product_features = product_data.get('Product_Features', [])
        product_url = product_data.get('Product_URL', '')
        sender_name = communication_settings.get('sender_name', 'OpenEngage Team')

        # Create result dictionary with fallback content
        result = {
            'subject': f"Personalized recommendation for {first_name}",
            'content': f"Hi {first_name},\n\nBased on your recent activity ({user_behavior}), we thought you might be interested in {product_name}.\n\n{product_description}\n\nKey features:\n" + "\n".join([f"- {feature}" for feature in product_features[:3]]) + f"\n\nCheck it out here: {product_url}\n\nBest regards,\n{sender_name}",
            'preheader': f"A personalized recommendation based on your recent activity",
            'Model': 'fallback',
            'Prompt': 'N/A - Using fallback content'
        }

        return result
    except Exception as e:
        st.error(f"Error generating personalized email: {str(e)}")
        # Return a basic fallback
        product_name = product_data.get('Product_Name', '')
        product_url = product_data.get('Product_URL', '')
        sender_name = communication_settings.get('sender_name', 'OpenEngage Team')

        # Create result dictionary with emergency fallback content
        result = {
            'subject': f"Personalized recommendation for {first_name}",
            'content': f"Hi {first_name},\n\nWe have a personalized recommendation for you based on your recent activity.\n\nCheck out {product_name} at {product_url}\n\nBest regards,\n{sender_name}",
            'preheader': "A personalized recommendation just for you",
            'Model': 'emergency-fallback',
            'Prompt': 'N/A - Using emergency fallback content',
            'Input_Tokens': 0,
            'Output_Tokens': 0
        }

        return result
