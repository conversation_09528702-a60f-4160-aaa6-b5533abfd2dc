"""
Popup Batch Generator for OpenEngage.
Generates personalized popup content using AI with parallel processing.
"""
import os
import json
import pandas as pd
import yaml
from typing import Dict, Any, Optional, Callable
from openai import OpenAI
from dotenv import load_dotenv
from pathlib import Path
import logging
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import time

# Load environment variables
load_dotenv()

# Initialize OpenAI client if API key is available
openai_api_key = os.getenv("OPENAI_API_KEY")
client = None
if openai_api_key:
    try:
        client = OpenAI(api_key=openai_api_key)
    except Exception as e:
        print(f"Error initializing OpenAI client: {str(e)}")
else:
    print("Warning: OPENAI_API_KEY not found in environment variables. AI popup generation will be disabled.")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_popup_prompt_template():
    """Load the popup generation prompt template from config/prompts.yml"""
    try:
        prompts_path = Path(__file__).parent.parent.parent.parent / 'config' / 'prompts.yml'
        with open(prompts_path, 'r') as f:
            prompts = yaml.safe_load(f)
            return prompts.get('popup_generation', {}).get('template', '')
    except Exception as e:
        logger.error(f"Error loading popup prompt template: {str(e)}")
        return ""

def get_product_assignment(user_behavior: str, user_stage: str) -> str:
    """
    Assign a product based on user behavior and stage.
    This is a simplified assignment logic.
    """
    # Default priority 1 product
    default_product = "Agentic AI Pioneer Program"

    # Simple keyword-based assignment
    behavior_lower = user_behavior.lower() if user_behavior else ""

    if any(keyword in behavior_lower for keyword in ['blackbelt', 'machine learning', 'ml', 'data science']):
        return "Certified AI/ML BlackBelt Plus Program"
    elif any(keyword in behavior_lower for keyword in ['genai', 'generative', 'pinnacle', 'llm']):
        return "GenAI Pinnacle Plus Program"
    elif any(keyword in behavior_lower for keyword in ['agentic', 'agent', 'ai pioneer']):
        return "Agentic AI Pioneer Program"
    else:
        return default_product

def generate_single_popup_content(user_data: Dict[str, Any], prompt_template: str) -> Dict[str, Any]:
    """
    Generate popup content for a single user using OpenAI API.

    Args:
        user_data: Dictionary containing user information
        prompt_template: The prompt template to use for generation

    Returns:
        Dictionary with popup content or error information
    """
    try:
        # Extract user information
        user_email = user_data.get('user_email', '')
        first_name = user_data.get('first_name', user_email.split('@')[0] if user_email else 'Customer')
        # Use correct column name from user_popup_data.csv
        user_behavior = user_data.get('behaviour_data', user_data.get('user_behaviour', ''))
        user_stage = user_data.get('user_stage', 'New Visitor')

        # Use target_product from CSV data instead of auto-assignment
        target_product = user_data.get('target_product', 'Agentic AI Pioneer Program')
        redirect_url = user_data.get('redirect_url', '')

        # Debug statements
        logger.info(f"Processing user: {user_email}")
        logger.info(f"User behavior: {user_behavior[:100]}..." if len(user_behavior) > 100 else f"User behavior: {user_behavior}")
        logger.info(f"Target product: {target_product}")
        logger.info(f"User stage: {user_stage}")
        logger.info(f"Redirect URL: {redirect_url}")

        # Check if OpenAI client is available
        if client is None:
            raise ValueError("OpenAI client not available")

        # Use the prompt template if available, otherwise use a default
        if not prompt_template:
            print('prompt not found')
            prompt_template = """Generate personalized popup content for a website visitor based on their data and behavior.

            User Details:
            First Name: {first_name}
            User Behavior: {user_behavior}
            Target Product: {target_product}
            User Stage: {user_stage}

            Requirements:
            - Create a popup that feels tailored to this specific user's needs and interests
            - The content should be engaging, concise, and drive conversion
            - Avoid generic marketing language
            - If behavior data is not available, focus on the target product and user stage

            Return a JSON object with these fields:
            - popup_title: A short, attention-grabbing title (max 8 words)
            - popup_text: Compelling text (5-10 words) about why they should check out this product
            - button_text: Call-to-action text for the button (1-3 words)"""

        # Format the prompt with user data
        formatted_prompt = prompt_template.format(
            first_name=first_name,
            user_behavior=user_behavior,
            target_product=target_product,
            user_stage=user_stage
        )

        # Call OpenAI API
        response = client.chat.completions.create(
            model="gpt-4o",
            temperature=0.7,
            response_format={"type": "json_object"},
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert popup content generator. Create personalized, engaging popup content that drives conversions. Always respond with valid JSON."
                },
                {
                    "role": "user",
                    "content": formatted_prompt
                }
            ]
        )

        # Extract content from response
        content = response.choices[0].message.content
        response_json = json.loads(content)

        # Extract popup content
        popup_title = response_json.get("popup_title", f"Discover {target_product}")
        popup_text = response_json.get("popup_text", "Enhance your AI skills today!")
        button_text = response_json.get("button_text", "Learn More")

        # Debug statements for generated content
        logger.info(f"Generated popup title: {popup_title}")
        logger.info(f"Generated popup text: {popup_text}")
        logger.info(f"Generated button text: {button_text}")

        return {
            'user_email': user_email,
            'behaviour_data': user_behavior,
            'target_product': target_product,
            'user_stage': user_stage,
            'redirect_url': redirect_url,
            'popup_title': popup_title,
            'popup_text': popup_text,
            'button_text': button_text,
            'success': True
        }

    except Exception as e:
        logger.error(f"Error generating popup content for {user_data.get('user_email', 'unknown')}: {str(e)}")

        # Fallback content
        user_email = user_data.get('user_email', '')
        first_name = user_data.get('first_name', user_email.split('@')[0] if user_email else 'Customer')
        # Use correct column name from user_popup_data.csv
        user_behavior = user_data.get('behaviour_data', user_data.get('user_behaviour', ''))
        target_product = user_data.get('target_product', 'Agentic AI Pioneer Program')
        redirect_url = user_data.get('redirect_url', '')

        # Debug statement for fallback
        logger.info(f"Using fallback content for {user_email}")

        return {
            'user_email': user_email,
            'behaviour_data': user_behavior,
            'target_product': target_product,
            'user_stage': user_data.get('user_stage', 'New Visitor'),
            'redirect_url': redirect_url,
            'popup_title': f"Special Offer for {first_name}",
            'popup_text': f"Enhance your AI skills with {target_product}!",
            'button_text': "Learn More",
            'success': False,
            'error': str(e)
        }

def generate_popup_content_batch(
    data_df: Optional[pd.DataFrame] = None,
    progress_callback: Optional[Callable] = None,
    max_workers: int = 5,
    batch_size: int = 50
) -> pd.DataFrame:
    """
    Generate popup content for multiple users using parallel processing.

    Process:
    1. Read data from user_popup_data.csv (only user_email, behaviour_data, target_product, user_stage, redirect_url columns)
    2. Generate popup content using AI and add to dataframe
    3. Save complete dataframe to popup_text.csv
    4. Use only popup_text.csv in Popup configuration screen

    Data Source: Uses user_popup_data.csv with columns:
    - user_email: User's email address
    - behaviour_data: User behavior and browsing history
    - target_product: Assigned product (passed to LLM)
    - user_stage: Current user journey stage
    - redirect_url: Target URL for popup button

    Output: Saves complete data to popup_text.csv with original columns plus:
    - popup_title: AI-generated popup title
    - popup_text: AI-generated popup text
    - button_text: AI-generated button text
    - success: Generation success status

    Args:
        data_df: DataFrame containing user data (optional, will load from user_popup_data.csv if None)
        progress_callback: Function to report progress (current, total, message)
        max_workers: Maximum number of parallel workers
        batch_size: Number of users to process in each batch

    Returns:
        DataFrame with generated popup content saved to popup_text.csv
    """
    # Load data from user_popup_data.csv if no DataFrame is provided
    if data_df is None:
        user_popup_data_path = os.path.join(os.getcwd(), 'Sample Data For Mass Generation', 'user_popup_data.csv')
        if os.path.exists(user_popup_data_path):
            logger.info("Loading user data from user_popup_data.csv")
            # Read only the required columns
            required_columns = ['user_email', 'behaviour_data', 'target_product', 'user_stage', 'redirect_url']
            data_df = pd.read_csv(user_popup_data_path, usecols=required_columns)
            logger.info(f"Loaded {len(data_df)} users with columns: {list(data_df.columns)}")
            if progress_callback:
                # Report loading completion without causing division by zero
                progress_callback(0, len(data_df), f"Loaded {len(data_df)} users from user_popup_data.csv")
        else:
            logger.error("user_popup_data.csv not found and no data provided")
            raise FileNotFoundError("user_popup_data.csv not found. Please ensure the file exists or provide data_df parameter.")

    logger.info(f"Starting popup content generation for {len(data_df)} users")

    # Load the prompt template
    prompt_template = load_popup_prompt_template()

    # Prepare results list
    results = []
    total_users = len(data_df)
    processed_count = 0

    # Report initial progress
    if progress_callback:
        progress_callback(0, total_users, "Starting popup content generation...")

    # Process users in batches to avoid overwhelming the API
    for batch_start in range(0, total_users, batch_size):
        batch_end = min(batch_start + batch_size, total_users)
        batch_df = data_df.iloc[batch_start:batch_end]

        # Convert batch to list of dictionaries
        batch_users = batch_df.to_dict('records')

        # Process batch with parallel execution
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks for this batch
            future_to_user = {
                executor.submit(generate_single_popup_content, user_data, prompt_template): user_data
                for user_data in batch_users
            }

            # Collect results as they complete
            for future in as_completed(future_to_user):
                try:
                    result = future.result()
                    results.append(result)
                    processed_count += 1

                    # Report progress
                    if progress_callback:
                        progress_callback(processed_count - 1, total_users, f"Generated popup content for {result['user_email']}")

                except Exception as e:
                    user_data = future_to_user[future]
                    logger.error(f"Error processing user {user_data.get('user_email', 'unknown')}: {str(e)}")

                    # Add fallback result with all required columns
                    user_behavior = user_data.get('behaviour_data', user_data.get('user_behaviour', ''))
                    target_product = user_data.get('target_product', 'Agentic AI Pioneer Program')
                    first_name = user_data.get('user_email', '').split('@')[0] if user_data.get('user_email', '') else 'Customer'

                    results.append({
                        'user_email': user_data.get('user_email', ''),
                        'behaviour_data': user_behavior,
                        'target_product': target_product,
                        'user_stage': user_data.get('user_stage', 'New Visitor'),
                        'redirect_url': user_data.get('redirect_url', ''),
                        'popup_title': f"Special Offer for {first_name}",
                        'popup_text': f"Enhance your AI skills with {target_product}!",
                        'button_text': "Learn More",
                        'success': False,
                        'error': str(e)
                    })
                    processed_count += 1

        # Add a small delay between batches to respect API rate limits
        if batch_end < total_users:
            time.sleep(1)

    # Create DataFrame from results
    result_df = pd.DataFrame(results)

    # Debug statements for final dataframe
    logger.info(f"Created result dataframe with {len(result_df)} rows")
    logger.info(f"Result dataframe columns: {list(result_df.columns)}")
    logger.info(f"Success rate: {result_df['success'].sum()}/{len(result_df)} ({result_df['success'].mean()*100:.1f}%)")

    # Save to CSV file
    try:
        # Ensure the Sample Data directory exists
        sample_data_dir = os.path.join(os.getcwd(), 'Sample Data For Mass Generation')
        os.makedirs(sample_data_dir, exist_ok=True)

        # Save to popup_text.csv
        output_path = os.path.join(sample_data_dir, 'popup_text.csv')
        result_df.to_csv(output_path, index=False)

        logger.info(f"Popup content saved to {output_path}")
        logger.info(f"Saved {len(result_df)} records with columns: {list(result_df.columns)}")

        # Report completion
        if progress_callback:
            progress_callback(total_users, total_users, f"Saved popup content to popup_text.csv")

    except Exception as e:
        logger.error(f"Error saving popup content to CSV: {str(e)}")
        if progress_callback:
            progress_callback(total_users, total_users, f"Error saving to CSV: {str(e)}")

    return result_df
