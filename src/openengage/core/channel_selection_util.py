"""
Utility functions for channel selection in OpenEngage marketing automation platform.
"""
import os
import pandas as pd
import logging
from typing import Dict, Any, List, Optional, Tuple

from core.channel_selector import ChannelSelector, load_user_engagement_from_performance_data

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def select_channels_for_campaign(
    users_data_path: str,
    content_type: str = "general",
    urgency_level: str = "medium",
    performance_data_path: Optional[str] = None,
    output_dir: Optional[str] = None,
    weights: Optional[Dict[str, float]] = None
) -> Dict[str, pd.DataFrame]:
    """
    Selects the optimal channel for each user in a campaign and outputs segmented CSVs.
    
    Args:
        users_data_path: Path to the CSV file with user data
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        output_dir: Directory to save output CSV files (default: current directory)
        weights: Optional dictionary of weights for different factors
        
    Returns:
        Dictionary containing DataFrames segmented by channel
    """
    try:
        # Validate input file
        if not os.path.exists(users_data_path):
            logger.error(f"User data file not found: {users_data_path}")
            return {}
            
        # Load user data
        users_df = pd.read_csv(users_data_path)
        
        # Validate user data has required columns
        if "user_email" not in users_df.columns and "phone_number" not in users_df.columns:
            logger.error("User data must contain at least one of these columns: user_email, phone_number")
            return {}
            
        # Convert to list of dictionaries
        users_data = users_df.to_dict('records')
        
        # Initialize channel selector with custom weights if provided
        channel_selector = ChannelSelector(weights=weights)
        
        # Load engagement data if performance file provided
        engagement_data = None
        if performance_data_path and os.path.exists(performance_data_path):
            engagement_data = load_user_engagement_from_performance_data(performance_data_path)
            
        # Process channels
        logger.info(f"Processing channels for {len(users_data)} users")
        channels_dict = channel_selector.batch_select_channels(
            users_data,
            content_type=content_type,
            urgency_level=urgency_level
        )
        
        # Create result dataframes
        result = {}
        
        # Email users
        email_users = channels_dict.get("email", [])
        if email_users:
            # Get user IDs for filtering
            email_ids = [u.get("user_email") for u in email_users if u.get("user_email")]
            result["email"] = users_df[users_df["user_email"].isin(email_ids)]
        else:
            result["email"] = pd.DataFrame()
            
        # WhatsApp users
        whatsapp_users = channels_dict.get("whatsapp", [])
        if whatsapp_users and "phone_number" in users_df.columns:
            # Get user phone numbers for filtering
            phone_numbers = [u.get("phone_number") for u in whatsapp_users if u.get("phone_number")]
            result["whatsapp"] = users_df[users_df["phone_number"].isin(phone_numbers)]
        else:
            result["whatsapp"] = pd.DataFrame()
            
        # Unavailable users
        unavailable_users = channels_dict.get("unavailable", [])
        if unavailable_users:
            # Identify unavailable users (those with neither email nor phone)
            if "phone_number" in users_df.columns:
                result["unavailable"] = users_df[
                    (~users_df["user_email"].isin([u.get("user_email") for u in email_users if u.get("user_email")])) &
                    (~users_df["phone_number"].isin([u.get("phone_number") for u in whatsapp_users if u.get("phone_number")]))
                ]
            else:
                result["unavailable"] = users_df[
                    ~users_df["user_email"].isin([u.get("user_email") for u in email_users if u.get("user_email")])
                ]
        else:
            result["unavailable"] = pd.DataFrame()
            
        # Save output files if output_dir is provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate timestamp for filenames
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save email users
            if not result["email"].empty:
                email_path = os.path.join(output_dir, f"email_users_{timestamp}.csv")
                result["email"].to_csv(email_path, index=False)
                logger.info(f"Saved {len(result['email'])} email users to {email_path}")
                
            # Save WhatsApp users
            if not result["whatsapp"].empty:
                whatsapp_path = os.path.join(output_dir, f"whatsapp_users_{timestamp}.csv")
                result["whatsapp"].to_csv(whatsapp_path, index=False)
                logger.info(f"Saved {len(result['whatsapp'])} WhatsApp users to {whatsapp_path}")
                
            # Save unavailable users
            if not result["unavailable"].empty:
                unavailable_path = os.path.join(output_dir, f"unavailable_users_{timestamp}.csv")
                result["unavailable"].to_csv(unavailable_path, index=False)
                logger.info(f"Saved {len(result['unavailable'])} unavailable users to {unavailable_path}")
                
        return result
        
    except Exception as e:
        logger.error(f"Error in channel selection: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}


def get_user_channel_with_scores(
    user_data: Dict[str, Any],
    content_type: str = "general",
    urgency_level: str = "medium",
    performance_data_path: Optional[str] = None,
    weights: Optional[Dict[str, float]] = None
) -> Dict[str, Any]:
    """
    Get the recommended channel and scores for a single user.
    
    Args:
        user_data: Dictionary containing user data (must include user_email and/or phone_number)
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        weights: Optional dictionary of weights for different factors
        
    Returns:
        Dictionary with selected channel and scores
    """
    try:
        # Initialize channel selector with custom weights if provided
        channel_selector = ChannelSelector(weights=weights)
        
        # Load engagement data if performance file provided
        engagement_data = None
        if performance_data_path and os.path.exists(performance_data_path):
            engagement_data = load_user_engagement_from_performance_data(performance_data_path)
            
        # Select channel for user
        channel, scores = channel_selector.select_channel(
            user_data,
            content_type=content_type,
            urgency_level=urgency_level,
            engagement_data=engagement_data
        )
        
        # Return result
        result = {
            "selected_channel": channel,
            "email_score": scores.get("email", 0),
            "whatsapp_score": scores.get("whatsapp", 0),
        }
        
        # Add user identifiers if they exist
        if "user_email" in user_data:
            result["user_email"] = user_data["user_email"]
        if "phone_number" in user_data:
            result["phone_number"] = user_data["phone_number"]
            
        return result
        
    except Exception as e:
        logger.error(f"Error getting user channel: {str(e)}")
        return {"selected_channel": "email", "email_score": 0, "whatsapp_score": 0}
