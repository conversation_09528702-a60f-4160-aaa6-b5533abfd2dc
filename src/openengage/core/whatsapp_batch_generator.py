"""
WhatsApp batch generation functionality for OpenEngage.
"""
import os
import json
import time
import sys
import pandas as pd
import streamlit as st
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def generate_whatsapp_batch(
    data_df: pd.DataFrame,
    products: List[Dict[str, Any]],
    progress_callback: Optional[Callable] = None,
    batch_size: int = 20
) -> pd.DataFrame:
    """
    Generate WhatsApp messages in batch using OpenAI's Batch API.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        products (List[Dict]): List of product data dictionaries
        progress_callback (callable, optional): Function to report progress
        batch_size (int): Size of batches for processing

    Returns:
        pd.DataFrame: DataFrame with generated WhatsApp content
    """
    # Create a mapping of user rows to matched products
    user_product_mapping = {}

    # Create lists for batch tasks
    tasks = []

    # System prompt for WhatsApp message generation
    system_prompt = """
    You are an expert WhatsApp marketing specialist. Your task is to create personalized WhatsApp messages
    based on user behavior, product information, and existing email templates.

    Generate a short, concise WhatsApp message for the specified product. The message should be 2-3 lines maximum.

    Your response should be in JSON format with the following structure:
    {
        "content": "Your personalized WhatsApp message content here"
    }

    IMPORTANT INSTRUCTIONS:
    1. DO NOT include any greetings (like "Hi", "Hello", "Dear") or salutations (like "Thanks", "Regards", etc.)
    2. The template already has greeting with {{1}} for the name and closing text
    3. Your message will be inserted as {{2}} in the middle of the template
    4. Start directly with the personalized content about the product
    5. Focus only on the key benefits relevant to the user's behavior
    6. ONLY generate the message content - no greeting, no salutation, no signature
    7. The message should be ONLY the core content about the product benefits
    8. Keep it under 160 characters total
    9. Use the email template as a reference for tone, style, and key selling points
    10. ONLY mention features that are actually mentioned in the product details or email template
    11. DO NOT invent or make up features that aren't mentioned in the provided information
    12. Focus on personalization based on user behavior if available

    Example of what NOT to do:
    "Hi {{1}}! Check out our amazing product with features X and Y. Thanks, Team Z"

    Example of what TO do:
    "Our product offers features X and Y that solve your specific problem with impressive results."

    Keep the message concise, engaging, and focused on the product's benefits that match the user's interests.
    Make sure to include only the most relevant product features and benefits.
    Do not include any links or formatting that wouldn't work in WhatsApp.
    """

    # Report progress
    if progress_callback:
        progress_callback(0, len(data_df), "Processing users and creating tasks...")

    # Process all users in a single loop - match products and create tasks
    for idx, row in data_df.iterrows():
        # Check if user has behavior data
        has_behavior_data = ("Behaviour data not found" not in row['user_behaviour']) or ("last opened an email" in row['user_behaviour'])

        # Always use product selector based on similarity, regardless of whether product is in CSV
        try:
            from core.product_selector import select_product_for_user
            from core.journey_builder import find_similar_product

            # Use find_similar_product directly to match based on user behavior
            if "Behaviour data not found" not in row['user_behaviour']:
                matched_product, similarity = find_similar_product(row['user_behaviour'], products)
            else:
                # Fall back to product selector if no behavior data
                matched_product, similarity = select_product_for_user(row, products)
        except Exception as e:
            print(f"Error finding similar product for user {idx}: {str(e)}")
            # Fall back to random product selection if product selector fails
            import random
            matched_product = random.choice(products)
            similarity = 0

        # Store the mapping
        user_product_mapping[idx] = {
            'product': matched_product,
            'similarity': similarity,
            'has_behavior': has_behavior_data
        }

        # Get email template for this product to use as reference
        email_template = None
        email_template_content = ""

        # Try to find an email template for this product
        product_name = matched_product.get('Product_Name', '')
        template_stages = ["new_visitor", "product_page_viewed", "cart_abandoned", "product_purchased"]
        for stage in template_stages:
            template_file = f'data/templates/{stage}.json'
            if os.path.exists(template_file):
                try:
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        # Find a template for this product
                        matching_templates = [t for t in templates if t.get('product_data', {}).get('Product_Name') == product_name]
                        if matching_templates:
                            email_template = matching_templates[0]
                            if email_template and 'template' in email_template:
                                email_template_content = email_template['template'].get('body', '')
                                # We found a template, no need to check other stages
                                break
                except Exception as e:
                    print(f"Error loading email template from {template_file}: {str(e)}")

        # If no specific template was found, try to find any template
        if not email_template_content:
            for stage in template_stages:
                template_file = f'data/templates/{stage}.json'
                if os.path.exists(template_file):
                    try:
                        with open(template_file, 'r') as f:
                            templates = json.load(f)
                            if templates and len(templates) > 0:
                                email_template = templates[0]
                                if email_template and 'template' in email_template:
                                    email_template_content = email_template['template'].get('body', '')
                                    # We found a template, no need to check other stages
                                    break
                    except Exception as e:
                        print(f"Error loading email template from {template_file}: {str(e)}")

        # Create the user message
        user_message = f"""
        Generate a short, personalized WhatsApp message for {row['first_name']} about {matched_product.get('Product_Name', '')}.

        Product Details:
        Product Name: {matched_product.get('Product_Name', '')}
        Product URL: {matched_product.get('Product_URL', '')}
        Product Summary: {matched_product.get('Product_Description', '')}
        Product Features:
        {chr(10).join('- ' + feature for feature in matched_product.get('Product_Features', []))}

        User Details:
        First Name: {row['first_name']}
        User Behavior: {row['user_behaviour'] if has_behavior_data else f"Interested in {matched_product.get('Product_Name')}"}

        Reference Email Template:
        {email_template_content}

        Instructions:
        1. Write a short, engaging WhatsApp message (2-3 lines maximum)
        2. Focus on the most relevant product benefits for this user based on their behavior
        3. Keep it personal and conversational
        4. Do not include any links or formatting in the WhatsApp message
        5. Use the email template as reference for accurate product information and tone
        6. Only mention features that are actually part of the product (from product details or email template)
        7. Ensure the message is personalized and relevant to the user's specific interests or behavior
        """

        # Create the task
        task = {
            "custom_id": f"task-{idx}",
            "method": "POST",
            "url": "/v1/chat/completions",
            "body": {
                "model": "gpt-4o-mini",
                "temperature": 0.7,
                "response_format": {"type": "json_object"},
                "messages": [
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_message
                    }
                ]
            }
        }

        tasks.append(task)

        # Update progress
        if progress_callback and idx % 10 == 0:
            progress_callback(idx, len(data_df), f"Processed {idx}/{len(data_df)} users...")

    # Report progress
    if progress_callback:
        progress_callback(len(data_df), len(data_df),
                         f"Completed processing all users. Created {len(tasks)} batch tasks.")

    # Skip if no tasks to process
    if not tasks:
        print("No tasks to process, skipping batch processing")
        if progress_callback:
            progress_callback(0, len(data_df), "No users to process in batches.")
        return data_df

    # Split tasks into smaller batches for faster processing
    batched_tasks = [tasks[i:i + batch_size] for i in range(0, len(tasks), batch_size)]

    # Report progress
    if progress_callback:
        progress_callback(0, len(data_df), f"Processing {len(tasks)} users in {len(batched_tasks)} batches of size {batch_size}...")

    # Process each batch
    batch_results = []

    for batch_index, batch in enumerate(batched_tasks):
        # Create batch file
        batch_file_path = f"data/whatsapp_batch_tasks_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{batch_index}.jsonl"
        os.makedirs("data", exist_ok=True)

        # Report progress
        if progress_callback:
            progress_callback(batch_index, len(batched_tasks), f"Creating batch file {batch_index+1}/{len(batched_tasks)}...")

        # Write batch file
        with open(batch_file_path, 'w') as file:
            for task in batch:
                file.write(json.dumps(task) + '\n')

        # Report progress
        if progress_callback:
            progress_callback(batch_index, len(batched_tasks), f"Uploading batch file {batch_index+1}/{len(batched_tasks)}...")

        # Upload batch file
        batch_file = client.files.create(
            file=open(batch_file_path, "rb"),
            purpose="batch"
        )

        # Report progress
        if progress_callback:
            progress_callback(batch_index, len(batched_tasks), f"Creating batch job {batch_index+1}/{len(batched_tasks)}...")

        # Create batch job
        batch_job = client.batches.create(
            input_file_id=batch_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h"
        )

        # Store batch job info
        batch_results.append({
            "batch_index": batch_index,
            "batch_job": batch_job,
            "batch_file_path": batch_file_path,
            "tasks": batch
        })

    # Process batch results
    completed_batches = 0
    all_results = []

    # Report progress
    if progress_callback:
        progress_callback(0, len(batched_tasks), "Waiting for batch jobs to complete...")

    # Wait for batch jobs to complete
    while completed_batches < len(batch_results):
        # Update progress message to show we're still waiting
        if progress_callback:
            progress_callback(completed_batches, len(batched_tasks),
                             f"Waiting for all batch jobs to complete... ({completed_batches}/{len(batch_results)} done)")

        # Check each batch job status
        for batch_info in batch_results:
            # Skip already completed batches
            if "completed" in batch_info:
                continue

            # Check batch job status
            batch_job = client.batches.retrieve(batch_info["batch_job"].id)
            batch_info["status"] = batch_job.status

            # Update progress
            if progress_callback:
                progress_callback(completed_batches, len(batched_tasks),
                                 f"Batch {batch_info['batch_index']+1}/{len(batched_tasks)} status: {batch_job.status}")

            # If completed, process results
            if batch_job.status == "completed":
                # Mark as completed
                batch_info["completed"] = True
                completed_batches += 1

                # Report progress
                if progress_callback:
                    progress_callback(completed_batches, len(batched_tasks),
                                     f"Retrieving results for batch {batch_info['batch_index']+1}/{len(batched_tasks)}...")

                # Get result file
                result_file_id = batch_job.output_file_id
                result_content = client.files.content(result_file_id).content

                # Save result file
                result_file_path = f"data/whatsapp_batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{batch_info['batch_index']}.jsonl"
                with open(result_file_path, 'wb') as file:
                    file.write(result_content)

                # Parse results
                batch_results_list = []
                with open(result_file_path, 'r') as file:
                    for line in file:
                        batch_results_list.append(json.loads(line.strip()))

                # Store results
                batch_info["results"] = batch_results_list
                all_results.extend(batch_results_list)

        # If all batches are completed, break
        if completed_batches == len(batch_results):
            break

        # Wait before checking again
        time.sleep(5)

    # Report progress
    if progress_callback:
        progress_callback(completed_batches, len(batched_tasks),
                         f"Completed {completed_batches}/{len(batched_tasks)} batches. Processing results...")

    # Track processed rows for progress reporting
    processed_rows = 0
    total_rows = len(all_results)

    # Process all results
    for result in all_results:
        # Get task ID and index
        task_id = result['custom_id']
        idx = int(task_id.split('-')[1])

        # Update progress with row count
        processed_rows += 1
        if progress_callback:
            progress_callback(processed_rows, total_rows,
                             f"Processing message {processed_rows}/{total_rows} (row {idx})...")

        # Get response content
        try:
            response_body = result['response']['body']

            if not isinstance(response_body, dict):
                response_body = json.loads(response_body) if isinstance(response_body, str) else {}

            choices = response_body.get('choices', [])

            if not choices:
                raise ValueError("No choices in response")

            message = choices[0].get('message', {})
            content = message.get('content', '')

            # Handle different content types
            if isinstance(content, (int, float)):
                content = str(content)

            response_content = json.loads(content) if content else {}

            # Extract WhatsApp content
            whatsapp_content = response_content.get('content', '')

            # Update DataFrame with WhatsApp content
            data_df.at[idx, 'WhatsApp_Content'] = whatsapp_content
            data_df.at[idx, 'Matched_Product'] = user_product_mapping[idx]['product'].get('Product_Name', '')
            data_df.at[idx, 'Similarity_Score'] = round(float(user_product_mapping[idx]['similarity']) * 100, 2)

            # Get template ID from WhatsApp template manager
            try:
                from core.whatsapp_template_manager import WhatsAppTemplateManager
                template_manager = WhatsAppTemplateManager()

                # Get product name
                product_name = user_product_mapping[idx]['product'].get('Product_Name', '')

                # Get template ID for this product
                template_id = template_manager.get_template_id_for_product(product_name)

                # Update DataFrame with template ID
                data_df.at[idx, 'Template_ID'] = template_id
            except Exception as e:
                print(f"Error getting template ID for product {product_name}: {str(e)}")
                data_df.at[idx, 'Template_ID'] = ""

        except Exception as e:
            print(f"Error processing result for task {task_id}: {str(e)}")
            data_df.at[idx, 'WhatsApp_Content'] = f"Error: {str(e)}"

    # Add the WhatsApp parameters to the DataFrame
    for idx, row in data_df.iterrows():
        # Set the parameters for WhatsApp template
        data_df.at[idx, 'param_1'] = row['first_name']  # {{1}} is first name
        data_df.at[idx, 'param_2'] = row.get('WhatsApp_Content', '')  # {{2}} is personalized content

    return data_df

def process_whatsapp_data(data_df, progress_callback=None, use_batch_api=True):
    """
    Process WhatsApp data for generating personalized campaigns.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        progress_callback (callable, optional): Function to report progress (current, total, message)
        use_batch_api (bool): Whether to use OpenAI's Batch API for faster processing

    Returns:
        pd.DataFrame: DataFrame with generated WhatsApp content
    """
    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load all products
    products = []
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
            if not isinstance(products, list):
                products = [products]
            if org_filter_enabled and org_url:
                products = [p for p in products if p.get("Company_URL", "") == org_url]
    except Exception as e:
        st.error("Error loading product details")
        return data_df

    if not products:
        if org_filter_enabled and org_url:
            st.error(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
        else:
            st.error("No products found. Please add products first.")
        return data_df

    # Use batch API if enabled
    if use_batch_api:
        try:
            return generate_whatsapp_batch(data_df, products, progress_callback)
        except Exception as e:
            st.error(f"Error using batch API: {str(e)}")
            use_batch_api = False

    # If batch API is disabled or failed, use sequential processing
    if not use_batch_api:
        # Process each user sequentially
        total_users = len(data_df)
        for idx, row in data_df.iterrows():
            if progress_callback:
                progress_callback(idx, total_users, f"Processing user: {row['first_name']}")

            # Check if user has behavior data
            has_behavior_data = ("Behaviour data not found" not in row['user_behaviour']) or ("last opened an email" in row['user_behaviour'])

            # Always use product selector based on similarity, regardless of whether product is in CSV
            try:
                from core.product_selector import select_product_for_user
                from core.journey_builder import find_similar_product

                # Use find_similar_product directly to match based on user behavior
                if "Behaviour data not found" not in row['user_behaviour']:
                    matched_product, similarity = find_similar_product(row['user_behaviour'], products)
                else:
                    # Fall back to product selector if no behavior data
                    matched_product, similarity = select_product_for_user(row, products)
            except Exception as e:
                print(f"Error finding similar product for user {idx}: {str(e)}")
                # Fall back to random product selection if product selector fails
                import random
                matched_product = random.choice(products)
                similarity = 0

            # Generate WhatsApp content
            try:
                # Get email template for this product to use as reference
                email_template = None
                email_template_content = ""

                # Try to find an email template for this product
                product_name = matched_product.get('Product_Name', '')
                template_stages = ["new_visitor", "product_page_viewed", "cart_abandoned", "product_purchased"]
                for stage in template_stages:
                    template_file = f'data/templates/{stage}.json'
                    if os.path.exists(template_file):
                        try:
                            with open(template_file, 'r') as f:
                                templates = json.load(f)
                                # Find a template for this product
                                matching_templates = [t for t in templates if t.get('product_data', {}).get('Product_Name') == product_name]
                                if matching_templates:
                                    email_template = matching_templates[0]
                                    if email_template and 'template' in email_template:
                                        email_template_content = email_template['template'].get('body', '')
                                        # We found a template, no need to check other stages
                                        break
                        except Exception as e:
                            print(f"Error loading email template from {template_file}: {str(e)}")

                # If no specific template was found, try to find any template
                if not email_template_content:
                    for stage in template_stages:
                        template_file = f'data/templates/{stage}.json'
                        if os.path.exists(template_file):
                            try:
                                with open(template_file, 'r') as f:
                                    templates = json.load(f)
                                    if templates and len(templates) > 0:
                                        email_template = templates[0]
                                        if email_template and 'template' in email_template:
                                            email_template_content = email_template['template'].get('body', '')
                                            # We found a template, no need to check other stages
                                            break
                            except Exception as e:
                                print(f"Error loading email template from {template_file}: {str(e)}")

                # Create a prompt for generating WhatsApp content
                prompt = f"""
                Generate a short, personalized WhatsApp message for {row['first_name']} about {matched_product.get('Product_Name', '')}.

                Product Details:
                Product Name: {matched_product.get('Product_Name', '')}
                Product URL: {matched_product.get('Product_URL', '')}
                Product Summary: {matched_product.get('Product_Description', '')}
                Product Features:
                {chr(10).join('- ' + feature for feature in matched_product.get('Product_Features', []))}

                User Details:
                First Name: {row['first_name']}
                User Behavior: {row['user_behaviour'] if has_behavior_data else f"Interested in {matched_product.get('Product_Name')}"}

                Reference Email Template:
                {email_template_content}

                Instructions:
                1. Write a short, engaging WhatsApp message (2-3 lines maximum)
                2. Focus on the most relevant product benefits for this user based on their behavior
                3. Keep it personal and conversational
                4. Do not include any links or formatting in the WhatsApp message
                5. Use the email template as reference for accurate product information and tone
                6. Only mention features that are actually part of the product (from product details or email template)
                7. Ensure the message is personalized and relevant to the user's specific interests or behavior
                """

                # Call OpenAI API directly
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    temperature=0.7,
                    response_format={"type": "json_object"},
                    messages=[
                        {
                            "role": "system",
                            "content": """
                            You are an expert WhatsApp marketing specialist. Your task is to create personalized WhatsApp messages
                            based on user behavior, product information, and existing email templates.

                            Generate a short, concise WhatsApp message for the specified product. The message should be 2-3 lines maximum.

                            Your response should be in JSON format with the following structure:
                            {
                                "content": "Your personalized WhatsApp message content here"
                            }

                            IMPORTANT INSTRUCTIONS:
                            1. DO NOT include any greetings (like "Hi", "Hello", "Dear") or salutations (like "Thanks", "Regards", etc.)
                            2. The template already has greeting with {{1}} for the name and closing text
                            3. Your message will be inserted as {{2}} in the middle of the template
                            4. Start directly with the personalized content about the product
                            5. Focus only on the key benefits relevant to the user's behavior
                            6. ONLY generate the message content - no greeting, no salutation, no signature
                            7. The message should be ONLY the core content about the product benefits
                            8. Keep it under 160 characters total
                            9. Use the email template as a reference for tone, style, and key selling points
                            10. ONLY mention features that are actually mentioned in the product details or email template
                            11. DO NOT invent or make up features that aren't mentioned in the provided information
                            12. Focus on personalization based on user behavior if available

                            Example of what NOT to do:
                            "Hi {{1}}! Check out our amazing product with features X and Y. Thanks, Team Z"

                            Example of what TO do:
                            "Our product offers features X and Y that solve your specific problem with impressive results."

                            Keep the message concise, engaging, and focused on the product's benefits that match the user's interests.
                            Make sure to include only the most relevant product features and benefits.
                            Do not include any links or formatting that wouldn't work in WhatsApp.
                            """
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                )

                # Extract content from response
                content = response.choices[0].message.content
                response_content = json.loads(content)
                whatsapp_content = response_content.get('content', '')

                # Update DataFrame with WhatsApp content
                data_df.at[idx, 'WhatsApp_Content'] = whatsapp_content
                data_df.at[idx, 'Matched_Product'] = matched_product.get('Product_Name', '')
                data_df.at[idx, 'Similarity_Score'] = round(float(similarity) * 100, 2)

                # Get template ID from WhatsApp template manager
                try:
                    from core.whatsapp_template_manager import WhatsAppTemplateManager
                    template_manager = WhatsAppTemplateManager()

                    # Get product name
                    product_name = matched_product.get('Product_Name', '')

                    # Get template ID for this product
                    template_id = template_manager.get_template_id_for_product(product_name)

                    # Update DataFrame with template ID
                    data_df.at[idx, 'Template_ID'] = template_id
                except Exception as e:
                    print(f"Error getting template ID for product {product_name}: {str(e)}")
                    data_df.at[idx, 'Template_ID'] = ""

                # Set the parameters for WhatsApp template
                data_df.at[idx, 'param_1'] = row['first_name']  # {{1}} is first name
                data_df.at[idx, 'param_2'] = whatsapp_content  # {{2}} is personalized content

            except Exception as e:
                st.error(f"Error generating WhatsApp content for user {row['first_name']}: {str(e)}")
                data_df.at[idx, 'WhatsApp_Content'] = f"Error: {str(e)}"
                data_df.at[idx, 'Template_ID'] = ""
                data_df.at[idx, 'param_1'] = row['first_name']
                data_df.at[idx, 'param_2'] = ""

    return data_df
