"""
Core trigger points functionality for OpenEngage.
"""
import json
import os
from datetime import datetime
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, date

from .data_integration import DataIntegration
from .user_data_processor import UserDataProcessor

class TriggerPointsManager:
    def __init__(self):
        self.data_integration = DataIntegration()
        self.user_processor = UserDataProcessor()
        
    def _load_brahma_data(self) -> pd.DataFrame:
        """Load and preprocess brahma data"""
        return pd.read_csv(self.data_integration.brahma_data_path)
    
    def _get_product_data(self, product_name: str) -> Dict[str, pd.DataFrame]:
        """Get buyers and leads data for a specific product"""
        product_mapping = {
            "Certified AI/ML BlackBelt Plus Program": ("bb_buyers_path", "bb_leads_path"),
            "GenAI Pinnacle Plus Program": ("genai_buyers_path", "pinnacle_leads_path"),
            "Agentic AI Pioneer Program": ("agentic_buyers_path", "agentic_leads_path")
        }
        
        if product_name not in product_mapping:
            raise ValueError(f"Unknown product: {product_name}")
            
        buyers_path, leads_path = product_mapping[product_name]
        buyers_df = pd.read_csv(getattr(self.data_integration, buyers_path))
        leads_df = pd.read_csv(getattr(self.data_integration, leads_path))
        
        return {"buyers": buyers_df, "leads": leads_df}

    def segment_users(self, trigger_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Segment users based on trigger configuration.
        
        Args:
            trigger_config: Dictionary containing trigger configuration
            
        Returns:
            List of user dictionaries matching the trigger criteria
        """
        # Get all users initially
        all_users = pd.read_csv(self.data_integration.target_audience_path)
        users_df = all_users.copy()
        
        # Apply each condition with AND logic
        for condition in trigger_config["conditions"]:
            condition_type = condition["condition_type"]
            details = condition["trigger_details"]
            
            filtered_users = None
            
            if condition_type == "Activity in product funnel":
                filtered_users = self._segment_by_product_activity(details)
            elif condition_type == "User done something on website":
                filtered_users = self._segment_by_website_activity(details)
            elif condition_type == "User responded the previous campaign":
                filtered_users = self._segment_by_campaign_response(details)
            elif condition_type == "User demographic":
                filtered_users = self._segment_by_demographics(details, users_df)
            elif condition_type == "Is inactive":
                filtered_users = self._segment_inactive_users()
            
            if filtered_users is not None:
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(filtered_users, list):
                    filtered_users = pd.DataFrame(filtered_users)
                
                # Get common users (AND operation)
                users_df = users_df.merge(filtered_users, on="user_email", how="inner")
        
        return users_df.to_dict("records")

    def _segment_by_product_activity(self, details: Dict[str, Any]) -> pd.DataFrame:
        """Segment users based on product funnel activity"""
        product_data = self._get_product_data(details["product"])
        
        if details["activity_type"] == "Is Buyer":
            users = product_data["buyers"]
        elif details["activity_type"] == "Is Lead":
            users = product_data["leads"]
        else:  # Is Pagevisitor
            brahma_data = self._load_brahma_data()
            product_urls = {
                "Certified AI/ML BlackBelt Plus Program": "/blackbelt",
                "GenAI Pinnacle Plus Program": "/genai-pinnacle",
                "Agentic AI Pioneer Program": "/agentic-ai"
            }
            url_pattern = product_urls[details["product"]]
            users = brahma_data[brahma_data["http_referer"].str.contains(url_pattern, na=False)]
        
        return users[["user_email"]].drop_duplicates()

    def _segment_by_website_activity(self, details: Dict[str, Any]) -> pd.DataFrame:
        """Segment users based on website activity"""
        brahma_data = self._load_brahma_data()
        
        # Filter by action type
        if details["action"] == "Pageview":
            events = brahma_data[brahma_data["event_type"] == "pageload"]
        else:  # Click
            events = brahma_data[brahma_data["event_type"] == "click"]
            
        # Apply URL filter if specified
        if details["url_contains"]:
            events = events[events["http_referer"].str.contains(details["url_contains"], na=False)]
            
        # Apply time duration filter if specified
        if details["time_duration"]:
            # Parse time filter (assuming format like "last 7 days")
            try:
                days = int(details["time_duration"].split()[1])
                cutoff_date = pd.Timestamp.now() - pd.Timedelta(days=days)
                events = events[pd.to_datetime(events["current_server_time"]) >= cutoff_date]
            except:
                pass
                
        return events[["brahma_session_id"]].rename(columns={"brahma_session_id": "user_email"}).drop_duplicates()

    def _segment_by_campaign_response(self, details: Dict[str, Any]) -> pd.DataFrame:
        """Segment users based on campaign response"""
        # This would integrate with your email campaign tracking system
        # For now, returning empty DataFrame as placeholder
        return pd.DataFrame(columns=["user_email"])

    def _segment_by_demographics(self, details: Dict[str, Any], users_df: pd.DataFrame) -> pd.DataFrame:
        """Segment users based on demographic data"""
        if details["attribute"] == "Country":
            return users_df[users_df["country_name"] == details["filter_value"]][["user_email"]]
            
        elif details["attribute"] == "Age is between":
            # Calculate age from date_joined (assuming it contains birth date)
            # In a real implementation, you might want to store age or birth date separately
            today = date.today()
            users_df["age"] = (today - pd.to_datetime(users_df["date_joined"]).dt.date).dt.days // 365
            
            return users_df[
                (users_df["age"] >= details["min_age"]) & 
                (users_df["age"] <= details["max_age"])
            ][["user_email"]]
            
        elif details["attribute"] == "Signup Date":
            filter_date = pd.to_datetime(details["filter_value"]).date()
            return users_df[pd.to_datetime(users_df["date_joined"]).dt.date == filter_date][["user_email"]]
            
        return users_df[["user_email"]]

    def _segment_inactive_users(self) -> pd.DataFrame:
        """Segment inactive users"""
        brahma_data = self._load_brahma_data()
        recent_active_users = brahma_data[
            pd.to_datetime(brahma_data["current_server_time"]) >= pd.Timestamp.now() - pd.Timedelta(days=30)
        ]
        all_users = pd.read_csv(self.data_integration.target_audience_path)
        
        inactive_users = all_users[~all_users["user_email"].isin(recent_active_users["brahma_session_id"])]
        return inactive_users[["user_email"]]

# Global instance
_manager = TriggerPointsManager()

def save_trigger_action(trigger_action: Dict[str, Any]) -> str:
    """Save a trigger action"""
    file_path = "data/trigger_points.json"
    
    # Create data directory if it doesn't exist
    if not os.path.exists("data"):
        os.makedirs("data")
        
    # Load existing triggers
    try:
        with open(file_path, "r") as f:
            triggers = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        triggers = []
        
    # Add new trigger
    trigger_id = datetime.now().strftime("%Y%m%d%H%M%S")
    trigger_action["id"] = trigger_id
    triggers.append(trigger_action)
    
    # Save updated triggers
    with open(file_path, "w") as f:
        json.dump(triggers, f, indent=4)
        
    return trigger_id

def get_trigger_actions() -> List[Dict[str, Any]]:
    """Get all trigger actions"""
    file_path = "data/trigger_points.json"
    try:
        with open(file_path, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def get_trigger_action(trigger_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific trigger action"""
    triggers = get_trigger_actions()
    return next((t for t in triggers if t["id"] == trigger_id), None)

def delete_trigger_action(trigger_id: str) -> bool:
    """Delete a trigger action"""
    file_path = "data/trigger_points.json"
    triggers = get_trigger_actions()
    
    original_length = len(triggers)
    triggers = [t for t in triggers if t["id"] != trigger_id]
    
    if len(triggers) < original_length:
        with open(file_path, "w") as f:
            json.dump(triggers, f, indent=4)
        return True
        
    return False

def segment_users_by_trigger(trigger_id: str) -> List[Dict[str, Any]]:
    """Get users matching a trigger's criteria"""
    trigger = get_trigger_action(trigger_id)
    if not trigger:
        return []
    
    # Convert old format to new format if needed
    if "conditions" not in trigger and "condition_type" in trigger:
        trigger = {
            "id": trigger["id"],
            "action_type": trigger["action_type"],
            "conditions": [{
                "condition_type": trigger["condition_type"],
                "trigger_details": trigger["trigger_details"]
            }],
            "combination_type": "AND",
            "status": trigger.get("status", "Active")
        }
        
    return _manager.segment_users(trigger)

def fire_trigger(trigger_id: str) -> Dict[str, Any]:
    """Execute a trigger's action on matching users"""
    trigger = get_trigger_action(trigger_id)
    if not trigger:
        return {"success": False, "error": "Trigger not found"}
        
    users = segment_users_by_trigger(trigger_id)
    
    if trigger["action_type"] == "Send Campaign":
        # Integrate with your campaign sending system
        pass
    elif trigger["action_type"] == "Update User Behaviour":
        # Update user behavior data
        pass
        
    return {
        "success": True,
        "affected_users": len(users)
    }
