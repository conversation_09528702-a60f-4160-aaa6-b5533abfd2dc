"""
Campaign Outcome Prediction

This module provides functionality to predict campaign performance metrics
(Open Rate, Click Rate, Unsubscribe Rate) based on historical data.
"""

import os
import pandas as pd
import numpy as np
import logging
import re
import json
from datetime import datetime
from pathlib import Path
from glob import glob
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CampaignPredictor:
    """
    A class to predict campaign performance metrics based on historical data.
    """
    
    def __init__(self):
        """Initialize the CampaignPredictor."""
        self.models = {
            'open_rate': None,
            'click_rate': None,
            'unsub_rate': None
        }
        self.feature_columns = None
        self.preprocessor = None
        self.model_trained = False
        self.training_metrics = {}
        
    def _extract_features_from_campaign(self, campaign_df):
        """
        Extract features from campaign data for prediction.
        
        Args:
            campaign_df (pd.DataFrame): DataFrame containing campaign data
            
        Returns:
            pd.DataFrame: DataFrame with extracted features
        """
        features = pd.DataFrame()
        
        # Extract time-based features
        if 'Send_Time' in campaign_df.columns:
            campaign_df['Send_Time'] = pd.to_datetime(campaign_df['Send_Time'])
            features['hour_of_day'] = campaign_df['Send_Time'].dt.hour
            features['day_of_week'] = campaign_df['Send_Time'].dt.dayofweek
            features['month'] = campaign_df['Send_Time'].dt.month
            
        # Extract subject line features
        if 'Subject' in campaign_df.columns:
            # Subject line length
            features['subject_length'] = campaign_df['Subject'].str.len()
            
            # Check for personalization in subject
            features['subject_has_personalization'] = campaign_df['Subject'].str.contains(r'\{.*?\}', regex=True).astype(int)
            
            # Check for question mark in subject
            features['subject_has_question'] = campaign_df['Subject'].str.contains(r'\?', regex=True).astype(int)
            
            # Check for exclamation mark in subject
            features['subject_has_exclamation'] = campaign_df['Subject'].str.contains(r'!', regex=True).astype(int)
            
            # Check for numbers in subject
            features['subject_has_numbers'] = campaign_df['Subject'].str.contains(r'\d', regex=True).astype(int)
            
        # Extract email content features
        if 'Mail_Content' in campaign_df.columns:
            # Email content length
            features['content_length'] = campaign_df['Mail_Content'].str.len()
            
            # Count links in content
            features['link_count'] = campaign_df['Mail_Content'].str.count(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
            
            # Check for personalization in content
            features['content_has_personalization'] = campaign_df['Mail_Content'].str.contains(r'\{.*?\}', regex=True).astype(int)
            
        # Extract user behavior features
        if 'user_behaviour' in campaign_df.columns:
            # Convert user behavior to numeric
            behavior_map = {
                'active': 3,
                'semi-active': 2,
                'inactive': 1,
                'new': 0
            }
            features['user_behavior'] = campaign_df['user_behaviour'].map(behavior_map).fillna(0)
            
        # Extract user stage features
        if 'user_stage' in campaign_df.columns:
            # One-hot encode user stage
            features['user_stage'] = campaign_df['user_stage']
            
        return features
    
    def _prepare_training_data(self, performance_df):
        """
        Prepare training data from performance data.
        
        Args:
            performance_df (pd.DataFrame): DataFrame containing performance data
            
        Returns:
            tuple: X (features) and y (targets) for training
        """
        # Extract features
        features = self._extract_features_from_campaign(performance_df)
        
        # Prepare target variables
        targets = pd.DataFrame()
        
        # Calculate open rate
        if 'Open_Time' in performance_df.columns:
            targets['open_rate'] = performance_df['Open_Time'].notna().astype(int)
            
        # Calculate click rate
        if 'Click_Time' in performance_df.columns:
            targets['click_rate'] = performance_df['Click_Time'].notna().astype(int)
            
        # Calculate unsubscribe rate
        if 'Unsubscribe_Time' in performance_df.columns:
            targets['unsub_rate'] = performance_df['Unsubscribe_Time'].notna().astype(int)
        
        # Store feature columns for prediction
        self.feature_columns = features.columns.tolist()
        
        # Create preprocessor for categorical features
        categorical_features = ['user_stage'] if 'user_stage' in features.columns else []
        numeric_features = [col for col in features.columns if col not in categorical_features]
        
        transformers = []
        
        if numeric_features:
            transformers.append(('num', StandardScaler(), numeric_features))
            
        if categorical_features:
            transformers.append(('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features))
            
        self.preprocessor = ColumnTransformer(transformers)
        
        # Preprocess features
        X = self.preprocessor.fit_transform(features)
        
        return X, targets
    
    def train_models(self, performance_file=None):
        """
        Train prediction models using historical performance data.
        
        Args:
            performance_file (str, optional): Path to the performance data file.
                If None, will use the latest file in data/mail_performance/combined/.
                
        Returns:
            bool: True if training was successful, False otherwise
        """
        try:
            # Find the performance file if not provided
            if performance_file is None:
                performance_files = glob("data/mail_performance/combined/all_performance_*.csv")
                if not performance_files:
                    logger.warning("No performance data files found")
                    return False
                performance_file = max(performance_files)  # Get the most recent file
                
            logger.info(f"Training models using performance data from: {performance_file}")
            
            # Load performance data
            performance_df = pd.read_csv(performance_file)
            
            # Check if we have enough data for training
            if len(performance_df) < 50:
                logger.warning(f"Not enough data for training ({len(performance_df)} records). Need at least 50.")
                return False
            
            # Prepare training data
            X, targets = self._prepare_training_data(performance_df)
            
            # Train models for each target
            for target in ['open_rate', 'click_rate', 'unsub_rate']:
                if target in targets.columns:
                    # Split data
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, targets[target], test_size=0.2, random_state=42
                    )
                    
                    # Create and train model
                    model = RandomForestRegressor(n_estimators=100, random_state=42)
                    model.fit(X_train, y_train)
                    
                    # Evaluate model
                    y_pred = model.predict(X_test)
                    mse = mean_squared_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                    
                    # Store model and metrics
                    self.models[target] = model
                    self.training_metrics[target] = {
                        'mse': mse,
                        'r2': r2,
                        'baseline': targets[target].mean()
                    }
                    
                    logger.info(f"Trained {target} model. MSE: {mse:.4f}, R²: {r2:.4f}")
                else:
                    logger.warning(f"Target '{target}' not found in performance data")
            
            self.model_trained = True
            
            # Save model metadata
            self._save_model_metadata()
            
            return True
            
        except Exception as e:
            logger.error(f"Error training models: {str(e)}")
            return False
    
    def _save_model_metadata(self):
        """Save model metadata to a JSON file."""
        metadata = {
            'trained_at': datetime.now().isoformat(),
            'feature_columns': self.feature_columns,
            'metrics': self.training_metrics
        }
        
        os.makedirs("data/models", exist_ok=True)
        with open("data/models/campaign_predictor_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
        logger.info("Saved model metadata to data/models/campaign_predictor_metadata.json")
    
    def predict_campaign_performance(self, campaign_file=None):
        """
        Predict performance metrics for a campaign.
        
        Args:
            campaign_file (str, optional): Path to the campaign file.
                If None, will use the latest file in data/campaign_results/.
                
        Returns:
            dict: Predicted performance metrics
        """
        try:
            # Check if models are trained
            if not self.model_trained:
                logger.warning("Models are not trained. Training now...")
                if not self.train_models():
                    return {
                        'success': False,
                        'error': 'Failed to train models'
                    }
            
            # Find the campaign file if not provided
            if campaign_file is None:
                campaign_files = glob("data/campaign_results/*.csv")
                if not campaign_files:
                    logger.warning("No campaign files found")
                    return {
                        'success': False,
                        'error': 'No campaign files found'
                    }
                campaign_file = max(campaign_files)  # Get the most recent file
                
            logger.info(f"Predicting performance for campaign: {campaign_file}")
            
            # Load campaign data
            campaign_df = pd.read_csv(campaign_file)
            
            # Extract features
            features = self._extract_features_from_campaign(campaign_df)
            
            # Check if we have the necessary features
            missing_features = [col for col in self.feature_columns if col not in features.columns]
            if missing_features:
                logger.warning(f"Missing features: {missing_features}")
                # Add missing features with default values
                for col in missing_features:
                    features[col] = 0
            
            # Ensure features are in the same order as during training
            features = features[self.feature_columns]
            
            # Preprocess features
            X = self.preprocessor.transform(features)
            
            # Make predictions
            predictions = {}
            for target, model in self.models.items():
                if model is not None:
                    # Predict for each row
                    row_predictions = model.predict(X)
                    
                    # Calculate average prediction
                    avg_prediction = np.mean(row_predictions) * 100  # Convert to percentage
                    
                    predictions[target] = avg_prediction
                    
                    logger.info(f"Predicted {target}: {avg_prediction:.2f}%")
                else:
                    logger.warning(f"No model available for {target}")
            
            # Get campaign metadata
            campaign_name = os.path.basename(campaign_file).replace('.csv', '')
            campaign_date = re.search(r'(\d{8})', campaign_name)
            campaign_date = campaign_date.group(1) if campaign_date else 'Unknown'
            
            # Format date if available
            if campaign_date != 'Unknown':
                try:
                    date_obj = datetime.strptime(campaign_date, '%Y%m%d')
                    campaign_date = date_obj.strftime('%B %d, %Y')
                except:
                    pass
            
            # Get campaign size
            campaign_size = len(campaign_df)
            
            return {
                'success': True,
                'campaign_name': campaign_name,
                'campaign_date': campaign_date,
                'campaign_size': campaign_size,
                'predictions': predictions,
                'training_metrics': self.training_metrics
            }
            
        except Exception as e:
            logger.error(f"Error predicting campaign performance: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
