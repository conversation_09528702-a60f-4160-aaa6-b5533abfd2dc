"""
Channel selection algorithm for OpenEngage marketing automation platform.

This module provides functionality to select the optimal marketing channel (Email or WhatsApp)
for each user based on various factors including channel availability, past engagement rates,
content type, and urgency.
"""
import os
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChannelSelector:
    """
    Channel selection algorithm to determine the optimal channel (Email or WhatsApp) for each user.
    """
    
    # Channel types
    EMAIL = "email"
    WHATSAPP = "whatsapp"
    
    # Weights for different factors in channel selection
    DEFAULT_WEIGHTS = {
        "availability": 0.4,        # Weight for channel availability
        "engagement": 0.3,          # Weight for past engagement
        "content_suitability": 0.2, # Weight for content suitability
        "urgency": 0.1,             # Weight for message urgency
    }
    
    def __init__(self, weights: Dict[str, float] = None):
        """
        Initialize the channel selector with weights for different factors.
        
        Args:
            weights: Dictionary of weights for different factors
                    (availability, engagement, content_suitability, urgency)
        """
        self.weights = weights or self.DEFAULT_WEIGHTS
        self.logger = logging.getLogger(__name__)
        
        # Normalize weights to ensure they sum to 1
        weight_sum = sum(self.weights.values())
        for key in self.weights:
            self.weights[key] /= weight_sum
            
        self.logger.info(f"Initialized channel selector with weights: {self.weights}")
    
    def get_channel_availability(self, user_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Determine channel availability for the user.
        
        Args:
            user_data: Dictionary containing user data
        
        Returns:
            Dictionary with availability score for each channel (0-1)
        """
        availability = {
            self.EMAIL: 0.0,
            self.WHATSAPP: 0.0,
        }
        
        # Email is available if user_email exists and is not empty
        if user_data.get("user_email") and isinstance(user_data["user_email"], str) and "@" in user_data["user_email"]:
            availability[self.EMAIL] = 1.0
            
        # WhatsApp is available if phone_number exists and is not empty
        if user_data.get("phone_number") and isinstance(user_data["phone_number"], str) and len(user_data["phone_number"]) >= 10:
            availability[self.WHATSAPP] = 1.0
            
        return availability
    
    def get_channel_engagement(self, user_data: Dict[str, Any], engagement_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        Calculate engagement score for each channel based on past user interaction.
        
        Args:
            user_data: Dictionary containing user data
            engagement_data: Optional dictionary containing pre-computed engagement metrics
        
        Returns:
            Dictionary with engagement score for each channel (0-1)
        """
        # Default engagement scores (neutral)
        engagement = {
            self.EMAIL: 0.5,
            self.WHATSAPP: 0.5,
        }
        
        # If we have user-specific engagement data, use it
        if engagement_data:
            email = user_data.get("user_email", "").lower()
            phone = user_data.get("phone_number", "")
            
            # Email engagement
            if email and email in engagement_data.get("email", {}):
                user_email_data = engagement_data["email"][email]
                # Calculate engagement score based on open rate and click rate
                open_rate = user_email_data.get("open_rate", 0)
                click_rate = user_email_data.get("click_rate", 0)
                # Weighted combination of open and click rates
                engagement[self.EMAIL] = min(1.0, (0.4 * open_rate + 0.6 * click_rate) / 100)
            
            # WhatsApp engagement
            if phone and phone in engagement_data.get("whatsapp", {}):
                user_whatsapp_data = engagement_data["whatsapp"][phone]
                # Calculate engagement score based on read rate and response rate
                read_rate = user_whatsapp_data.get("read_rate", 0)
                response_rate = user_whatsapp_data.get("response_rate", 0)
                # Weighted combination of read and response rates
                engagement[self.WHATSAPP] = min(1.0, (0.3 * read_rate + 0.7 * response_rate) / 100)
        
        return engagement
    
    def get_content_suitability(self, content_type: str) -> Dict[str, float]:
        """
        Determine content suitability for each channel based on content type.
        
        Args:
            content_type: Type of content being sent
        
        Returns:
            Dictionary with suitability score for each channel (0-1)
        """
        # Default suitability (neutral for both channels)
        suitability = {
            self.EMAIL: 0.5,
            self.WHATSAPP: 0.5,
        }
        
        # Content types better suited for email
        email_suited_content = ["newsletter", "detailed_update", "visual_rich", "promotional", "long_form"]
        
        # Content types better suited for WhatsApp
        whatsapp_suited_content = ["alert", "reminder", "confirmation", "short_update", "quick_response"]
        
        # Adjust suitability based on content type
        if content_type.lower() in email_suited_content:
            suitability[self.EMAIL] = 0.8
            suitability[self.WHATSAPP] = 0.3
        elif content_type.lower() in whatsapp_suited_content:
            suitability[self.EMAIL] = 0.3
            suitability[self.WHATSAPP] = 0.8
        
        return suitability
    
    def get_urgency_factor(self, urgency_level: str) -> Dict[str, float]:
        """
        Calculate urgency factor for each channel based on urgency level.
        
        Args:
            urgency_level: Level of urgency ("low", "medium", "high")
        
        Returns:
            Dictionary with urgency factor for each channel (0-1)
        """
        urgency = {
            self.EMAIL: 0.5,
            self.WHATSAPP: 0.5,
        }
        
        if urgency_level.lower() == "high":
            # High urgency favors WhatsApp
            urgency[self.EMAIL] = 0.2
            urgency[self.WHATSAPP] = 0.9
        elif urgency_level.lower() == "medium":
            # Medium urgency slightly favors WhatsApp
            urgency[self.EMAIL] = 0.4
            urgency[self.WHATSAPP] = 0.7
        elif urgency_level.lower() == "low":
            # Low urgency favors email
            urgency[self.EMAIL] = 0.7
            urgency[self.WHATSAPP] = 0.3
        
        return urgency
    
    def select_channel(self, user_data: Dict[str, Any], 
                       content_type: str = "general", 
                       urgency_level: str = "medium",
                       engagement_data: Optional[Dict[str, Any]] = None) -> Tuple[str, Dict[str, float]]:
        """
        Select the optimal channel for the user based on various factors.
        
        Args:
            user_data: Dictionary containing user data (must include user_email and/or phone_number)
            content_type: Type of content being sent
            urgency_level: Level of urgency ("low", "medium", "high")
            engagement_data: Optional dictionary containing pre-computed engagement metrics
            
        Returns:
            Tuple of (selected_channel, scores_dict)
        """
        # Get scores for different factors
        availability = self.get_channel_availability(user_data)
        engagement = self.get_channel_engagement(user_data, engagement_data)
        content_suitability = self.get_content_suitability(content_type)
        urgency = self.get_urgency_factor(urgency_level)
        
        # Calculate weighted scores for each channel
        scores = {}
        for channel in [self.EMAIL, self.WHATSAPP]:
            # If channel is not available, score is 0
            if availability[channel] == 0:
                scores[channel] = 0
                continue
                
            # Calculate weighted score
            score = (
                self.weights["availability"] * availability[channel] +
                self.weights["engagement"] * engagement[channel] +
                self.weights["content_suitability"] * content_suitability[channel] +
                self.weights["urgency"] * urgency[channel]
            )
            scores[channel] = score
        
        # Get the channel with the highest score
        if scores[self.EMAIL] >= scores[self.WHATSAPP]:
            selected_channel = self.EMAIL
        else:
            selected_channel = self.WHATSAPP
            
        # If selected channel has no availability, use the alternative channel if available
        if availability[selected_channel] == 0:
            alternative = self.WHATSAPP if selected_channel == self.EMAIL else self.EMAIL
            if availability[alternative] > 0:
                selected_channel = alternative
            else:
                # If neither channel is available, default to email (for logging)
                selected_channel = self.EMAIL
                self.logger.warning(f"No valid channel found for user: {user_data.get('user_email', 'unknown')}")
        
        # Return selected channel and scores
        return selected_channel, scores
    
    def batch_select_channels(self, users_data: List[Dict[str, Any]], 
                             content_type: str = "general",
                             urgency_level: str = "medium") -> Dict[str, List[Dict[str, Any]]]:
        """
        Select channels for a batch of users and group them by selected channel.
        
        Args:
            users_data: List of dictionaries containing user data
            content_type: Type of content being sent
            urgency_level: Level of urgency ("low", "medium", "high")
            
        Returns:
            Dictionary with users grouped by selected channel
        """
        # Calculate engagement data for all users first
        engagement_data = self._batch_calculate_engagement(users_data)
        
        # Group users by channel
        channels = {
            self.EMAIL: [],
            self.WHATSAPP: [],
            "unavailable": []
        }
        
        # Process each user
        for user_data in users_data:
            # Skip if neither email nor phone number exists
            if not user_data.get("user_email") and not user_data.get("phone_number"):
                channels["unavailable"].append(user_data)
                continue
                
            # Select channel for user
            selected_channel, _ = self.select_channel(
                user_data, 
                content_type=content_type,
                urgency_level=urgency_level,
                engagement_data=engagement_data
            )
            
            # Add user to the appropriate channel group
            if selected_channel in channels:
                channels[selected_channel].append(user_data)
            
        return channels
    
    def _batch_calculate_engagement(self, users_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Pre-calculate engagement metrics for a batch of users.
        
        Args:
            users_data: List of dictionaries containing user data
            
        Returns:
            Dictionary with engagement data keyed by channel and user identifier
        """
        # In a real implementation, this would load engagement data from a database
        # For this prototype, we'll generate some simulated engagement data
        
        engagement_data = {
            "email": {},
            "whatsapp": {}
        }
        
        # In a real system, we would fetch this data from analytics
        # Here we'll just create some sample data
        for user_data in users_data:
            # Email engagement
            if user_data.get("user_email"):
                email = user_data["user_email"].lower()
                # Generate some random engagement metrics
                engagement_data["email"][email] = {
                    "open_rate": np.random.uniform(20, 70),  # Random open rate between 20% and 70%
                    "click_rate": np.random.uniform(5, 30),  # Random click rate between 5% and 30%
                }
                
            # WhatsApp engagement
            if user_data.get("phone_number"):
                phone = user_data["phone_number"]
                # Generate some random engagement metrics
                engagement_data["whatsapp"][phone] = {
                    "read_rate": np.random.uniform(50, 90),     # Random read rate between 50% and 90%
                    "response_rate": np.random.uniform(5, 40),  # Random response rate between 5% and 40%
                }
        
        return engagement_data


def load_user_engagement_from_performance_data(performance_file_path: str) -> Dict[str, Dict[str, Dict[str, float]]]:
    """
    Load user engagement data from the performance CSV file.
    
    Args:
        performance_file_path: Path to the performance CSV file
        
    Returns:
        Dictionary with engagement data keyed by channel and user identifier
    """
    try:
        # Check if file exists
        if not os.path.exists(performance_file_path):
            logger.warning(f"Performance file not found: {performance_file_path}")
            return {"email": {}, "whatsapp": {}}
            
        # Load performance data
        perf_df = pd.read_csv(performance_file_path)
        
        # Initialize engagement data
        engagement_data = {
            "email": {},
            "whatsapp": {}
        }
        
        # Check for required columns
        required_email_cols = ['user_email']
        if not all(col in perf_df.columns for col in required_email_cols):
            logger.warning(f"Performance file missing required columns for email data")
        else:
            # Process email engagement
            for _, row in perf_df.iterrows():
                if not pd.isna(row.get('user_email')):
                    email = row['user_email'].lower()
                    
                    # Calculate engagement metrics
                    open_rate = 0
                    click_rate = 0
                    
                    # Calculate open rate if we have send and open data
                    if not pd.isna(row.get('Send_Time')) and not pd.isna(row.get('Open_Time')):
                        open_rate = 100  # This user has opened the email
                        
                    # Calculate click rate if we have open and click data
                    if not pd.isna(row.get('Open_Time')) and not pd.isna(row.get('Click_Time')):
                        click_rate = 100  # This user has clicked in the email
                    
                    # Store engagement data
                    if email not in engagement_data["email"]:
                        engagement_data["email"][email] = {
                            "open_rate": open_rate,
                            "click_rate": click_rate,
                            "count": 1
                        }
                    else:
                        # Update with running average
                        count = engagement_data["email"][email]["count"]
                        engagement_data["email"][email]["open_rate"] = (
                            (engagement_data["email"][email]["open_rate"] * count + open_rate) / (count + 1)
                        )
                        engagement_data["email"][email]["click_rate"] = (
                            (engagement_data["email"][email]["click_rate"] * count + click_rate) / (count + 1)
                        )
                        engagement_data["email"][email]["count"] += 1
        
        # Process WhatsApp engagement if we have phone numbers and WhatsApp interaction data
        if 'phone_number' in perf_df.columns and 'WhatsApp_Read_Time' in perf_df.columns:
            for _, row in perf_df.iterrows():
                if not pd.isna(row.get('phone_number')):
                    phone = row['phone_number']
                    
                    # Calculate engagement metrics
                    read_rate = 0
                    response_rate = 0
                    
                    # Calculate read rate if we have send and read data
                    if not pd.isna(row.get('WhatsApp_Send_Time')) and not pd.isna(row.get('WhatsApp_Read_Time')):
                        read_rate = 100  # This user has read the WhatsApp message
                        
                    # Calculate response rate if we have read and response data
                    if not pd.isna(row.get('WhatsApp_Read_Time')) and not pd.isna(row.get('WhatsApp_Response_Time')):
                        response_rate = 100  # This user has responded to the WhatsApp message
                    
                    # Store engagement data
                    if phone not in engagement_data["whatsapp"]:
                        engagement_data["whatsapp"][phone] = {
                            "read_rate": read_rate,
                            "response_rate": response_rate,
                            "count": 1
                        }
                    else:
                        # Update with running average
                        count = engagement_data["whatsapp"][phone]["count"]
                        engagement_data["whatsapp"][phone]["read_rate"] = (
                            (engagement_data["whatsapp"][phone]["read_rate"] * count + read_rate) / (count + 1)
                        )
                        engagement_data["whatsapp"][phone]["response_rate"] = (
                            (engagement_data["whatsapp"][phone]["response_rate"] * count + response_rate) / (count + 1)
                        )
                        engagement_data["whatsapp"][phone]["count"] += 1
                        
        return engagement_data
    
    except Exception as e:
        logger.error(f"Error loading engagement data: {str(e)}")
        return {"email": {}, "whatsapp": {}}
