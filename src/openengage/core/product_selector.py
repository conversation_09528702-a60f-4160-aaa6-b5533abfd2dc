"""
Product selection logic for OpenEngage.
"""
from core.journey_builder import find_similar_product
import re

def select_product_for_user(user_data, products):
    """
    Select the most appropriate product for a user based on their stage and behavior.

    Args:
        user_data (dict): User data containing 'user_stage' and 'user_behaviour'
        products (list): List of product dictionaries

    Returns:
        tuple: (selected_product, similarity_score)
    """
    # Extract user stage and behavior
    user_stage = user_data.get('user_stage', '')
    user_behavior = user_data.get('user_behaviour', '')
    last_product_sent = user_data.get('last_product_sent', '')
    
    # NEW: Check for Cross_Sell flag and Cross_Sell_Product
    if 'Cross_Sell' in user_data and user_data['Cross_Sell'] == True and 'Cross_Sell_Product' in user_data and user_data['Cross_Sell_Product']:
        # User is eligible for cross-sell, use the designated cross-sell product
        cross_sell_product_name = user_data['Cross_Sell_Product']
        
        # Find the cross-sell product in the products list
        for product in products:
            if product['Product_Name'] == cross_sell_product_name:
                # Return the cross-sell product with highest confidence score
                return product, 1.0
    
    # Check if user has behavior data
    has_behavior_data = ("Behaviour data not found" not in user_behavior)
    print(has_behavior_data)

    if last_product_sent:
        for i in products:
            if i['Product_Name'] == last_product_sent:
                return i, 1.0
    # If no behavior data, randomly assign a product
    if not has_behavior_data:
        return select_by_priority(products), 0.0

    # Case 1: New Visitor - Use similarity logic
    if user_stage == 'New Visitor':
        return find_similar_product(user_behavior, products)

    # Case 2: Product Page Viewed or Product Lead Generated
    elif user_stage in ['Product Page Viewed']:
        # Try to extract product information from user behavior
        viewed_products = extract_viewed_products(user_behavior, products)

        # If we found products the user has interacted with
        if viewed_products:
            # If user has interacted with only one product, return that
            if len(viewed_products) == 1:
                return viewed_products[0], 1.0

            # If user has interacted with multiple products, use priority to decide
            elif len(viewed_products) > 1:
                return select_by_priority(viewed_products), 1.0

        # If no product information found in behavior, fall back to similarity
        return find_similar_product(user_behavior, products)
    
    # Case 2: Product Page Viewed or Product Lead Generated
    elif user_stage in ['Product Lead Generated']:
        # Try to extract product information from user behavior
        lead_products = extract_lead_products(user_behavior, products)

        # If we found products the user has interacted with
        if lead_products:
            # If user has interacted with only one product, return that
            if len(lead_products) == 1:
                return lead_products[0], 1.0

            # If user has interacted with multiple products, use priority to decide
            elif len(lead_products) > 1:
                return select_by_priority(lead_products), 1.0

        # If no product information found in behavior, fall back to similarity
        return find_similar_product(user_behavior, products)

    # Default case: Use similarity logic
    else:
        return find_similar_product(user_behavior, products)
    

def extract_viewed_products(user_behavior, products):
    """
    Extract products that the user has viewed or interacted with based on behavior description.

    Args:
        user_behavior (str): Description of user behavior
        products (list): List of product dictionaries

    Returns:
        list: List of products the user has interacted with
    """ 

    product_names = re.findall(r'visited the product pages for ([^.]+)', user_behavior)
    visited_products=[i for i in products if i['Product_Name'] in product_names]

    return visited_products

def extract_lead_products(user_behavior, products):
    """
    Extract products that the user has shown interest in based on behavior description.

    Args:
        user_behavior (str): Description of user behavior
        products (list): List of product dictionaries
    Returns:
        list: List of products the user has shown interest in
    """ 

    product_names = re.findall(r'shown interest in ([^.]+)', user_behavior)
    lead_products=[i for i in products if i['Product_Name'] in product_names]
    return lead_products


def select_by_priority(products):
    """
    Select product with highest priority (lowest priority number).

    Args:
        products (list): List of product dictionaries

    Returns:
        dict: Product with highest priority
    """
    # Sort products by priority (lower number = higher priority)
    sorted_products = sorted(products, key=lambda p: p.get('Priority', float('inf')))

    # Return the highest priority product
    return sorted_products[0]
