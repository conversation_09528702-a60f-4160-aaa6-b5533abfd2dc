"""
Mail Performance Analyzer

This module provides functions to analyze mail campaign performance data.
It can process campaign result files and fetch performance metrics from SparkPost API.
"""

import os
import pandas as pd
import requests
import time
import logging
from datetime import datetime, timedelta
from glob import glob
import re
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MailPerformanceAnalyzer:
    """
    A class to analyze mail campaign performance data.
    """

    def __init__(self):
        """Initialize the MailPerformanceAnalyzer."""
        self.year_month = datetime.now().strftime("%Y%m")
        # Create directories for storing performance data if they don't exist
        self.data_dir = Path("data/mail_performance")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories for different types of data
        self.openers_dir = self.data_dir / "openers"
        self.clickers_dir = self.data_dir / "clickers"
        self.unsub_dir = self.data_dir / "unsubscribers"
        self.performance_dir = self.data_dir / "combined"

        self.openers_dir.mkdir(exist_ok=True)
        self.clickers_dir.mkdir(exist_ok=True)
        self.unsub_dir.mkdir(exist_ok=True)
        self.performance_dir.mkdir(exist_ok=True)

    def sparkpost_events_api(self, starttime, days_to_add, event_type):
        """
        Fetch events data from SparkPost API.

        Args:
            starttime (str): Start time for fetching events
            days_to_add (int): Number of days to add to start time
            event_type (str): Type of event to fetch (e.g., 'open', 'click')

        Returns:
            pandas.DataFrame: DataFrame containing events data
        """
        # Replace 'your_api_key_here' with your actual SparkPost API key
        api_key = os.getenv('SPARKPOST_API_KEY')

        # Specify the SparkPost API endpoint for retrieving detailed metrics about campaigns
        url = 'https://api.sparkpost.com/api/v1/events/message'

        # Set up the headers with your API key for authentication
        headers = {
            'Authorization': api_key,
            'Content-Type': 'application/json'
        }

        start_time = pd.to_datetime(starttime)
        end_time = start_time + timedelta(days_to_add)
        start_date_str = start_time.strftime('%Y-%m-%dT%H:%M')
        end_date_str = end_time.strftime('%Y-%m-%dT%H:%M')

        links = []

        params = {
            'from': start_date_str,
            'to': end_date_str,
            'events': event_type,
            'campaigns': '2025-'
        }

        # Make the GET request to the SparkPost API
        logger.info(f"Fetching {event_type} events from {start_date_str} to {end_date_str}")
        response = requests.get(url, headers=headers, params=params)
        data = response.json()
        if "links" in data:
            if "next" in data["links"]:
                links.append(data["links"]["next"])

        if "results" not in data:
            logger.warning(f"No results found for {event_type} events")
            return pd.DataFrame()

        df = pd.DataFrame(data['results'])

        if "total_count" not in data:
            logger.warning(f"No total_count found in response for {event_type} events")
            return df

        logger.info(f"Total {event_type} events: {data['total_count']}")
        times_left = data["total_count"] // 1000
        i = 0
        while i < times_left:
            try:
                response = requests.get("https://api.sparkpost.com" + links[i], headers=headers)
                data = response.json()
                df = pd.concat([df, pd.DataFrame(data['results'])])
                if "next" in data["links"]:
                    links.append(data["links"]["next"])
                i += 1
            except Exception as e:
                logger.error(f"Error fetching additional data: {str(e)}")
                time.sleep(1)
        return df

    def map_link(self, df):
        """
        Map link URLs to categories.

        Args:
            df (pandas.Series): Series containing target_link_url

        Returns:
            str: Category of the link
        """
        if "/blog/" in df["target_link_url"]:
            return "Blog"
        elif "/unsubscribe" in df["target_link_url"]:
            return "Unsubscribe"
        elif "/one-tap-unsubscribe/?email_hash" in df["target_link_url"]:
            return "Unsubscribe"
        else:
            return "GenAI"

    def load_previous_data(self):
        """
        Load previous performance data from saved files.

        Returns:
            tuple: (openers_df, clickers_df, unsub_df, performance_df) DataFrames with previous data
        """
        openers_file = self.openers_dir / "all_openers.csv"
        clickers_file = self.clickers_dir / "all_clickers.csv"
        unsub_file = self.unsub_dir / "all_unsub.csv"
        performance_file = self.performance_dir / f"all_performance_{self.year_month}.csv"

        openers_df = pd.DataFrame()
        clickers_df = pd.DataFrame()
        unsub_df = pd.DataFrame()
        performance_df = pd.DataFrame()

        if openers_file.exists():
            try:
                openers_df = pd.read_csv(openers_file)
                logger.info(f"Loaded {len(openers_df)} previous opener records")
            except Exception as e:
                logger.error(f"Error loading openers data: {str(e)}")

        if clickers_file.exists():
            try:
                clickers_df = pd.read_csv(clickers_file)
                logger.info(f"Loaded {len(clickers_df)} previous clicker records")
            except Exception as e:
                logger.error(f"Error loading clickers data: {str(e)}")

        if unsub_file.exists():
            try:
                unsub_df = pd.read_csv(unsub_file)
                logger.info(f"Loaded {len(unsub_df)} previous unsubscriber records")
            except Exception as e:
                logger.error(f"Error loading unsubscriber data: {str(e)}")

        if performance_file.exists():
            try:
                performance_df = pd.read_csv(performance_file)
                logger.info(f"Loaded {len(performance_df)} previous performance records")
            except Exception as e:
                logger.error(f"Error loading performance data: {str(e)}")

        return openers_df, clickers_df, unsub_df, performance_df

    def save_performance_data(self, openers_df, clickers_df, unsub_df, performance_df):
        """
        Save performance data to CSV files.

        Args:
            openers_df (pandas.DataFrame): DataFrame with opener data
            clickers_df (pandas.DataFrame): DataFrame with clicker data
            unsub_df (pandas.DataFrame): DataFrame with unsubscriber data
            performance_df (pandas.DataFrame): DataFrame with combined performance data
        """
        openers_file = self.openers_dir / "all_openers.csv"
        clickers_file = self.clickers_dir / "all_clickers.csv"
        unsub_file = self.unsub_dir / "all_unsub.csv"
        performance_file = self.performance_dir / f"all_performance_{self.year_month}.csv"

        try:
            openers_df=openers_df.drop_duplicates(subset=['user_email', 'Send_Time', 'Open_Time'])
            openers_df.to_csv(openers_file, index=False)
            logger.info(f"Saved {len(openers_df)} opener records to {openers_file}")
        except Exception as e:
            logger.error(f"Error saving openers data: {str(e)}")

        try:
            clickers_df=clickers_df.drop_duplicates(subset=['user_email', 'Send_Time', 'Click_Time'])
            clickers_df.to_csv(clickers_file, index=False)
            logger.info(f"Saved {len(clickers_df)} clicker records to {clickers_file}")
        except Exception as e:
            logger.error(f"Error saving clickers data: {str(e)}")

        try:
            unsub_df=unsub_df.drop_duplicates(subset=['user_email', 'Send_Time', 'Click_Time'])
            unsub_df.to_csv(unsub_file, index=False)
            logger.info(f"Saved {len(unsub_df)} unsubscriber records to {unsub_file}")
        except Exception as e:
            logger.error(f"Error saving unsubscriber data: {str(e)}")

        try:
            performance_df.to_csv(performance_file, index=False)
            logger.info(f"Saved {len(performance_df)} performance records to {performance_file}")
        except Exception as e:
            logger.error(f"Error saving performance data: {str(e)}")

    def analyze_all_campaigns(self):
        """
        Analyze all campaign result files in the data/campaign_results folder.

        This method optimizes performance by:
        1. Merging all campaign files into a single dataframe
        2. Including previous performance data to capture campaigns not in current files
        3. Only fetching new events data for the last 4 days
        4. Combining new data with previously stored data

        This approach ensures we capture new opens/clicks for older campaigns while
        minimizing API calls and processing time.

        Returns:
            pandas.DataFrame: Combined performance data from all campaigns
        """
        # Get all campaign result files
        campaign_files = glob("data/campaign_results/*.csv")

        if not campaign_files:
            logger.warning("No campaign result files found in data/campaign_results folder")
            return pd.DataFrame()

        # Load previous data
        prev_openers, prev_clickers, prev_unsub, prev_performance = self.load_previous_data()

        # First, merge all campaign files into a single dataframe
        all_campaign_data = pd.DataFrame()

        for file_path in campaign_files:
            try:
                logger.info(f"Reading campaign file: {file_path}")
                campaign_data = pd.read_csv(file_path)

                # Ensure campaign_data has necessary columns
                if 'user_email' not in campaign_data.columns:
                    if 'email' in campaign_data.columns:
                        campaign_data = campaign_data.rename(columns={"email": "user_email"})
                    else:
                        logger.warning(f"No user_email or email column found in {file_path}")
                        continue

                # Extract campaign date from filename for Send_Time if needed
                file_name = os.path.basename(file_path)
                match = re.search(r'\d{8}', file_name)
                campaign_date = None
                if match:
                    date_str = match.group()
                    campaign_date = datetime.strptime(date_str, '%Y%m%d')
                else:
                    # If no date in filename, use current date
                    campaign_date = datetime.now()
                    logger.warning(f"No date found in filename {file_name}, using current date")

                if 'Send_Time' not in campaign_data.columns:
                    if 'Campaign_ID' in campaign_data.columns:
                        campaign_data['Send_Time'] = campaign_data["Campaign_ID"]
                    else:
                        campaign_data['Send_Time'] = campaign_date

                # Add campaign date as a column for reference
                campaign_data['campaign_date'] = campaign_date
                
                # Store the most recent campaign date for later use
                if 'latest_campaign_date' not in locals() or not latest_campaign_date or campaign_date > latest_campaign_date:
                    latest_campaign_date = campaign_date

                # Add to the combined dataset
                all_campaign_data = pd.concat([all_campaign_data, campaign_data])

            except Exception as e:
                logger.error(f"Error reading campaign file {file_path}: {str(e)}")

        # If we have previous performance data, include it for reprocessing
        # This ensures we capture any campaigns that might not be in the current batch of files
        # but still need to be updated with new opens/clicks
        if not prev_performance.empty and not all_campaign_data.empty:
            # Make sure we have the necessary columns in both dataframes
            common_columns = set(prev_performance.columns).intersection(set(all_campaign_data.columns))
            if 'user_email' in common_columns and 'Send_Time' in common_columns:
                # Convert Send_Time to datetime for proper merging
                prev_performance['Send_Time'] = pd.to_datetime(prev_performance['Send_Time'], errors='coerce')
                all_campaign_data['Send_Time'] = pd.to_datetime(all_campaign_data['Send_Time'], errors='coerce')

                # Combine with previous performance data, keeping only unique user_email/Send_Time combinations
                all_campaign_data = pd.concat([all_campaign_data, prev_performance])
                all_campaign_data = all_campaign_data.drop_duplicates(subset=['user_email', 'Send_Time'])
                logger.info(f"Combined with {len(prev_performance)} previous performance records")

        if all_campaign_data.empty:
            logger.warning("No campaign data to process")
            return prev_performance if not prev_performance.empty else pd.DataFrame()

        # Only fetch events data for the last few days
        # This is sufficient since we're already storing and combining with previous data
        current_date = datetime.now()
        default_gap_time = 4
        
        # Calculate the appropriate start date and gap time
        # Use latest_campaign_date which was set during file processing
        if 'latest_campaign_date' in locals() and latest_campaign_date is not None:
            # Ensure we have a datetime object
            if isinstance(latest_campaign_date, str):
                try:
                    latest_campaign_date = datetime.strptime(latest_campaign_date, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    latest_campaign_date = datetime.now()
                    logger.warning("Couldn't parse campaign date string, using current date")
                    
            # If campaign date is older than our default window, adjust to include it
            if (current_date - timedelta(days=default_gap_time)) > latest_campaign_date:
                # Start from one day before the campaign date
                start_date_dt = latest_campaign_date - timedelta(days=1)
                # Calculate the new gap time (days between start_date and current_date)
                gap_time = (current_date - start_date_dt).days
            else:
                # Use the default window
                gap_time = default_gap_time
                start_date_dt = current_date - timedelta(days=gap_time)
        else:
            # If no campaign date is available, use default window
            logger.warning("No campaign date found, using default time window")
            gap_time = default_gap_time
            start_date_dt = current_date - timedelta(days=gap_time)
        
        # Format the start date as a string
        start_date = start_date_dt.strftime('%Y-%m-%d')

        logger.info(f"Fetching events data for the last {gap_time} days starting from: {start_date}")

        # Fetch openers and clickers data for just the last few days
        openers_data = self.sparkpost_events_api(start_date, gap_time, 'open')
        clickers_data = self.sparkpost_events_api(start_date, gap_time, 'click')

        # Initialize dataframes for event data
        all_openers = prev_openers.copy() if not prev_openers.empty else pd.DataFrame()
        all_clickers = prev_clickers.copy() if not prev_clickers.empty else pd.DataFrame()
        all_unsub = prev_unsub.copy() if not prev_unsub.empty else pd.DataFrame()

        # Process openers data
        if not openers_data.empty:
            openers_data = openers_data[["raw_rcpt_to", "campaign_id", "subject", "type",
                                        "friendly_from", "timestamp", "user_agent",
                                        "user_agent_parsed", "geo_ip"]]

            # Rename columns for merging
            openers = openers_data.rename(columns={"raw_rcpt_to": "user_email",
                                                 "campaign_id": "Send_Time",
                                                 "timestamp": "Open_Time"})

            # Convert datetime columns
            openers["Send_Time"] = pd.to_datetime(openers["Send_Time"], format="mixed")

            # Combine with previous openers
            all_openers = pd.concat([all_openers, openers])

            # Remove duplicates
            if not all_openers.empty:
                all_openers = all_openers.drop_duplicates(subset=['user_email', 'Send_Time', 'subject', 'type'])

            logger.info(f"Processed {len(openers)} opener records")

        # Process clickers data
        if not clickers_data.empty:
            clickers_data["Link_Type"] = clickers_data.apply(self.map_link, axis=1)
            clickers_data = clickers_data[["raw_rcpt_to", "campaign_id", "subject", "type",
                                          "target_link_url", "friendly_from", "timestamp",
                                          "user_agent", "user_agent_parsed", "geo_ip", "Link_Type"]]

            # Separate clickers by type
            clickers_unsub = clickers_data[(clickers_data["Link_Type"] == "Unsubscribe")]
            clickers = clickers_data[(clickers_data["Link_Type"] == "GenAI")]

            # Rename columns for merging
            clickers = clickers.rename(columns={"raw_rcpt_to": "user_email",
                                               "campaign_id": "Send_Time",
                                               "timestamp": "Click_Time"})
            clickers_unsub = clickers_unsub.rename(columns={"raw_rcpt_to": "user_email",
                                                           "campaign_id": "Send_Time",
                                                           "timestamp": "Unsubscribe_Time"})

            # Convert datetime columns
            clickers["Send_Time"] = pd.to_datetime(clickers["Send_Time"], format="mixed")
            clickers_unsub["Send_Time"] = pd.to_datetime(clickers_unsub["Send_Time"], format="mixed")

            # Combine with previous clickers
            all_clickers = pd.concat([all_clickers, clickers])
            all_unsub = pd.concat([all_unsub, clickers_unsub])

            # Remove duplicates
            if not all_clickers.empty:
                all_clickers = all_clickers.drop_duplicates(subset=['user_email', 'Send_Time', 'subject', 'type', 'target_link_url'])

            if not all_unsub.empty:
                all_unsub = all_unsub.drop_duplicates(subset=['user_email', 'Send_Time', 'subject', 'type', 'target_link_url'])

            logger.info(f"Processed {len(clickers)} clicker records and {len(clickers_unsub)} unsubscriber records")

        # Now map performance using the combined datasets
        final_performance = all_campaign_data.copy()
        if "Open_Time" in final_performance.columns:
            final_performance.drop(columns=["Open_Time"], inplace=True)

        if "Click_Time" in final_performance.columns:
            final_performance.drop(columns=["Click_Time"], inplace=True)

        if "Unsubscribe_Time" in final_performance.columns:
            final_performance.drop(columns=["Unsubscribe_Time"], inplace=True)

        # Ensure all Send_Time columns are datetime type before merging
        try:
            logger.info(f"Converting Send_Time to datetime. Current type: {final_performance['Send_Time'].dtype}")
            final_performance['Send_Time'] = pd.to_datetime(final_performance['Send_Time'], errors='coerce')
            logger.info(f"Conversion complete. New type: {final_performance['Send_Time'].dtype}")
        except Exception as e:
            logger.error(f"Error converting Send_Time to datetime: {str(e)}")

        # Prepare openers data for merging
        if not all_openers.empty:
            try:
                merge_openers = all_openers.copy()
                logger.info(f"Converting openers Send_Time to datetime. Current type: {merge_openers['Send_Time'].dtype}")
                merge_openers['Send_Time'] = pd.to_datetime(merge_openers['Send_Time'], errors='coerce')
                logger.info(f"Conversion complete. New type: {merge_openers['Send_Time'].dtype}")
            except Exception as e:
                logger.error(f"Error converting openers Send_Time to datetime: {str(e)}")
                merge_openers = pd.DataFrame()

            # Merge with openers data
            try:
                if not merge_openers.empty and 'user_email' in merge_openers.columns and 'Send_Time' in merge_openers.columns:
                    logger.info("Merging with openers data")
                    final_performance = final_performance.merge(
                        merge_openers[["user_email", "Send_Time", "Open_Time"]],
                        on=["user_email", "Send_Time"],
                        how="left"
                    )
                    logger.info("Merge with openers data complete")
            except Exception as e:
                logger.error(f"Error merging with openers data: {str(e)}")

        # Prepare clickers data for merging
        if not all_clickers.empty:
            try:
                merge_clickers = all_clickers.copy()
                logger.info(f"Converting clickers Send_Time to datetime. Current type: {merge_clickers['Send_Time'].dtype}")
                merge_clickers['Send_Time'] = pd.to_datetime(merge_clickers['Send_Time'], errors='coerce')
                logger.info(f"Conversion complete. New type: {merge_clickers['Send_Time'].dtype}")
            except Exception as e:
                logger.error(f"Error converting clickers Send_Time to datetime: {str(e)}")
                merge_clickers = pd.DataFrame()

            # Merge with clickers data
            try:
                if not merge_clickers.empty and 'user_email' in merge_clickers.columns and 'Send_Time' in merge_clickers.columns:
                    logger.info("Merging with clickers data")
                    final_performance = final_performance.merge(
                        merge_clickers[["user_email", "Send_Time", "Click_Time"]],
                        on=["user_email", "Send_Time"],
                        how="left"
                    )
                    logger.info("Merge with clickers data complete")
            except Exception as e:
                logger.error(f"Error merging with clickers data: {str(e)}")

        # Prepare unsubscribers data for merging
        if not all_unsub.empty:
            try:
                merge_unsub = all_unsub.copy()
                logger.info(f"Converting unsubscribers Send_Time to datetime. Current type: {merge_unsub['Send_Time'].dtype}")
                merge_unsub['Send_Time'] = pd.to_datetime(merge_unsub['Send_Time'], errors='coerce')
                logger.info(f"Conversion complete. New type: {merge_unsub['Send_Time'].dtype}")
            except Exception as e:
                logger.error(f"Error converting unsubscribers Send_Time to datetime: {str(e)}")
                merge_unsub = pd.DataFrame()

            # Merge with unsubscribers data
            try:
                if not merge_unsub.empty and 'user_email' in merge_unsub.columns and 'Send_Time' in merge_unsub.columns:
                    logger.info("Merging with unsubscribers data")
                    final_performance = final_performance.merge(
                        merge_unsub[["user_email", "Send_Time", "Unsubscribe_Time"]],
                        on=["user_email", "Send_Time"],
                        how="left"
                    )
                    logger.info("Merge with unsubscribers data complete")
            except Exception as e:
                logger.error(f"Error merging with unsubscribers data: {str(e)}")

        # Remove duplicates
        final_performance = final_performance.drop_duplicates(subset=["user_email", "Send_Time"])

        logger.info(f"Final performance data contains {len(final_performance)} records")

        # Save all performance data
        self.save_performance_data(all_openers, all_clickers, all_unsub, final_performance)

        # Save combined campaign results for the current month (for backward compatibility)
        output_path = f"data/mail_performance/mail_performance_{self.year_month}.csv"
        if not final_performance.empty:
            final_performance.to_csv(output_path, index=False)
            logger.info(f"Combined mail performance data saved to: {output_path}")

        return final_performance

    def get_performance_summary(self):
        """
        Get a summary of mail performance metrics.

        Returns:
            dict: Dictionary with performance metrics and visualization data
        """
        # First try to load from the combined performance data
        performance_file = self.performance_dir / f"all_performance_{self.year_month}.csv"

        if not performance_file.exists():
            # Fall back to the monthly performance file
            performance_file = f"data/mail_performance/mail_performance_{self.year_month}.csv"

            if not os.path.exists(performance_file):
                # Try to find any performance file
                performance_files = glob("data/mail_performance/mail_performance_*.csv")
                if performance_files:
                    performance_file = max(performance_files)  # Get the most recent file
                else:
                    logger.warning("No performance data files found")
                    return {
                        "total_emails": 0,
                        "open_count": 0,
                        "open_rate": 0,
                        "click_count": 0,
                        "click_rate": 0,
                        "click_to_open_rate": 0,
                        "unsub_count": 0,
                        "unsub_rate": 0
                    }

        try:
            df = pd.read_csv(performance_file)

            # Calculate metrics
            total_emails = len(df)

            # Check if required columns exist
            open_count = 0
            if 'Open_Time' in df.columns:
                open_count = df['Open_Time'].notna().sum()
            else:
                logger.warning("'Open_Time' column not found in performance data")

            open_rate = (open_count / total_emails) * 100 if total_emails > 0 else 0

            click_count = 0
            if 'Click_Time' in df.columns:
                click_count = df['Click_Time'].notna().sum()
            else:
                logger.warning("'Click_Time' column not found in performance data")

            click_rate = (click_count / total_emails) * 100 if total_emails > 0 else 0
            click_to_open_rate = (click_count / open_count) * 100 if open_count > 0 else 0

            unsub_count = 0
            if 'Unsubscribe_Time' in df.columns:
                unsub_count = df['Unsubscribe_Time'].notna().sum()
            else:
                logger.warning("'Unsubscribe_Time' column not found in performance data")

            unsub_rate = (unsub_count / total_emails) * 100 if total_emails > 0 else 0

            # Prepare visualization data
            visualization_data = None
            try:
                # Group by date for visualization
                if 'Send_Time' in df.columns:
                    # Convert to datetime if not already
                    df['Send_Time'] = pd.to_datetime(df['Send_Time'], errors='coerce')
                    # Extract date part
                    df['date'] = df['Send_Time'].dt.date

                    # Group by date
                    viz_data = df.groupby('date').agg({
                        'user_email': 'count',
                        'Open_Time': lambda x: x.notna().sum(),
                        'Click_Time': lambda x: x.notna().sum()
                    }).reset_index()

                    # Rename columns
                    viz_data = viz_data.rename(columns={
                        'user_email': 'total_sent',
                        'Open_Time': 'total_opened',
                        'Click_Time': 'total_clicked'
                    })

                    # Calculate rates
                    viz_data['open_rate'] = (viz_data['total_opened'] / viz_data['total_sent']) * 100
                    viz_data['click_rate'] = (viz_data['total_clicked'] / viz_data['total_sent']) * 100

                    # Convert date to string for serialization
                    viz_data['date'] = viz_data['date'].astype(str)

                    # Convert to dict for returning
                    visualization_data = viz_data.to_dict('records')
            except Exception as e:
                logger.error(f"Error preparing visualization data: {str(e)}")

            return {
                "total_emails": total_emails,
                "open_count": open_count,
                "open_rate": open_rate,
                "click_count": click_count,
                "click_rate": click_rate,
                "click_to_open_rate": click_to_open_rate,
                "unsub_count": unsub_count,
                "unsub_rate": unsub_rate,
                "visualization_data": visualization_data
            }

        except Exception as e:
            logger.error(f"Error calculating performance summary: {str(e)}")
            return {
                "total_emails": 0,
                "open_count": 0,
                "open_rate": 0,
                "click_count": 0,
                "click_rate": 0,
                "click_to_open_rate": 0,
                "unsub_count": 0,
                "unsub_rate": 0,
                "visualization_data": None
            }
