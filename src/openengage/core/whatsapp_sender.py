"""
WhatsApp sending functionality for OpenEngage.
"""
import os
import json
import logging
import pandas as pd
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime
from dotenv import load_dotenv

class WhatsAppSender:
    """Base class for WhatsApp sending functionality"""

    def __init__(self, api_key: str):
        """Initialize the WhatsApp sender with API key"""
        self.api_key = api_key
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Set up logger for the WhatsApp sender"""
        logger = logging.getLogger(f"openengage.whatsapp.{self.__class__.__name__}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def set_sender_details(self, sender_name: str, phone_number: str):
        """
        Set sender details for WhatsApp sending.

        Args:
            sender_name: Name of the sender (business name)
            phone_number: Phone number of the sender (with country code, e.g., ************)
        """
        self.sender_name = sender_name

        # Clean the phone number (remove any non-digit characters)
        clean_phone = ''.join(filter(str.isdigit, phone_number))

        # Make sure it doesn't start with a + (Gupshup doesn't want the + prefix)
        if clean_phone.startswith('+'):
            clean_phone = clean_phone[1:]

        # Add country code if not present (default to India 91)
        if not (clean_phone.startswith('91') or clean_phone.startswith('1')):
            clean_phone = '91' + clean_phone

        self.phone_number = clean_phone

        self.logger.info(f"Original sender phone: {phone_number}, Cleaned sender phone: {self.phone_number}")
        self.logger.info(f"Using sender: {self.sender_name} <{self.phone_number}>")

    def send_messages(self, message_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send WhatsApp messages using the configured provider.

        Args:
            message_data: DataFrame containing message data

        Returns:
            Dict with results of the send operation
        """
        raise NotImplementedError("Subclasses must implement send_messages")

    def validate_api_key(self):
        """Validate the API key"""
        raise NotImplementedError("Subclasses must implement validate_api_key")

class GupshupSender(WhatsAppSender):
    """Gupshup implementation of WhatsApp sender"""

    def __init__(self, api_key: str):
        """Initialize the Gupshup sender with API key"""
        super().__init__(api_key)

        # Load environment variables for sender details
        load_dotenv()

        # Get sender details from environment variables
        sender_name = os.getenv("WHATSAPP_SENDER_NAME", "OpenEngage")
        phone_number = os.getenv("WHATSAPP_PHONE_NUMBER", "")

        # Store the values
        self.sender_name = sender_name
        self.phone_number = phone_number

        # Log sender details
        self.logger.info(f"Using sender: {self.sender_name} <{self.phone_number}>")

        # Validate the API key
        self.validate_api_key()

    def validate_api_key(self):
        """Validate the Gupshup API key"""
        if not self.api_key:
            raise ValueError("Gupshup API key is missing. Please provide a valid API key.")

        if len(self.api_key) < 10:
            raise ValueError(f"Invalid Gupshup API key: '{self.api_key}'. API key should be at least 10 characters long.")

        # Log a masked version of the API key for debugging
        masked_key = self.api_key[:4] + '*' * (len(self.api_key) - 8) + self.api_key[-4:]
        self.logger.info(f"Using Gupshup API key: {masked_key}")
        self.logger.info("Gupshup API key validation passed basic checks")

    def send_messages(self, message_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send WhatsApp messages using Gupshup.

        Args:
            message_data: DataFrame containing message data with columns:
                - phone_number: Recipient's phone number
                - template_id: ID of the template to use
                - params: List of parameters to substitute in the template

        Returns:
            Dict with results of the send operation
        """
        self.logger.info(f"Preparing to send {len(message_data)} WhatsApp messages via Gupshup")

        # Add result columns
        message_data["Status"] = ""
        message_data["Message_ID"] = ""
        message_data["Time_Send"] = ""

        # Process messages
        results = {
            "total_sent": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "errors": []
        }

        # Gupshup API endpoint
        url = "https://api.gupshup.io/wa/api/v1/template/msg"

        # Headers for the API request
        headers = {
            "accept": "application/json",
            "apikey": self.api_key,
            "content-type": "application/x-www-form-urlencoded"
        }

        # Base payload
        payload = {
            "source": self.phone_number,
            "src.name": self.sender_name,
            "destination": "",
            "channel": "whatsapp",
            "template": ""
        }

        # Process each message
        for idx, row in message_data.iterrows():
            self.logger.info(f"Processing message for index {idx}")
            try:
                # Set destination phone number
                phone = row["phone_number"]
                print(phone)
                # Clean the phone number (remove any non-digit characters)
                clean_phone = ''.join(filter(str.isdigit, phone))
                print(clean_phone)
                # Make sure it doesn't start with a + (Gupshup doesn't want the + prefix)
                if clean_phone.startswith('+'):
                    clean_phone = clean_phone[1:]

                # Do not add country code - assume it's already included in the phone number

                # Log the phone number transformation
                self.logger.info(f"Original phone: {phone}, Cleaned phone: {clean_phone}")

                # Set destination
                payload["destination"] = clean_phone

                # Set template with parameters
                template_id = row["template_id"]

                # Handle params - ensure it's a list of strings
                try:
                    # If params is already a list, use it
                    if isinstance(row["params"], list):
                        params = row["params"]
                    # If params is a string representation of a list, evaluate it
                    elif isinstance(row["params"], str) and row["params"].startswith('[') and row["params"].endswith(']'):
                        import ast
                        params = ast.literal_eval(row["params"])
                    # Otherwise, create a list from param_1 and param_2 columns if they exist
                    elif 'param_1' in row and 'param_2' in row:
                        params = [str(row['param_1']), str(row['param_2'])]
                    else:
                        # Fallback to empty params
                        params = []
                except Exception as e:
                    self.logger.error(f"Error processing params for row {idx}: {str(e)}")
                    params = []

                # Ensure all params are strings
                params = [str(p) for p in params]

                # Ensure we only have the required parameters ({{1}} and {{2}})
                if len(params) > 2:
                    params = params[:2]  # Limit to first two parameters
                elif len(params) < 2:
                    # Pad with empty strings if needed
                    params = params + [""] * (2 - len(params))

                # Convert params to JSON string
                template_json = {
                    "id": template_id,
                    "params": params
                }

                # Log the template data being sent
                self.logger.info(f"Sending template: {template_id} with params: {params}")

                payload["template"] = json.dumps(template_json)

                # Send the message
                time_send = datetime.now()
                try:
                    # Log the full request details for debugging
                    self.logger.info(f"Sending request to {url}")
                    self.logger.info(f"Headers: {headers}")
                    self.logger.info(f"Payload: {payload}")

                    response = requests.post(url, data=payload, headers=headers)

                    # Log the response
                    self.logger.info(f"Response status code: {response.status_code}")
                    self.logger.info(f"Response content: {response.text}")

                    # Parse response
                    resp = response.json()
                except Exception as e:
                    self.logger.error(f"Error in API request: {str(e)}")
                    resp = {"status": "error", "message": str(e)}

                # Update results
                message_data.at[idx, "Status"] = resp.get("status", "error")
                message_data.at[idx, "Message_ID"] = resp.get("messageId", "")
                message_data.at[idx, "Time_Send"] = time_send

                if resp.get("status") == "submitted":
                    results["total_accepted"] += 1
                else:
                    results["total_rejected"] += 1
                    error_msg = f"Error sending message to {phone}: {resp.get('message', 'Unknown error')}"
                    self.logger.error(error_msg)
                    results["errors"].append(error_msg)

                results["total_sent"] += 1

            except Exception as e:
                error_msg = f"Error processing message for index {idx}: {str(e)}"
                self.logger.error(error_msg)
                results["errors"].append(error_msg)
                results["total_rejected"] += 1

        # Save results to message_data
        results["message_data"] = message_data

        return results

    def send_test_message(self, phone_number: str, template_id: str, params: List[str]) -> Dict[str, Any]:
        """
        Send a test WhatsApp message.

        Args:
            phone_number: Recipient's phone number
            template_id: ID of the template to use
            params: List of parameters to substitute in the template

        Returns:
            Dict with results of the send operation
        """
        self.logger.info(f"Sending test WhatsApp message to {phone_number}")

        # Create a DataFrame with a single row
        df = pd.DataFrame({
            "phone_number": [phone_number],
            "template_id": [template_id],
            "params": [params]
        })

        # Send the message
        return self.send_messages(df)

# Factory function to create the appropriate WhatsApp sender based on provider
def create_whatsapp_sender(provider: str, api_key: str) -> WhatsAppSender:
    """
    Create a WhatsApp sender instance based on the provider.

    Args:
        provider: Name of the WhatsApp service provider
        api_key: API key for the provider

    Returns:
        WhatsAppSender instance
    """
    if provider.lower() == "gupshup":
        return GupshupSender(api_key)
    elif provider.lower() == "twilio":
        # TODO: Implement TwilioSender
        raise NotImplementedError("Twilio WhatsApp sender not implemented yet")
    elif provider.lower() == "messagebird":
        # TODO: Implement MessageBirdSender
        raise NotImplementedError("MessageBird WhatsApp sender not implemented yet")
    else:
        raise ValueError(f"Unsupported WhatsApp provider: {provider}")
