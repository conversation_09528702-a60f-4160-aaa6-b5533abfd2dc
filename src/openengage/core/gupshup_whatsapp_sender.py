"""
Gupshup WhatsApp sender functionality for OpenEngage.
This module provides functionality for sending test WhatsApp messages via Gupshup.
"""
import os
import json
import pandas as pd
import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import datetime
from dotenv import load_dotenv

# Import WhatsApp template manager and sender
try:
    # Try direct import first
    from core.whatsapp_template_manager import WhatsAppTemplateManager
    from core.whatsapp_sender import create_whatsapp_sender
except ImportError:
    # Try package import
    from openengage.core.whatsapp_template_manager import WhatsAppTemplateManager
    from openengage.core.whatsapp_sender import create_whatsapp_sender

def get_esp_api_keys():
    """Get ESP API keys from .env file."""
    load_dotenv()
    return {
        "sparkpost": os.getenv("SPARKPOST_API_KEY", ""),
        "mailmodo": os.getenv("MAILMODO_API_KEY", ""),
        "amazon_ses": os.getenv("AMAZON_SES_API_KEY", ""),
        "twilio": os.getenv("TWILIO_API_KEY", ""),
        "gupshup": os.getenv("GUPSHUP_API_KEY", ""),
        "messagebird": os.getenv("MESSAGEBIRD_API_KEY", "")
    }

def get_sender_details():
    """Get all sender details from senderDetails.json file."""
    if os.path.exists('data/senderDetails.json'):
        try:
            with open('data/senderDetails.json', 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            return []
    return []

def send_test_whatsapp_message(recipient_phone: str, selected_template_id: str, variable_values: List[str], 
                              selected_esp: str = "Gupshup") -> Dict[str, Any]:
    """
    Send a test WhatsApp message using the specified template and parameters.
    
    Args:
        recipient_phone: Recipient's phone number with country code
        selected_template_id: ID of the template to use
        variable_values: List of values for template variables
        selected_esp: WhatsApp service provider (default: Gupshup)
        
    Returns:
        Dict with results of the send operation
    """
    try:
        # Get API keys
        api_keys = get_esp_api_keys()

        # Get sender details
        sender_details = get_sender_details()
        current_details = {}

        # Find active WhatsApp sender configuration
        for details in sender_details:
            if (details.get("channel") == "WhatsApp" and
                details.get("esp") == selected_esp and
                details.get("active")):
                current_details = details
                break

        # If no active sender found, use any matching configuration
        if not current_details:
            for details in sender_details:
                if (details.get("channel") == "WhatsApp" and
                    details.get("esp") == selected_esp):
                    current_details = details
                    break

        # Configure WhatsApp sender
        if selected_esp.lower() == "gupshup":
            api_key = api_keys.get("gupshup", "")
            sender = create_whatsapp_sender("gupshup", api_key)
        elif selected_esp.lower() == "twilio":
            api_key = api_keys.get("twilio", "")
            sender = create_whatsapp_sender("twilio", api_key)
        elif selected_esp.lower() == "messagebird":
            api_key = api_keys.get("messagebird", "")
            sender = create_whatsapp_sender("messagebird", api_key)
        else:
            return {
                "success": False,
                "error": f"Unsupported WhatsApp provider: {selected_esp}"
            }

        # Set sender details for WhatsApp
        if current_details:
            # For WhatsApp, we're using the sender_email field to store the phone number
            sender.set_sender_details(
                current_details.get("sender_name", ""),
                current_details.get("sender_email", "")  # Using email field for phone number
            )

        # Send test message
        result = sender.send_test_message(
            phone_number=recipient_phone,
            template_id=selected_template_id,
            params=variable_values
        )

        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def get_template_details(product_name: Optional[str] = None, template_id: Optional[str] = None):
    """
    Get template details either by product name or template ID.
    
    Args:
        product_name: Name of the product to get template for
        template_id: ID of the template to get details for
        
    Returns:
        Template details or None if not found
    """
    try:
        template_manager = WhatsAppTemplateManager()
        
        if template_id:
            return template_manager.get_template(template_id)
        elif product_name:
            template_id = template_manager.get_product_template(product_name)
            if template_id:
                return template_manager.get_template(template_id)
        
        return None
    except Exception as e:
        print(f"Error getting template details: {str(e)}")
        return None

def get_product_templates(product_name: str):
    """
    Get all templates mapped to a product.
    
    Args:
        product_name: Name of the product
        
    Returns:
        List of template IDs
    """
    try:
        template_manager = WhatsAppTemplateManager()
        return template_manager.get_product_templates_list(product_name)
    except Exception as e:
        print(f"Error getting product templates: {str(e)}")
        return []

def get_unmapped_products(product_names: List[str]):
    """
    Get a list of products that don't have any template mappings.
    
    Args:
        product_names: List of all product names
        
    Returns:
        List of unmapped product names
    """
    try:
        template_manager = WhatsAppTemplateManager()
        return template_manager.get_unmapped_products(product_names)
    except Exception as e:
        print(f"Error getting unmapped products: {str(e)}")
        return []
