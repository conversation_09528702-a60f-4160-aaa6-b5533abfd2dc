"""
Trend analysis functionality for OpenEngage.
"""
import pandas as pd
from datetime import datetime
import requests
from typing import List, Dict, Any, Tuple
import logging
from bs4 import BeautifulSoup
import urllib.parse
import time
import io
import re
import json
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrendAnalyzer:
    """Tool for analyzing keyword trends and news"""

    def __init__(self):
        """Initialize the trend analyzer tool"""
        logger.info("Initializing TrendAnalyzer")
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Referer': 'https://www.google.com/'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        logger.info("TrendAnalyzer initialized successfully")

    def scrape_trend_data(self, keyword: str, timeframe: str = 'today 3-m') -> Tuple[pd.DataFrame, Dict, Dict]:
        """
        Scrape trend data, related queries, and related topics for a keyword from Google Trends.

        Args:
            keyword: Keyword to analyze
            timeframe: Time frame for the data (default: 'today 3-m')

        Returns:
            Tuple of (trend_data, related_queries, related_topics)
        """
        # Generate the URL for this keyword
        encoded_keyword = urllib.parse.quote(keyword)

        # Map timeframe to URL parameter
        timeframe_map = {
            'today 1-m': 'today%201-m',
            'today 3-m': 'today%203-m',
            'today 6-m': 'today%206-m',
            'today 12-m': 'today%2012-m'
        }
        timeframe_param = timeframe_map.get(timeframe, 'today%203-m')

        # Construct the URL
        url = f"https://trends.google.com/trends/explore?date={timeframe_param}&q={encoded_keyword}&hl=en"

        logger.info(f"Scraping trend data for keyword '{keyword}' from URL: {url}")

        try:
            # Add a small delay to avoid rate limiting
            time.sleep(random.uniform(1, 3))

            # Make the request
            response = self.session.get(url)

            if response.status_code != 200:
                logger.error(f"Failed to retrieve trend data for keyword '{keyword}': HTTP {response.status_code}")
                return pd.DataFrame(), {}, {}

            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract the data from the page
            # Google Trends loads data dynamically with JavaScript, so we need to find the data in the script tags
            scripts = soup.find_all('script')

            # Initialize variables to store the extracted data
            trend_data = pd.DataFrame()
            related_queries = {'rising': [], 'top': []}
            related_topics = {'rising': [], 'top': []}

            # Look for the script that contains the trend data
            for script in scripts:
                script_text = script.string
                if script_text and 'window.INITIAL_DATA' in script_text:
                    # Extract the JSON data
                    try:
                        # Find the JSON data in the script
                        json_str = re.search(r'window\.INITIAL_DATA\s*=\s*(.+?);\s*</script>', script_text, re.DOTALL)
                        if json_str:
                            json_data = json.loads(json_str.group(1))

                            # Extract trend data
                            if 'lineChart' in str(json_data):
                                # Find the trend data in the JSON
                                trend_data_str = re.search(r'"lineChart":\s*(\[.+?\])', str(json_data), re.DOTALL)
                                if trend_data_str:
                                    # Parse the trend data
                                    trend_data_list = json.loads(trend_data_str.group(1).replace("'", '"'))

                                    # Convert to DataFrame
                                    dates = []
                                    values = []

                                    for point in trend_data_list:
                                        if 'time' in point and 'value' in point:
                                            dates.append(point['time'])
                                            values.append(point['value'])

                                    if dates and values:
                                        trend_data = pd.DataFrame({
                                            'date': pd.to_datetime(dates),
                                            keyword: values
                                        })
                                        trend_data.set_index('date', inplace=True)

                            # Extract related queries
                            if 'relatedQueries' in str(json_data):
                                # Find the related queries in the JSON
                                related_queries_str = re.search(r'"relatedQueries":\s*(\{.+?\})', str(json_data), re.DOTALL)
                                if related_queries_str:
                                    # Parse the related queries
                                    related_queries_data = json.loads(related_queries_str.group(1).replace("'", '"'))

                                    # Extract rising and top queries
                                    if 'rising' in related_queries_data:
                                        related_queries['rising'] = related_queries_data['rising']
                                    if 'top' in related_queries_data:
                                        related_queries['top'] = related_queries_data['top']

                            # Extract related topics
                            if 'relatedTopics' in str(json_data):
                                # Find the related topics in the JSON
                                related_topics_str = re.search(r'"relatedTopics":\s*(\{.+?\})', str(json_data), re.DOTALL)
                                if related_topics_str:
                                    # Parse the related topics
                                    related_topics_data = json.loads(related_topics_str.group(1).replace("'", '"'))

                                    # Extract rising and top topics
                                    if 'rising' in related_topics_data:
                                        related_topics['rising'] = related_topics_data['rising']
                                    if 'top' in related_topics_data:
                                        related_topics['top'] = related_topics_data['top']

                    except Exception as e:
                        logger.error(f"Error parsing JSON data for keyword '{keyword}': {str(e)}")

            # If we couldn't extract the data from the scripts, try to find the download button and get the CSV
            if trend_data.empty:
                # Look for the download button
                download_button = soup.find('button', {'aria-label': 'Download data'})
                if download_button:
                    logger.info(f"Found download button for keyword '{keyword}', attempting to download CSV")

                    # The download URL is in a data attribute
                    download_url = download_button.get('data-url')
                    if download_url:
                        # Download the CSV
                        csv_response = self.session.get(download_url)
                        if csv_response.status_code == 200:
                            # Parse the CSV
                            try:
                                trend_data = pd.read_csv(io.StringIO(csv_response.text))
                                # Format the DataFrame
                                trend_data.rename(columns={'Week': 'date'}, inplace=True)
                                trend_data.set_index('date', inplace=True)
                            except Exception as e:
                                logger.error(f"Error parsing CSV data for keyword '{keyword}': {str(e)}")

            # If we still don't have trend data, create a mock DataFrame
            if trend_data.empty:
                logger.warning(f"Could not extract trend data for keyword '{keyword}', creating mock data")

                # Create a mock DataFrame with the expected structure
                mock_data = {
                    'date': pd.date_range(start='today', periods=12, freq='W'),
                }

                # Add a column for the keyword with zero values
                mock_data[keyword] = [0] * 12

                trend_data = pd.DataFrame(mock_data)
                trend_data.set_index('date', inplace=True)

            # Ensure trend_data is a DataFrame
            if not isinstance(trend_data, pd.DataFrame):
                logger.warning(f"trend_data is not a DataFrame, creating an empty DataFrame")
                trend_data = pd.DataFrame()

            return trend_data, related_queries, related_topics

        except Exception as e:
            logger.error(f"Error scraping trend data for keyword '{keyword}': {str(e)}")
            # Create an empty DataFrame with the expected structure
            empty_df = pd.DataFrame(columns=['date', keyword])
            if not empty_df.empty:
                empty_df.set_index('date', inplace=True)
            return empty_df, {}, {}

    def get_keyword_trends(self, keywords: List[str], timeframe: str = 'today 3-m') -> Dict[str, Any]:
        """
        Get Google Trends data for specified keywords.

        Args:
            keywords: List of keywords to analyze
            timeframe: Time frame for the data (default: 'today 3-m')
                       Options: 'today 1-m', 'today 3-m', 'today 6-m', 'today 12-m'

        Returns:
            Dictionary with trend data, related queries, and related topics for each keyword
        """
        if not keywords:
            logger.warning("No keywords provided for trend analysis")
            return {
                'trend_data': {},
                'related_queries': {},
                'related_topics': {}
            }

        # Initialize result containers
        all_trend_data = {}
        all_related_queries = {}
        all_related_topics = {}

        # Process each keyword individually
        for keyword in keywords:
            logger.info(f"Processing keyword: {keyword}")

            # Scrape data for this keyword
            trend_data, related_queries, related_topics = self.scrape_trend_data(keyword, timeframe)

            # Store the results
            if isinstance(trend_data, pd.DataFrame) and not trend_data.empty:
                all_trend_data[keyword] = trend_data

            if related_queries:
                all_related_queries[keyword] = related_queries

            if related_topics:
                all_related_topics[keyword] = related_topics

        return {
            'trend_data': all_trend_data,
            'related_queries': all_related_queries,
            'related_topics': all_related_topics
        }

    def generate_google_trends_url(self, keywords: List[str], timeframe: str = 'today 3-m') -> List[Dict[str, str]]:
        """
        Generate URLs to Google Trends for each keyword individually.

        Args:
            keywords: List of keywords to analyze
            timeframe: Time frame for the data (default: 'today 3-m')
                       Options: 'today 1-m', 'today 3-m', 'today 6-m', 'today 12-m'

        Returns:
            List of dictionaries with keyword and URL
        """
        # Ensure we have at least one keyword
        if not keywords:
            logger.warning("No keywords provided for URL generation")
            return []

        # Map timeframe to URL parameter
        timeframe_map = {
            'today 1-m': 'today%201-m',
            'today 3-m': 'today%203-m',
            'today 6-m': 'today%206-m',
            'today 12-m': 'today%2012-m'
        }

        # Use the mapped timeframe or default to 3 months
        timeframe_param = timeframe_map.get(timeframe, 'today%203-m')

        # Generate a URL for each keyword
        keyword_urls = []
        for keyword in keywords:
            # URL encode the keyword
            encoded_keyword = urllib.parse.quote(keyword)

            # Construct the URL
            url = f"https://trends.google.com/trends/explore?date={timeframe_param}&q={encoded_keyword}&hl=en"

            keyword_urls.append({
                'keyword': keyword,
                'url': url
            })

            logger.info(f"Generated Google Trends URL for '{keyword}': {url}")

        return keyword_urls

    def generate_combined_trends_url(self, keywords: List[str], timeframe: str = 'today 3-m') -> str:
        """
        Generate a URL to Google Trends for multiple keywords (up to 5).

        Args:
            keywords: List of keywords to analyze (max 5)
            timeframe: Time frame for the data (default: 'today 3-m')
                       Options: 'today 1-m', 'today 3-m', 'today 6-m', 'today 12-m'

        Returns:
            URL to Google Trends
        """
        # Ensure we have at least one keyword
        if not keywords:
            logger.warning("No keywords provided for URL generation")
            return ""

        # Google Trends only allows up to 5 keywords at a time
        keywords_to_use = keywords[:5] if len(keywords) > 5 else keywords

        # URL encode the keywords
        encoded_keywords = []
        for keyword in keywords_to_use:
            encoded_keywords.append(urllib.parse.quote(keyword))

        # Join the keywords with commas for multiple keywords
        keyword_param = ",".join(encoded_keywords)

        # Map timeframe to URL parameter
        timeframe_map = {
            'today 1-m': 'today%201-m',
            'today 3-m': 'today%203-m',
            'today 6-m': 'today%206-m',
            'today 12-m': 'today%2012-m'
        }

        # Use the mapped timeframe or default to 3 months
        timeframe_param = timeframe_map.get(timeframe, 'today%203-m')

        # Construct the URL
        url = f"https://trends.google.com/trends/explore?date={timeframe_param}&q={keyword_param}&hl=en"

        logger.info(f"Generated combined Google Trends URL: {url}")
        return url

    def get_related_queries(self, keyword: str) -> Dict[str, Any]:
        """
        Get related queries for a keyword from Google Trends.

        Args:
            keyword: Keyword to analyze

        Returns:
            Dictionary with top and rising related queries
        """
        if not keyword:
            logger.warning("No keyword provided for related queries analysis")
            return {}

        # Try multiple times with different timeframes if needed
        timeframes = ['today 6-m', 'today 3-m', 'today 12-m']

        for timeframe in timeframes:
            try:
                logger.info(f"Fetching related queries for keyword: {keyword} with timeframe: {timeframe}")

                # Add a small delay to avoid rate limiting
                import time
                time.sleep(1)

                self.pytrends.build_payload([keyword], cat=0, timeframe=timeframe, geo='', gprop='')
                related_queries = self.pytrends.related_queries()

                if related_queries and keyword in related_queries and related_queries[keyword]:
                    result = related_queries[keyword]

                    # Convert DataFrames to dictionaries for easier handling
                    processed_result = {}

                    if 'top' in result and not result['top'].empty:
                        processed_result['top'] = result['top'].to_dict('records')
                    else:
                        processed_result['top'] = []

                    if 'rising' in result and not result['rising'].empty:
                        processed_result['rising'] = result['rising'].to_dict('records')
                    else:
                        processed_result['rising'] = []

                    # If we have rising queries, return the result
                    if processed_result['rising']:
                        logger.info(f"Successfully retrieved related queries for keyword: {keyword}")
                        return processed_result

                    # If we only have top queries but no rising, keep the result but try other timeframes
                    if processed_result['top'] and not processed_result['rising']:
                        logger.info(f"Retrieved top queries but no rising queries for keyword: {keyword}")
                        last_result = processed_result

                logger.warning(f"No related queries found for keyword: {keyword} with timeframe: {timeframe}")

            except Exception as e:
                logger.error(f"Error fetching related queries for timeframe {timeframe}: {str(e)}")

        # If we tried all timeframes and still have no data, return empty result or the last partial result
        if 'last_result' in locals() and last_result:
            return last_result

        logger.error(f"Failed to retrieve related queries after trying multiple timeframes")
        return {
            'top': [],
            'rising': []
        }

    def get_related_topics(self, keyword: str) -> Dict[str, Any]:
        """
        Get related topics for a keyword from Google Trends.

        Args:
            keyword: Keyword to analyze

        Returns:
            Dictionary with top and rising related topics
        """
        if not keyword:
            logger.warning("No keyword provided for related topics analysis")
            return {}

        # Try multiple times with different timeframes if needed
        timeframes = ['today 6-m', 'today 3-m', 'today 12-m']

        for timeframe in timeframes:
            try:
                logger.info(f"Fetching related topics for keyword: {keyword} with timeframe: {timeframe}")

                # Add a small delay to avoid rate limiting
                import time
                time.sleep(1)

                self.pytrends.build_payload([keyword], cat=0, timeframe=timeframe, geo='', gprop='')
                related_topics = self.pytrends.related_topics()

                if related_topics and keyword in related_topics and related_topics[keyword]:
                    result = related_topics[keyword]

                    # Convert DataFrames to dictionaries for easier handling
                    processed_result = {}

                    if 'top' in result and not result['top'].empty:
                        processed_result['top'] = result['top'].to_dict('records')
                    else:
                        processed_result['top'] = []

                    if 'rising' in result and not result['rising'].empty:
                        processed_result['rising'] = result['rising'].to_dict('records')
                    else:
                        processed_result['rising'] = []

                    # If we have rising topics, return the result
                    if processed_result['rising']:
                        logger.info(f"Successfully retrieved related topics for keyword: {keyword}")
                        return processed_result

                    # If we only have top topics but no rising, keep the result but try other timeframes
                    if processed_result['top'] and not processed_result['rising']:
                        logger.info(f"Retrieved top topics but no rising topics for keyword: {keyword}")
                        last_result = processed_result

                logger.warning(f"No related topics found for keyword: {keyword} with timeframe: {timeframe}")

            except Exception as e:
                logger.error(f"Error fetching related topics for timeframe {timeframe}: {str(e)}")

        # If we tried all timeframes and still have no data, return empty result or the last partial result
        if 'last_result' in locals() and last_result:
            return last_result

        logger.error(f"Failed to retrieve related topics after trying multiple timeframes")
        return {
            'top': [],
            'rising': []
        }

    def get_news_for_keywords(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """
        Get recent news articles related to keywords using web search.

        Args:
            keywords: List of keywords to search for

        Returns:
            List of news articles
        """
        if not keywords:
            logger.warning("No keywords provided for news search")
            return []

        try:
            # Combine keywords for search
            # Add "news" to the query to focus on news articles
            query = ' '.join(keywords[:3]) + " news"  # Limit to first 3 keywords for better results

            logger.info(f"Searching news for query: {query}")

            # Use requests to search Google News
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # Search Google News
            search_url = f"https://www.google.com/search?q={query}&tbm=nws&tbs=qdr:w"  # qdr:w limits to past week
            response = requests.get(search_url, headers=headers)

            if response.status_code != 200:
                logger.error(f"Error searching for news: HTTP {response.status_code}")
                return []

            # Parse the HTML response
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract news articles
            news_elements = soup.select('div.SoaBEf')

            if not news_elements:
                # Try alternative selector if the first one doesn't work
                news_elements = soup.select('div.WlydOe')

            if not news_elements:
                logger.warning(f"No news articles found for query: {query}")
                return []

            # Process and return articles
            processed_articles = []
            for element in news_elements[:5]:  # Limit to 5 articles
                try:
                    # Extract title
                    title_element = element.select_one('div.mCBkyc')
                    title = title_element.text if title_element else "No title available"

                    # Extract link
                    link_element = element.select_one('a')
                    url = link_element['href'] if link_element and 'href' in link_element.attrs else "#"

                    # Clean up URL (Google prepends /url?q= to the actual URL)
                    if url.startswith('/url?q='):
                        url = url.split('/url?q=')[1].split('&')[0]

                    # Extract source and time
                    source_element = element.select_one('div.CEMjEf')
                    source = source_element.text.split(' · ')[0] if source_element else "Unknown source"

                    # Extract description
                    desc_element = element.select_one('div.GI74Re')
                    description = desc_element.text if desc_element else "No description available"

                    # Add to results
                    processed_articles.append({
                        'title': title,
                        'description': description,
                        'url': url,
                        'source': source,
                        'published_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%S')  # We don't have exact time from search results
                    })
                except Exception as e:
                    logger.error(f"Error processing news element: {str(e)}")
                    continue

            return processed_articles

        except Exception as e:
            logger.error(f"Error fetching news: {str(e)}")
            return []

    def extract_keywords_from_product(self, product_data: Dict[str, Any]) -> List[str]:
        """
        Extract relevant keywords from product data.

        Args:
            product_data: Dictionary containing product details

        Returns:
            List of keywords
        """
        if not product_data:
            logger.warning("No product data provided for keyword extraction")
            return []

        keywords = []

        # Extract from product name - this is often too specific, so we'll extract key terms
        if 'Product_Name' in product_data and product_data['Product_Name']:
            product_name = product_data['Product_Name']
            # Add the full product name
            keywords.append(product_name)

            # Extract key terms from product name (usually 1-2 words are better for trends)
            name_parts = product_name.split()
            if len(name_parts) > 2:
                # For longer names, extract the first two words which often contain the main concept
                keywords.append(' '.join(name_parts[:2]))

        # Extract from product type - this is usually a good general keyword
        if 'Type_of_Product' in product_data and product_data['Type_of_Product']:
            product_type = product_data['Type_of_Product']
            keywords.append(product_type)

            # Add some general variations that are likely to have trend data
            keywords.append(f"{product_type} trends")
            keywords.append(f"best {product_type}")

        # Extract from product features - be selective and only use short, general terms
        if 'Product_Features' in product_data and isinstance(product_data['Product_Features'], list):
            for feature in product_data['Product_Features'][:2]:  # Limit to 2 features
                if feature and isinstance(feature, str):
                    # Only use short features (1-3 words) as they're more likely to have trend data
                    feature_words = feature.split()
                    if 1 <= len(feature_words) <= 3:
                        keywords.append(feature)

        # Extract from company name if available
        if 'Company_Name' in product_data and product_data['Company_Name']:
            company_name = product_data['Company_Name']
            # Only add if it's a single word or well-known brand
            if len(company_name.split()) <= 2:
                keywords.append(company_name)

        # Remove duplicates and empty strings
        keywords = [k.strip() for k in keywords if k and isinstance(k, str)]

        # Remove duplicates while preserving order
        unique_keywords = []
        for k in keywords:
            if k.lower() not in [uk.lower() for uk in unique_keywords]:
                unique_keywords.append(k)

        # Limit to 5 keywords (Google Trends limit)
        unique_keywords = unique_keywords[:5]

        logger.info(f"Extracted {len(unique_keywords)} keywords from product data: {unique_keywords}")
        return unique_keywords

# Create a global instance for easy access
trend_analyzer = TrendAnalyzer()

def get_trend_data_for_product(product_name: str) -> Dict[str, Any]:
    """
    Get trend data for a specific product.

    Args:
        product_name: Name of the product to analyze

    Returns:
        Dictionary with trend data, related queries, related topics, and news
    """
    from utils.file_utils import load_product_details, load_custom_keywords

    # Load product details
    product_data = load_product_details(product_name)

    if not product_data:
        logger.warning(f"No product data found for: {product_name}")
        return {
            'keywords': [],
            'extracted_keywords': [],
            'trend_data': {},
            'keyword_urls': [],
            'combined_url': '',
            'related_queries': {},
            'related_topics': {},
            'news': []
        }

    # Extract keywords from product data
    extracted_keywords = trend_analyzer.extract_keywords_from_product(product_data)

    # Check if there are custom keywords for this product
    custom_keywords = load_custom_keywords().get(product_name, [])

    # Use custom keywords if available, otherwise use extracted keywords
    keywords = custom_keywords if custom_keywords else extracted_keywords

    if not keywords:
        logger.warning(f"No keywords available for product: {product_name}")
        return {
            'keywords': [],
            'extracted_keywords': extracted_keywords,
            'trend_data': {},
            'keyword_urls': [],
            'combined_url': '',
            'related_queries': {},
            'related_topics': {},
            'news': []
        }

    # Generate Google Trends URLs for each keyword
    keyword_urls = trend_analyzer.generate_google_trends_url(keywords, 'today 3-m')

    # Generate combined Google Trends URL
    combined_url = trend_analyzer.generate_combined_trends_url(keywords, 'today 3-m')

    # Get trend data, related queries, and related topics for keywords
    trends_data = trend_analyzer.get_keyword_trends(keywords)

    # Get news for all keywords
    news = trend_analyzer.get_news_for_keywords(keywords)

    return {
        'keywords': keywords,
        'extracted_keywords': extracted_keywords,
        'trend_data': trends_data.get('trend_data', {}),
        'keyword_urls': keyword_urls,
        'combined_url': combined_url,
        'related_queries': trends_data.get('related_queries', {}),
        'related_topics': trends_data.get('related_topics', {}),
        'news': news
    }
