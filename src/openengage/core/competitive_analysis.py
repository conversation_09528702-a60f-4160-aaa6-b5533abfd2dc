"""
Core functionality for competitive analysis in OpenEngage.
"""
import os
import json
import re
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from openai import OpenAI

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data/competitive_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('competitive_analysis')

def load_product_details(product_name=None):
    """
    Load product details from product_details.json.

    Args:
        product_name: Optional name of the product to load

    Returns:
        Dict or List: Product details or list of all products
    """
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)

        # Convert to list if it's a single object
        if not isinstance(products, list):
            products = [products]

        if product_name:
            # Find the specific product
            for product in products:
                if product.get('Product_Name') == product_name:
                    return product
            return None
        else:
            return products
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Error loading product details: {str(e)}")
        return [] if product_name is None else None

def save_competitive_analysis(analysis_data):
    """
    Save competitive analysis data to a JSON file.

    Args:
        analysis_data: Analysis data to save

    Returns:
        str: Filename where data was saved
    """
    if not analysis_data:
        return None

    # Create a timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"competitive_analysis_{timestamp}.json"

    # Ensure directory exists
    os.makedirs("data/competitive_analysis", exist_ok=True)

    # Save the data
    with open(f"data/competitive_analysis/{filename}", 'w') as f:
        json.dump(analysis_data, f, indent=4)

    logger.info(f"Saved competitive analysis to {filename}")
    return filename

def load_competitive_analysis(filename=None):
    """
    Load competitive analysis data from a JSON file.

    Args:
        filename: Optional filename to load from

    Returns:
        Dict: Analysis data
    """
    try:
        if filename:
            # Load specific file
            file_path = f"data/competitive_analysis/{filename}"
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return {}

            with open(file_path, 'r') as f:
                return json.load(f)
        else:
            # Get the most recent file
            if not os.path.exists("data/competitive_analysis"):
                logger.warning("No competitive analysis directory found")
                return {}

            analysis_files = [f for f in os.listdir("data/competitive_analysis")
                             if f.endswith('.json')]

            if not analysis_files:
                logger.warning("No analysis files found")
                return {}

            # Sort by modification time (most recent first)
            analysis_files.sort(key=lambda x: os.path.getmtime(f"data/competitive_analysis/{x}"),
                               reverse=True)

            # Load the most recent file
            with open(f"data/competitive_analysis/{analysis_files[0]}", 'r') as f:
                return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Error loading competitive analysis: {str(e)}")
        return {}

def create_comparison_dataframe(analysis_data, our_product):
    """
    Create a comparison dataframe from analysis data.

    Args:
        analysis_data: Dictionary with competitor analyses
        our_product: Dictionary with our product details

    Returns:
        DataFrame: Comparison dataframe
    """
    if not analysis_data or not our_product:
        return pd.DataFrame()

    # Start with our product as the first row
    comparison_data = [{
        "Product": our_product.get("Product_Name", ""),
        "Company": our_product.get("Company_Name", ""),
        "Product URL": our_product.get("Product_URL", ""),
        "Price": our_product.get("Price", "Not specified"),
        "Summary": our_product.get("Product_Summary", ""),
        "Feature List": "\n• " + "\n• ".join(our_product.get("Product_Features", [])) if our_product.get("Product_Features") else "No features listed",
        "Similarity": "Reference product",
        "What We Do Better": "Reference product",
        "What They Do Better": "Reference product",
        "Unique Selling Points": "\n• " + "\n• ".join(our_product.get("Unique_Selling_Points", [])) if our_product.get("Unique_Selling_Points") else "None specified"
    }]

    # Add competitor products
    for competitor_url, competitor_data in analysis_data.items():
        company_name = competitor_data.get("company_name", "Unknown")

        for product in competitor_data.get("products", []):
            product_name = product.get("product_name", "Unknown Product")

            # Skip products with no name or marked as unknown
            if product_name == "Unknown Product" or product_name == "Not available":
                continue

            # Get product URL if available, otherwise use company URL
            product_url = product.get("product_url", competitor_url)

            # Create comparison entry
            comp_product = {
                "Product": product_name,
                "Company": company_name,
                "Product URL": product_url,
                "Price": product.get("pricing", "Not specified"),
                "Summary": product.get("product_description", "No description")
            }

            # Add Feature List (all features in a single column)
            features_list = product.get("features", [])
            if features_list:
                comp_product["Feature List"] = "\n• " + "\n• ".join(features_list)
            else:
                comp_product["Feature List"] = "No features listed"

            # Add Unique Selling Points
            unique_selling_points = product.get("unique_selling_points", [])
            if unique_selling_points:
                comp_product["Unique Selling Points"] = "\n• " + "\n• ".join(unique_selling_points)
            else:
                comp_product["Unique Selling Points"] = "None specified"

            # Add comparison results if available
            comparison = product.get("comparison", {})
            comp_product["Similarity"] = comparison.get("similarity", "Analysis not available")
            comp_product["What We Do Better"] = comparison.get("our_advantages", "Analysis not available")
            comp_product["What They Do Better"] = comparison.get("their_advantages", "Analysis not available")

            comparison_data.append(comp_product)

    # Convert to DataFrame
    if comparison_data:
        df = pd.DataFrame(comparison_data)

        # Reorder columns to ensure key columns come first
        column_order = [
            "Product",
            "Company",
            "Product URL",
            "Price",
            "Summary",
            "Feature List",
            "Similarity",
            "What We Do Better",
            "What They Do Better",
            "Unique Selling Points"
        ]

        # Reorder the DataFrame columns (only include columns that exist)
        df = df[[col for col in column_order if col in df.columns]]

        return df
    else:
        # Return empty DataFrame with expected columns
        return pd.DataFrame(columns=[
            "Product", "Company", "Product URL", "Price", "Summary",
            "Feature List", "Similarity", "What We Do Better",
            "What They Do Better", "Unique Selling Points"
        ])

def summarize_comparison(df, our_product_name):
    """
    Summarize the comparison table using OpenAI's GPT-4o-mini model.

    Args:
        df: DataFrame containing the comparison data
        our_product_name: Name of our product

    Returns:
        str: Summary of the comparison
    """
    if df.empty:
        return "No comparison data available."

    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Convert DataFrame to a list of dictionaries for easier processing
        comparison_data = df.to_dict(orient='records')

        # Create a prompt for the model
        prompt = f"""
        I have a comparison table of {our_product_name} versus its competitors. Here's the data:

        {comparison_data}

        Please provide a concise summary (maximum 300 words) of this comparison that highlights:
        1. The key strengths of {our_product_name} compared to competitors
        2. Areas where competitors might have an advantage
        3. The overall competitive position of {our_product_name} in the market
        4. Any notable patterns or insights from the comparison

        Format the summary in clear paragraphs with bullet points for key takeaways.
        """

        # Call the OpenAI API
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a competitive analysis expert who provides clear, concise, and insightful summaries of product comparisons."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.5,
            max_tokens=500
        )

        # Extract and return the summary
        summary = response.choices[0].message.content
        logger.info(f"Generated comparison summary for {our_product_name}")
        return summary

    except Exception as e:
        logger.error(f"Error generating comparison summary: {str(e)}")
        return f"Unable to generate comparison summary: {str(e)}"
