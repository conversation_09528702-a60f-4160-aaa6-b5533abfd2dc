"""
Email sending functionality for OpenEngage.
"""
import os
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

class EmailSender:
    """Base class for email sending functionality"""

    def __init__(self, api_key: str):
        """Initialize the email sender with API key"""
        self.api_key = api_key
    
    def set_sender_details(self, sender_name: str, sender_email: str, reply_to_email: str):
        """
        Set sender details for email sending.
        
        Args:
            sender_name: Name of the sender
            sender_email: Email address of the sender
            reply_to_email: Reply-to email address
        """
        self.sender_name = sender_name
        self.sender_email = sender_email
        self.reply_to_email = reply_to_email
        self.from_email = f"{self.sender_name} <{self.sender_email}>"

    def send_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send emails using the configured provider.

        Args:
            email_data: DataFrame containing email data

        Returns:
            Dict with results of the send operation
        """
        raise NotImplementedError("Subclasses must implement send_emails")

    def schedule_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Schedule emails for future delivery.

        Args:
            email_data: DataFrame containing email data with Send_Time column

        Returns:
            Dict with results of the schedule operation
        """
        raise NotImplementedError("Subclasses must implement schedule_emails")

class SparkPostSender(EmailSender):
    """SparkPost implementation of email sender"""

    def __init__(self, api_key: str):
        """Initialize the SparkPost sender with API key"""
        super().__init__(api_key)
        self.logger = self._setup_logger()

        # Load environment variables for sender details
        load_dotenv()

        # Get sender details from environment variables
        # Force reload to ensure we have the latest values
        sender_name = os.getenv("EMAIL_SENDER_NAME", "OpenEngage Team")
        sender_email = os.getenv("EMAIL_SENDER_EMAIL", "<EMAIL>")
        reply_to_email = os.getenv("EMAIL_REPLY_TO", "<EMAIL>")

        # Strip quotes if present
        if sender_name and sender_name.startswith("'") and sender_name.endswith("'"):
            sender_name = sender_name[1:-1]
        if sender_email and sender_email.startswith("'") and sender_email.endswith("'"):
            sender_email = sender_email[1:-1]
        if reply_to_email and reply_to_email.startswith("'") and reply_to_email.endswith("'"):
            reply_to_email = reply_to_email[1:-1]

        # Store the cleaned values
        self.sender_name = sender_name
        self.sender_email = sender_email
        self.reply_to_email = reply_to_email

        # Log sender details
        self.logger.info(f"Using sender: {self.sender_name} <{self.sender_email}> (Reply-to: {self.reply_to_email})")

        # Create from_email string
        self.from_email = f"{self.sender_name} <{self.sender_email}>"

        # Validate the API key
        self.validate_api_key()
        
    def set_sender_details(self, sender_name: str, sender_email: str, reply_to_email: str):
        """
        Set sender details for email sending.
        
        Args:
            sender_name: Name of the sender
            sender_email: Email address of the sender
            reply_to_email: Reply-to email address
        """
        super().set_sender_details(sender_name, sender_email, reply_to_email)
        
        # Log updated sender details
        self.logger.info(f"Updated sender: {self.sender_name} <{self.sender_email}> (Reply-to: {self.reply_to_email})")

    def _setup_logger(self) -> logging.Logger:
        """Set up logging for email operations"""
        logger = logging.getLogger("sparkpost_sender")
        logger.setLevel(logging.INFO)

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')
        console_handler.setFormatter(formatter)

        # Add handler to logger
        logger.addHandler(console_handler)

        return logger

    def validate_api_key(self):
        """Validate the SparkPost API key"""
        if not self.api_key:
            raise ValueError("SparkPost API key is required")

        if len(self.api_key) < 20:
            raise ValueError("SparkPost API key appears to be invalid (too short)")

        try:
            # Import SparkPost here to avoid dependency issues
            from sparkpost import SparkPost
            from sparkpost.exceptions import SparkPostAPIException

            # Initialize the client
            sp = SparkPost(self.api_key)

            # We could make a simple API call here to validate the key
            # but we'll defer that to the actual send/schedule operations
            # to avoid unnecessary API calls

            self.logger.info("SparkPost API key validation passed basic checks")
        except ImportError:
            raise ImportError("SparkPost module not installed. Please install it with 'pip install sparkpost'")
        except Exception as e:
            raise ValueError(f"Error initializing SparkPost client: {str(e)}")

    def send_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send emails using SparkPost.

        Args:
            email_data: DataFrame containing email data

        Returns:
            Dict with results of the send operation
        """
        self.logger.info(f"Preparing to send {len(email_data)} emails via SparkPost")

        # Add result columns
        email_data["Campaign_ID"] = ""
        email_data["Accepted"] = ""
        email_data["Rejected"] = ""

        # Add preheader if available
        if "Preheader" in email_data.columns:
            email_data['HTML_Content'] = (
                "<div style='display:none; font-size:1px; line-height:1px; max-height:0; max-width:0; opacity:0; overflow:hidden;'>" +
                email_data['Preheader'].fillna("") +
                "</div>" +
                email_data['HTML_Content']
            )

        # Process in batches
        results = {
            "total_sent": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "errors": []
        }

        try:
            # Import SparkPost here to avoid dependency issues
            from sparkpost import SparkPost
            from sparkpost.exceptions import SparkPostAPIException

            # Initialize SparkPost client
            # Check if API key is valid (not empty and has proper format)
            if not self.api_key or len(self.api_key) < 20:  # SparkPost API keys are typically long
                raise ValueError("Invalid SparkPost API key. Please provide a valid API key.")

            # Initialize with API key
            sp = SparkPost(self.api_key)

            # Process in batches of 20
            batch_size = 20
            for i in range(0, len(email_data), batch_size):
                try:
                    # Get batch
                    end_idx = min(i + batch_size, len(email_data))
                    batch = email_data.iloc[i:end_idx].reset_index(drop=True)

                    # Create campaign ID
                    campaign_id = str(datetime.now())

                    # Prepare recipient data
                    recipients = []
                    for email in batch["user_email"]:
                        recipients.append({"address": {"email": email}})

                    # Prepare email content
                    offers = list(batch["user_email"])
                    email_dict = batch.set_index("user_email").to_dict()

                    # Send emails
                    response = sp.transmissions.send(
                        recipients=recipients,
                        html="{{each offers}}{{if loop_var==address.email}}{{render_dynamic_content(dynamic_html[loop_var])}}{{end}}{{end}}",
                        from_email=self.from_email,
                        subject="{{each offers}}{{if loop_var==address.email}}{{dynamic_subject[loop_var]}}{{end}}{{end}}",
                        track_opens=True,
                        track_clicks=True,
                        campaign=campaign_id,
                        metadata={
                            'source': 'openengage',
                            'batch': f'{i}-{end_idx}'
                        },
                        substitution_data={
                            "offers": offers,
                            "dynamic_html": email_dict["HTML_Content"],
                            "dynamic_subject": email_dict["Subject"]
                        },
                        reply_to=self.reply_to_email
                    )

                    # Update batch with results
                    batch["Campaign_ID"] = campaign_id
                    batch["Accepted"] = response["total_accepted_recipients"]
                    batch["Rejected"] = response["total_rejected_recipients"]

                    # Update email_data with batch results
                    email_data.iloc[i:end_idx] = batch

                    # Update results
                    results["total_sent"] += batch_size
                    results["total_accepted"] += response["total_accepted_recipients"]
                    results["total_rejected"] += response["total_rejected_recipients"]

                    self.logger.info(f"Batch {i//batch_size + 1}: Sent {batch_size} emails, {response['total_accepted_recipients']} accepted, {response['total_rejected_recipients']} rejected")

                except SparkPostAPIException as e:
                    error_msg = f"SparkPost API error in batch {i//batch_size + 1}: {str(e)}"
                    self.logger.error(error_msg)
                    results["errors"].append(error_msg)
                except Exception as e:
                    error_msg = f"Error in batch {i//batch_size + 1}: {str(e)}"
                    self.logger.error(error_msg)
                    results["errors"].append(error_msg)

        except ImportError:
            error_msg = "SparkPost module not installed. Please install it with 'pip install sparkpost'"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)
        except Exception as e:
            error_msg = f"Error initializing SparkPost: {str(e)}"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)

        # Save results to email_data
        results["email_data"] = email_data

        return results

    def schedule_emails(self, email_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Schedule emails for future delivery using SparkPost.

        Args:
            email_data: DataFrame containing email data with Send_Time column

        Returns:
            Dict with results of the schedule operation
        """
        self.logger.info(f"Preparing to schedule {len(email_data)} emails via SparkPost")

        # Validate Send_Time column
        if "Send_Time" not in email_data.columns:
            error_msg = "Send_Time column is required for scheduling emails"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }

        # Convert Send_Time to datetime if it's not already
        if not pd.api.types.is_datetime64_dtype(email_data["Send_Time"]):
            try:
                email_data["Send_Time"] = pd.to_datetime(email_data["Send_Time"])
            except Exception as e:
                error_msg = f"Error converting Send_Time to datetime: {str(e)}"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

        # Add ISO format Send_Time
        email_data["ISO_Date_Time"] = email_data["Send_Time"].dt.strftime('%Y-%m-%dT%H:%M:%S+00:00')

        # Add result columns
        email_data["Campaign_ID"] = ""
        email_data["Accepted"] = ""
        email_data["Rejected"] = ""

        # Add preheader if available
        if "Preheader" in email_data.columns:
            email_data['HTML_Content'] = (
                "<div style='display:none; font-size:1px; line-height:1px; max-height:0; max-width:0; opacity:0; overflow:hidden;'>" +
                email_data['Preheader'].fillna("") +
                "</div>" +
                email_data['HTML_Content']
            )

        # Process in batches
        results = {
            "total_scheduled": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "errors": []
        }

        try:
            # Import SparkPost here to avoid dependency issues
            from sparkpost import SparkPost
            from sparkpost.exceptions import SparkPostAPIException

            # Initialize SparkPost client
            # Check if API key is valid (not empty and has proper format)
            if not self.api_key or len(self.api_key) < 20:  # SparkPost API keys are typically long
                raise ValueError("Invalid SparkPost API key. Please provide a valid API key.")

            # Initialize with API key
            sp = SparkPost(self.api_key)

            # Sort by send time
            email_data = email_data.sort_values(by=["Send_Time"])

            # Process in batches of 20
            batch_size = 20
            for i in range(0, len(email_data), batch_size):
                try:
                    # Get batch
                    end_idx = min(i + batch_size, len(email_data))
                    batch = email_data.iloc[i:end_idx].reset_index(drop=True)

                    # Check if send time is in the past
                    now = datetime.now()
                    if batch["Send_Time"].iloc[0] < now:
                        # Adjust send time to tomorrow at the same time
                        batch["Send_Time"] = batch["Send_Time"] + timedelta(days=1)
                        batch["ISO_Date_Time"] = batch["Send_Time"].dt.strftime('%Y-%m-%dT%H:%M:%S+00:00')

                    # Create campaign ID
                    campaign_id = str(batch["Send_Time"].iloc[0])

                    # Prepare recipient data
                    recipients = []
                    for email in batch["user_email"]:
                        recipients.append({"address": {"email": email}})

                    # Prepare email content
                    offers = list(batch["user_email"])
                    email_dict = batch.set_index("user_email").to_dict()

                    # Schedule emails
                    response = sp.transmissions.send(
                        recipients=recipients,
                        html="{{each offers}}{{if loop_var==address.email}}{{render_dynamic_content(dynamic_html[loop_var])}}{{end}}{{end}}",
                        from_email=self.from_email,
                        subject="{{each offers}}{{if loop_var==address.email}}{{dynamic_subject[loop_var]}}{{end}}{{end}}",
                        track_opens=True,
                        track_clicks=True,
                        campaign=campaign_id,
                        start_time=batch["ISO_Date_Time"].iloc[0],
                        metadata={
                            'source': 'openengage',
                            'batch': f'{i}-{end_idx}'
                        },
                        substitution_data={
                            "offers": offers,
                            "dynamic_html": email_dict["HTML_Content"],
                            "dynamic_subject": email_dict["Subject"]
                        },
                        reply_to=self.reply_to_email
                    )

                    # Update batch with results
                    batch["Campaign_ID"] = campaign_id
                    batch["Accepted"] = response["total_accepted_recipients"]
                    batch["Rejected"] = response["total_rejected_recipients"]

                    # Update email_data with batch results
                    email_data.iloc[i:end_idx] = batch

                    # Update results
                    results["total_scheduled"] += batch_size
                    results["total_accepted"] += response["total_accepted_recipients"]
                    results["total_rejected"] += response["total_rejected_recipients"]

                    self.logger.info(f"Batch {i//batch_size + 1}: Scheduled {batch_size} emails for {batch['ISO_Date_Time'].iloc[0]}")

                except SparkPostAPIException as e:
                    error_msg = f"SparkPost API error in batch {i//batch_size + 1}: {str(e)}"
                    self.logger.error(error_msg)
                    results["errors"].append(error_msg)
                except Exception as e:
                    error_msg = f"Error in batch {i//batch_size + 1}: {str(e)}"
                    self.logger.error(error_msg)
                    results["errors"].append(error_msg)

        except ImportError:
            error_msg = "SparkPost module not installed. Please install it with 'pip install sparkpost'"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)
        except Exception as e:
            error_msg = f"Error initializing SparkPost: {str(e)}"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)

        # Save results to email_data
        results["email_data"] = email_data

        return results

# Factory function to create the appropriate sender based on provider
def create_email_sender(provider: str, api_key: str) -> EmailSender:
    """
    Create an email sender instance based on the provider.

    Args:
        provider: Name of the email service provider
        api_key: API key for the provider

    Returns:
        EmailSender instance
    """
    if provider.lower() == "sparkpost":
        return SparkPostSender(api_key)
    else:
        raise ValueError(f"Unsupported email provider: {provider}")
