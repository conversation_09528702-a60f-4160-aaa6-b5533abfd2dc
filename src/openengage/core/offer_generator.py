"""
Offer generation functionality for OpenEngage.
"""
import os
import json
import re
import streamlit as st
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Import email formatter
from core.email_formatter import process_paragraphs

# Try to load environment variables and OpenAI
try:
    from dotenv import load_dotenv
    load_dotenv()
    from openai import OpenAI
except ImportError:
    st.error("OpenAI or dotenv modules not found. Please install them with 'pip install openai python-dotenv'")

def generate_offer_section(offer_data: Dict[str, Any], product_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Generate a common offer section for all emails using GPT-4o-mini.

    Args:
        offer_data: Dictionary containing offer details (offer_details)
        product_data: Dictionary containing product information

    Returns:
        Dict[str, str]: Dictionary containing offer_subject and offer_text
    """
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Create prompt for offer generation
        prompt = f"""
        Generate a compelling offer section for an email based on the following details:

        Product Information:
        - Product Name: {product_data.get('Product_Name', 'N/A')}
        - Product Type: {product_data.get('Type_of_Product', 'N/A')}

        Offer Details:
        - Offer Details: {offer_data.get('offer_details', 'Special offer')}

        Instructions:
        1. First, create a short, attention-grabbing subject line for this offer (one line only)
        2. Then, create a compelling offer section (1-2 short paragraphs only)
        3. Highlight the value proposition and urgency
        4. Include the specific offer details
        5. Make it sound natural and engaging
        6. Do NOT include any greeting (like "Hi" or "Hello") or closing (like "Best regards")
        7. Keep it very concise and focused on the offer (maximum 3-4 sentences total)
        8. DO NOT use the words "unlock", "delve", "dive", or "transform" - they are overused
        9. Format your response exactly like this:

        Subject: [Your attention-grabbing subject line here]

        [Your offer section text here - 1-2 short paragraphs with no greeting or closing]
        """

        # Generate offer section using GPT-4o-mini
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a marketing specialist who creates compelling email offer sections."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=500
        )

        # Extract the generated offer content
        content = response.choices[0].message.content.strip()

        # Parse the subject and offer text
        offer_subject = ""
        offer_text = ""

        if "Subject:" in content:
            parts = content.split("Subject:", 1)
            if len(parts) > 1:
                subject_and_text = parts[1].strip()
                lines = subject_and_text.split('\n', 1)
                offer_subject = lines[0].strip()
                if len(lines) > 1:
                    offer_text = lines[1].strip()
        else:
            # Fallback if format is not followed
            offer_text = content
            offer_subject = "Special Offer"

        return {
            "offer_subject": offer_subject,
            "offer_text": offer_text
        }

    except Exception as e:
        st.error(f"Error generating offer section: {str(e)}")
        return f"Special Offer: {offer_data.get('offer_details', 'Special offer')} (Valid for: {offer_data.get('offer_time', 'Limited time')})"

def convert_offer_to_html(offer_text: str, offer_subject: str, product_url: Optional[str] = None, product_name: Optional[str] = None) -> str:
    """
    Convert offer text to HTML format using the same method as the email body.

    Args:
        offer_text: The offer text to convert
        offer_subject: The subject line for the offer
        product_url: URL of the product (optional)
        product_name: Name of the product (optional)

    Returns:
        str: HTML formatted offer section
    """
    # Import necessary functions from email_formatter
    try:
        from core.email_formatter import process_paragraphs, process_urls_in_text, process_inline_formatting, add_utm_parameters

        # Create communication settings for the offer
        communication_settings = {
            "sender_name": "OpenEngage Team",
            "style": "friendly",
            "length": "<100 words",  # Keep it short
            "utm_source": "email_offer",
            "utm_medium": "email",
            "utm_campaign": "special_offer",
            "utm_content": "offer_section"
        }

        # Ensure there's no 'Hi' in the offer text
        if offer_text.strip().lower().startswith('hi') or offer_text.strip().lower().startswith('hello'):
            # Remove the greeting line
            lines = offer_text.split('\n', 1)
            if len(lines) > 1:
                offer_text = lines[1].strip()
            else:
                # If there's only one line with Hi, remove the Hi
                offer_text = re.sub(r'^\s*(Hi|Hello)\b[,\s]*', '', offer_text).strip()

        # Remove words to avoid
        words_to_avoid = ['unlock', 'delve', 'dive', 'transform']
        for word in words_to_avoid:
            # Case insensitive replacement
            pattern = re.compile(re.escape(word), re.IGNORECASE)
            if pattern.search(offer_text):
                # Replace with alternatives
                alternatives = {
                    'unlock': 'access',
                    'delve': 'explore',
                    'dive': 'explore',
                    'transform': 'improve'
                }
                offer_text = pattern.sub(alternatives.get(word.lower(), ''), offer_text)

        # Split content into paragraphs
        paragraphs = offer_text.split('\n\n')

        html_paragraphs = []
        for para in paragraphs:
            if not para.strip():
                continue

            # Process URLs in the paragraph
            processed_para = process_urls_in_text(para, communication_settings, product_name)

            # Process other formatting (bold, italic)
            formatted_para = process_inline_formatting(processed_para)

            # Add product link if it exists
            if product_name and product_url and product_name in formatted_para:
                # Add UTM parameters to the product URL
                product_url_with_utm = add_utm_parameters(product_url, communication_settings)

                # Create linked version of the product name with brand color
                linked_product_name = f'<a href="{product_url_with_utm}" style="color: #8D06FE; text-decoration: none;">{product_name}</a>'

                # Replace product name with link (only first occurrence)
                formatted_para = formatted_para.replace(product_name, linked_product_name, 1)

            html_paragraphs.append(f'<p style="margin: 10px 0; line-height: 1.5;">{formatted_para}</p>')

        html_content = '\n'.join(html_paragraphs)

    except (ImportError, NameError) as e:
        # Simple fallback if email_formatter functions are not available
        # Convert newlines to <br> tags and wrap in paragraphs
        paragraphs = offer_text.split('\n\n')
        html_paragraphs = []

        for para in paragraphs:
            if not para.strip():
                continue
            # Replace newlines with <br>
            para_html = para.replace('\n', '<br>')
            html_paragraphs.append(f'<p style="margin: 10px 0; line-height: 1.5;">{para_html}</p>')

        html_content = '\n'.join(html_paragraphs)

    # Wrap the offer section in a styled div with brand colors
    # Brand colors: #8D06FE (primary), #02B9C9 (secondary), #02C688 (tertiary)
    styled_offer = f"""
    <div class="offer-section" style="
        background-color: #FFFFFF;
        border: 2px dashed #8D06FE;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        position: relative;
    ">
        <div style="
            position: absolute;
            top: -10px;
            left: 20px;
            background-color: #8D06FE;
            color: white;
            padding: 2px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        ">{offer_subject}</div>
        {html_content}
    </div>
    """

    return styled_offer

def process_offers_for_mass_campaign(data_df, offers_df):
    """
    Process offers for a mass campaign.

    Args:
        data_df: DataFrame containing user data
        offers_df: DataFrame containing offer data with offer_start_time, offer_end_time, and offer_details

    Returns:
        DataFrame with added offer HTML content
    """
    # Ensure pandas is imported
    try:
        import pandas as pd
    except ImportError:
        st.error("Pandas module not found. Please install it with 'pip install pandas'")
        return data_df
    # Load all products
    products = []
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
            if not isinstance(products, list):
                products = [products]
    except Exception:
        st.error("Error loading product details")
        return data_df

    # Add offer columns to the dataframe
    data_df['Offer_Subject'] = ''
    data_df['Offer_Text'] = ''
    data_df['Offer_HTML'] = ''

    # Import datetime for time comparisons
    from datetime import datetime

    # Get current time as datetime object
    current_time = datetime.now()

    # Process each user
    for idx, row in data_df.iterrows():
        # Apply offer to all users - no need to match by email
        # Just find the first valid offer where current time is between start and end time
        matching_offer = None

        for _, offer_row in offers_df.iterrows():
            # Check if offer is currently valid
            try:
                # Get start and end times as datetime objects
                offer_start_str = offer_row.get('offer_start_time', '2000-01-01T00:00:00')
                offer_end_str = offer_row.get('offer_end_time', '2099-12-31T23:59:59')

                # Convert strings to datetime objects
                offer_start = datetime.fromisoformat(offer_start_str)
                offer_end = datetime.fromisoformat(offer_end_str)

                # Check if current time is between start and end (inclusive)
                if offer_start <= current_time <= offer_end:
                    matching_offer = offer_row
                    break
            except Exception as e:
                # If there's an error parsing the dates, skip this offer
                st.warning(f"Error parsing offer dates: {str(e)}")
                continue

        # Only generate the offer once for all users
        if matching_offer is not None and idx == 0:  # Only for the first row
            # Find a product to use for the offer generation
            from core.journey_builder import find_similar_product
            matched_product = None

            # Try to find a product that matches the first user's behavior
            if 'user_behaviour' in row:
                matched_product, _ = find_similar_product(row['user_behaviour'], products)

            # If no match, use the first product in the list
            if not matched_product and products:
                matched_product = products[0]

            if matched_product:
                # Create offer data dict
                offer_data = {
                    'offer_details': matching_offer.get('offer_details', 'Special offer')
                }

                # Generate offer section (common for all users)
                offer_result = generate_offer_section(offer_data, matched_product)
                offer_subject = offer_result.get('offer_subject', 'Special Offer')
                offer_text = offer_result.get('offer_text', '')

                # Convert to HTML
                offer_html = convert_offer_to_html(
                    offer_text,
                    offer_subject,
                    product_url=matched_product.get('Product_URL'),
                    product_name=matched_product.get('Product_Name')
                )

                # Apply to all rows
                for i in range(len(data_df)):
                    data_df.at[i, 'Offer_Subject'] = offer_subject
                    data_df.at[i, 'Offer_Text'] = offer_text
                    data_df.at[i, 'Offer_HTML'] = offer_html

    return data_df
