"""
Script to reset WhatsApp templates.
"""
import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from openengage.core.whatsapp_template_manager import WhatsAppTemplateManager
except ImportError:
    try:
        from core.whatsapp_template_manager import WhatsAppTemplateManager
    except ImportError:
        print("Could not import WhatsAppTemplateManager")
        sys.exit(1)

def reset_templates():
    """Reset the WhatsApp templates file."""
    try:
        # Create a new template manager
        template_manager = WhatsAppTemplateManager()
        
        # Reset the templates
        template_manager.templates = {
            "templates": [],
            "product_templates": {},
            "product_template_mappings": {}
        }
        
        # Save the empty templates
        if template_manager._save_templates():
            print("Templates reset successfully!")
        else:
            print("Failed to reset templates")
    except Exception as e:
        print(f"Error resetting templates: {str(e)}")

if __name__ == "__main__":
    reset_templates()
