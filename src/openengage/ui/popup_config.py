"""
Popup Configuration UI
Enables users to configure the website popup functionality
"""
import streamlit as st
import json
import os
import base64
import pandas as pd
from pathlib import Path

def display_popup_configuration():
    """Display the popup configuration interface"""
    st.title("Popup Configuration")
    st.write("Configure the popup that appears on your website. These settings will be saved to popupdata.json.")

    # Add section for sample user selection
    st.write("")
    with st.expander("👤 Sample User Selection", expanded=True):
        st.write("Select a sample user to automatically fill the popup content based on their data.")

        # Load target audience data (first 10 users)
        target_audience_path = os.path.join(os.getcwd(), "Sample Data For Mass Generation/TargetAudience.csv")
        user_popup_data_path = os.path.join(os.getcwd(), "Sample Data For Mass Generation/user_popup_data.csv")
        popup_text_path = os.path.join(os.getcwd(), "Sample Data For Mass Generation/popup_text.csv")

        target_df = None
        popup_df = None
        popup_text_df = None

        try:
            if os.path.exists(target_audience_path):
                target_df = pd.read_csv(target_audience_path)
                st.session_state.target_users = target_df.head(10)['user_email'].tolist()
            else:
                st.warning("TargetAudience.csv not found. Sample user selection is unavailable.")
                st.session_state.target_users = []

            # Load popup_text.csv as primary source for email filtering
            if os.path.exists(popup_text_path):
                popup_text_df = pd.read_csv(popup_text_path)
                st.session_state.popup_text_df = popup_text_df
                st.info("Using AI-generated popup content from popup_text.csv")
            else:
                st.session_state.popup_text_df = None

            # Load user_popup_data.csv as fallback source
            if os.path.exists(user_popup_data_path):
                popup_df = pd.read_csv(user_popup_data_path)
                st.session_state.popup_data_df = popup_df
            else:
                st.session_state.popup_data_df = None

            # Show warning if no files are found
            if not os.path.exists(popup_text_path) and not os.path.exists(user_popup_data_path):
                st.warning("No popup content files found. Auto-fill functionality is unavailable.")
        except Exception as e:
            st.error(f"Error loading sample data: {str(e)}")
            st.session_state.target_users = []
            st.session_state.popup_data_df = None

        # Create dropdown for sample users
        if hasattr(st.session_state, 'target_users') and len(st.session_state.target_users) > 0:
            selected_user = st.selectbox(
                "Select Sample User",
                options=["None"] + st.session_state.target_users,
                key="sample_user_selection"
            )

            # Auto-fill popup data when user is selected
            if selected_user != "None":
                try:
                    # Define default priority 1 product (this can be changed based on business requirements)
                    priority_1_product = "Agentic AI Pioneer Program"

                    # Load user details from user_popup_data.csv and popup content from popup_text.csv
                    user_details_data = None
                    popup_content_data = None

                    # Get user details from user_popup_data.csv
                    if hasattr(st.session_state, 'popup_data_df') and st.session_state.popup_data_df is not None:
                        user_details_data = st.session_state.popup_data_df[st.session_state.popup_data_df['user_email'] == selected_user]

                    # Get popup content from popup_text.csv
                    if hasattr(st.session_state, 'popup_text_df') and st.session_state.popup_text_df is not None:
                        popup_content_data = st.session_state.popup_text_df[st.session_state.popup_text_df['user_email'] == selected_user]

                    # Check if we have data from either source
                    if (popup_content_data is not None and not popup_content_data.empty) or (user_details_data is not None and not user_details_data.empty):
                        # Get popup content from popup_text.csv (preferred) or fallback to user_popup_data.csv
                        if popup_content_data is not None and not popup_content_data.empty:
                            popup_title = popup_content_data['popup_title'].values[0] if 'popup_title' in popup_content_data.columns and pd.notna(popup_content_data['popup_title'].values[0]) else f"Discover {priority_1_product}"
                            popup_text = popup_content_data['popup_text'].values[0] if 'popup_text' in popup_content_data.columns and pd.notna(popup_content_data['popup_text'].values[0]) else f"Enhance your AI skills today!"
                            button_text = popup_content_data['button_text'].values[0] if 'button_text' in popup_content_data.columns and pd.notna(popup_content_data['button_text'].values[0]) else 'Learn More'
                            popup_source = "popup_text.csv (AI-generated)"
                        elif user_details_data is not None and not user_details_data.empty:
                            # Fallback to user_popup_data.csv for popup content
                            popup_title = user_details_data['popup_title'].values[0] if 'popup_title' in user_details_data.columns and pd.notna(user_details_data['popup_title'].values[0]) else f"Discover {priority_1_product}"
                            raw_text = user_details_data['popup_text'].values[0] if 'popup_text' in user_details_data.columns else ''
                            popup_text = raw_text if pd.notna(raw_text) and raw_text != '' else f"Enhance your AI skills today!"
                            button_text = user_details_data['button_text'].values[0] if 'button_text' in user_details_data.columns and pd.notna(user_details_data['button_text'].values[0]) else 'Learn More'
                            popup_source = "user_popup_data.csv (legacy)"

                        # Get user details from user_popup_data.csv (preferred) or fallback to popup_text.csv
                        if user_details_data is not None and not user_details_data.empty:
                            target_product = user_details_data['target_product'].values[0] if 'target_product' in user_details_data.columns and pd.notna(user_details_data['target_product'].values[0]) else priority_1_product
                            user_stage = user_details_data['user_stage'].values[0] if 'user_stage' in user_details_data.columns and pd.notna(user_details_data['user_stage'].values[0]) else 'New Visitor'
                            behaviour_data = user_details_data['behaviour_data'].values[0] if 'behaviour_data' in user_details_data.columns and pd.notna(user_details_data['behaviour_data'].values[0]) else 'N/A'
                            redirect_url = user_details_data['redirect_url'].values[0] if 'redirect_url' in user_details_data.columns and pd.notna(user_details_data['redirect_url'].values[0]) else None
                            details_source = "user_popup_data.csv"
                        elif popup_content_data is not None and not popup_content_data.empty:
                            # Fallback to popup_text.csv for user details
                            target_product = popup_content_data['target_product'].values[0] if 'target_product' in popup_content_data.columns and pd.notna(popup_content_data['target_product'].values[0]) else priority_1_product
                            user_stage = popup_content_data['user_stage'].values[0] if 'user_stage' in popup_content_data.columns and pd.notna(popup_content_data['user_stage'].values[0]) else 'New Visitor'
                            behaviour_data = 'N/A (AI-generated content)'
                            redirect_url = None
                            details_source = "popup_text.csv"

                        # Generate redirect URL if not available
                        if not redirect_url:
                            product_urls = {
                                'Agentic AI Pioneer Program': 'https://www.analyticsvidhya.com/agentic-ai',
                                'Certified AI/ML BlackBelt Plus Program': 'https://www.analyticsvidhya.com/blackbelt',
                                'GenAI Pinnacle Plus Program': 'https://www.analyticsvidhya.com/pinnacleplus'
                            }
                            redirect_url = product_urls.get(target_product, 'https://www.analyticsvidhya.com')

                        # Store combined user data in session state
                        st.session_state.selected_user_data = {
                            'popup_title': popup_title,
                            'popup_text': popup_text,
                            'popup_button_text': button_text,
                            'popup_button_redirect_url': redirect_url
                        }

                        # Show success message and user details
                        st.success(f"Combined data loaded for: {selected_user}")
                        user_details = st.container()
                        user_details.markdown("**User Details:**")
                        user_details.markdown(f"**Behavior Data:** {behaviour_data}")
                        user_details.markdown(f"**Target Product:** {target_product}")
                        user_details.markdown(f"**User Stage:** {user_stage}")
                        user_details.markdown(f"**Popup Content Source:** {popup_source}")
                        user_details.markdown(f"**User Details Source:** {details_source}")
                        user_details.write("---")
                    else:
                        # User not found in either file, use default content
                        first_name = selected_user.split('@')[0]
                        st.session_state.selected_user_data = {
                            'popup_title': f"Welcome {first_name}",
                            'popup_text': f"Discover how {priority_1_product} can enhance your AI skills today!",
                            'popup_button_text': 'Learn More',
                            'popup_button_redirect_url': 'https://www.analyticsvidhya.com'
                        }
                        st.warning(f"No data found for user: {selected_user}. Using default content.")
                        user_details = st.container()
                        user_details.markdown("**User Details:**")
                        user_details.markdown(f"**Behavior Data:** No data available")
                        user_details.markdown(f"**Target Product:** {priority_1_product}")
                        user_details.markdown(f"**User Stage:** New Visitor")
                        user_details.markdown(f"**Content Source:** Default (no data found)")
                        user_details.write("---")
                except Exception as e:
                    st.error(f"Error loading user data: {str(e)}")
                    if 'selected_user_data' in st.session_state:
                        del st.session_state.selected_user_data
            elif selected_user == "None" and 'selected_user_data' in st.session_state:
                del st.session_state.selected_user_data

    # Initialize popup data
    popup_data = {
        "popup_title": "Welcome to OpenEngage",
        "popup_text": "",
        "popup_background_image": "",
        "popup_button_text": "Visit OpenEngage Website",
        "popup_button_redirect_url": "https://openengage.ai",
        "popup_image_opacity": 0.82,  # Default opacity value
        "popup_delay_seconds": 2,  # Default delay in seconds
        "popup_type": "banner",  # Default popup type: banner or form
        "popup_form_placeholder": "Enter your email or phone number"  # Default placeholder text for form input
    }

    # Load existing popup data if it exists
    popup_data_path = os.path.join(os.getcwd(), "popupdata.json")
    if os.path.exists(popup_data_path):
        try:
            with open(popup_data_path, "r") as f:
                popup_data = json.load(f)
        except json.JSONDecodeError:
            st.warning("Could not read existing popup data. Starting with default values.")

    # Load brand colors from brand_guidelines.json
    brand_colors = []
    brand_colors_path = os.path.join(os.getcwd(), "data", "brand_guidelines.json")
    try:
        if os.path.exists(brand_colors_path):
            with open(brand_colors_path, "r") as f:
                all_guidelines = json.load(f)

                # Get organization URL from session state if available
                org_url = None
                if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                    org_url = st.session_state.organization_url

                # Extract brand guidelines for the current organization or use the first one
                brand_guidelines = None

                if isinstance(all_guidelines, dict):
                    if org_url and org_url in all_guidelines:
                        # Use the current organization's guidelines
                        brand_guidelines = all_guidelines[org_url]
                    elif all_guidelines:
                        # Use the first organization's guidelines
                        brand_guidelines = next(iter(all_guidelines.values()))
                else:
                    # Handle legacy format (not a dictionary of organizations)
                    brand_guidelines = all_guidelines

                if brand_guidelines:
                    # Extract colors from brand guidelines
                    colors = [
                        brand_guidelines.get("primary_color", "#2674ED"),
                        brand_guidelines.get("secondary_color", "#FFFFFF"),
                        brand_guidelines.get("accent_color", "#F9C823"),
                        brand_guidelines.get("background_color", "#2261C2"),
                        brand_guidelines.get("text_color", "#383838")
                    ]

                    # Add colors to the brand_colors list, filtering out empty values
                    brand_colors = [color for color in colors if color]

                    st.sidebar.success("Loaded brand colors from brand_guidelines.json")
                else:
                    st.sidebar.warning("No brand guidelines found, using default colors")
        else:
            st.sidebar.warning("Brand guidelines file not found, using default colors")
    except Exception as e:
        st.sidebar.warning(f"Could not load brand colors: {e}")

    if not brand_colors:
        # Default colors if brand colors aren't found
        brand_colors = ["#8D06FE", "#02B9C9", "#02C688", "#FFFFFF", "#000000"]

    # Show current image if it exists
    current_image = popup_data.get("popup_background_image", "")
    image_exists = False

    # Check both relative to the current working directory and the project root
    possible_paths = [
        current_image,  # Directly as stored
        os.path.join(os.getcwd(), current_image),  # Relative to CWD
        os.path.join(os.path.dirname(os.getcwd()), current_image),  # One level up
        os.path.join("/Users/<USER>/Desktop/openengage", current_image)  # Absolute path to project root
    ]

    for path in possible_paths:
        if os.path.exists(path):
            current_image = path
            image_exists = True
            break

    if image_exists:
        st.image(current_image, caption="Current Background Image", width=300)

    # Use columns to divide the form and preview
    col1, col2 = st.columns([7, 5])

    with col1:
        with st.form("popup_configuration_form"):
            st.subheader("1. Popup Background Image")
            uploaded_file = st.file_uploader("Upload Popup Background Image", type=["jpg", "jpeg", "png"])

            # Add image opacity control
            if uploaded_file is not None or image_exists:
                st.subheader("Image Settings")
                image_opacity = st.slider(
                    "Image Opacity",
                    min_value=0.0,
                    max_value=1.0,
                    value=popup_data.get("popup_image_opacity", 0.82),
                    step=0.01,
                    help="Adjust the opacity of the background image (0 = transparent, 1 = fully opaque)"
                )

            st.subheader("2. Popup Type and Content")

            # Popup type selection (banner or form)
            popup_type_options = {"Banner (Single Click)": "banner", "Form (Collect User Input)": "form"}
            popup_type_label = next(key for key, value in popup_type_options.items() if value == popup_data.get("popup_type", "banner"))

            popup_type_selection = st.selectbox(
                "Popup Type",
                options=list(popup_type_options.keys()),
                index=list(popup_type_options.keys()).index(popup_type_label) if popup_type_label in popup_type_options.keys() else 0
            )
            popup_type = popup_type_options[popup_type_selection]

            # Popup title and text with auto-fill from user data if available
            popup_title = st.text_input(
                "Popup Title",
                value=st.session_state.selected_user_data['popup_title'] if 'selected_user_data' in st.session_state else popup_data.get("popup_title", "")
            )

            popup_text = st.text_area(
                "Popup Text (supports line breaks)",
                value=st.session_state.selected_user_data['popup_text'] if 'selected_user_data' in st.session_state else popup_data.get("popup_text", ""),
                height=100
            )

            # Show form-specific settings if form type is selected
            if popup_type == "form":
                popup_form_placeholder = st.text_input(
                    "Form Input Placeholder",
                    value=popup_data.get("popup_form_placeholder", "Enter your email or phone number")
                )
                st.info("The form will collect user input and redirect to your URL with the input appended as a 'contact' parameter.")
            else:
                popup_form_placeholder = popup_data.get("popup_form_placeholder", "Enter your email or phone number")

            st.subheader("3. Button Configuration")
            # Button text with auto-fill
            button_label = "Button Text" if popup_type == "banner" else "Submit Button Text"
            popup_button_text = st.text_input(
                button_label,
                value=st.session_state.selected_user_data['popup_button_text'] if 'selected_user_data' in st.session_state else popup_data.get("popup_button_text", "")
            )

            # Redirect URL with auto-fill
            redirect_label = "Button Redirect URL" if popup_type == "banner" else "Form Submission Redirect URL"
            popup_button_redirect_url = st.text_input(
                redirect_label,
                value=st.session_state.selected_user_data['popup_button_redirect_url'] if 'selected_user_data' in st.session_state else popup_data.get("popup_button_redirect_url", "")
            )

            st.subheader("4. Timing Settings")
            popup_delay_seconds = st.number_input(
                "Display Delay (seconds)",
                min_value=0,
                max_value=30,
                value=int(popup_data.get("popup_delay_seconds", 2)),
                help="Set how many seconds to wait before showing the popup after the page loads"
            )

            st.subheader("5. Appearance")
            color_col1, color_col2 = st.columns(2)
            with color_col1:
                popup_primary_color = st.selectbox(
                    "Primary Color (Title)",
                    options=brand_colors,
                    index=0 if not popup_data.get("popup_primary_color") else brand_colors.index(popup_data.get("popup_primary_color")) if popup_data.get("popup_primary_color") in brand_colors else 0,
                    format_func=lambda x: f"{x}",
                    key="primary_color"
                )

            with color_col2:
                popup_button_color = st.selectbox(
                    "Button Color",
                    options=brand_colors,
                    index=1 if not popup_data.get("popup_button_color") else brand_colors.index(popup_data.get("popup_button_color")) if popup_data.get("popup_button_color") in brand_colors else 1,
                    format_func=lambda x: f"{x}",
                    key="button_color"
                )

            # Save button
            submitted = st.form_submit_button("Save Configuration")

            if submitted:
                # Save the uploaded image if present
                if uploaded_file is not None:
                    # Get file extension
                    file_extension = Path(uploaded_file.name).suffix
                    image_path = f"popup_background{file_extension}"

                    # Save the file
                    with open(image_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())

                    # Update image path in popup data
                    popup_data["popup_background_image"] = image_path
                    current_image = image_path

                # Update popup data with form values
                popup_data["popup_title"] = popup_title
                popup_data["popup_text"] = popup_text
                popup_data["popup_button_text"] = popup_button_text
                popup_data["popup_button_redirect_url"] = popup_button_redirect_url
                popup_data["popup_primary_color"] = popup_primary_color
                popup_data["popup_button_color"] = popup_button_color
                popup_data["popup_delay_seconds"] = popup_delay_seconds
                popup_data["popup_type"] = popup_type
                popup_data["popup_form_placeholder"] = popup_form_placeholder

                # Update popup image opacity
                if uploaded_file is not None or image_exists:
                    popup_data["popup_image_opacity"] = image_opacity

                # Save popup data to JSON file
                try:
                    with open("popupdata.json", "w") as f:
                        json.dump(popup_data, f, indent=4)
                    st.success("Popup configuration saved successfully! The website popup has been updated.")
                except Exception as e:
                    st.error(f"Error saving popup configuration: {e}")

    # Store popup configuration in session state
    if "popup_title" not in st.session_state:
        st.session_state.popup_title = popup_data.get("popup_title", "Welcome to OpenEngage")
    if "popup_text" not in st.session_state:
        st.session_state.popup_text = popup_data.get("popup_text", "")
    if "popup_button_text" not in st.session_state:
        st.session_state.popup_button_text = popup_data.get("popup_button_text", "Visit OpenEngage Website")
    if "popup_primary_color" not in st.session_state:
        st.session_state.popup_primary_color = popup_data.get("popup_primary_color", brand_colors[0])
    if "popup_button_color" not in st.session_state:
        st.session_state.popup_button_color = popup_data.get("popup_button_color", brand_colors[1])

    # Get current values from session state or form
    try:
        current_title = popup_title if 'popup_title' in locals() else st.session_state.popup_title
        current_text = popup_text if 'popup_text' in locals() else st.session_state.popup_text
        current_button_text = popup_button_text if 'popup_button_text' in locals() else st.session_state.popup_button_text
        current_primary_color = popup_primary_color if 'popup_primary_color' in locals() else st.session_state.popup_primary_color
        current_button_color = popup_button_color if 'popup_button_color' in locals() else st.session_state.popup_button_color
    except:
        current_title = popup_data.get("popup_title", "Welcome to OpenEngage")
        current_text = popup_data.get("popup_text", "")
        current_button_text = popup_data.get("popup_button_text", "Visit OpenEngage Website")
        current_primary_color = popup_data.get("popup_primary_color", brand_colors[0])
        current_button_color = popup_data.get("popup_button_color", brand_colors[1])

    # Live preview section outside the form
    with col2:
        st.subheader("Live Preview")

        # Use base64 encoding for the background image if it exists
        background_image_css = ""
        if image_exists:
            try:
                with open(current_image, "rb") as img_file:
                    img_data = base64.b64encode(img_file.read()).decode('utf-8')
                    img_type = Path(current_image).suffix.lstrip('.')
                    if img_type.lower() in ['jpg', 'jpeg']:
                        img_type = 'jpeg'
                    background_image_css = f"background-image: linear-gradient(rgba(255, 255, 255, {popup_data.get('popup_image_opacity', 0.82)}), rgba(255, 255, 255, {popup_data.get('popup_image_opacity', 0.82)})), url('data:image/{img_type};base64,{img_data}');"
            except Exception as e:
                st.warning(f"Could not load image for preview: {e}")
                background_image_css = ""

        # Determine which preview to show based on popup type
        current_popup_type = popup_type if 'popup_type' in locals() else popup_data.get("popup_type", "banner")
        current_form_placeholder = popup_form_placeholder if 'popup_form_placeholder' in locals() else popup_data.get("popup_form_placeholder", "Enter your email or phone number")

        # Create either a banner or form preview based on the current popup type
        if current_popup_type == "form":
            preview_html = f"""
            <div style="
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
                {background_image_css}
                background-size: cover;
                background-position: center;
                text-align: center;
                width: 100%;
                max-width: 500px;
                margin: 0 auto;
            ">
                <h3 style="color: {current_primary_color};">{current_title}</h3>
                <p>{current_text}</p>
                <div style="margin: 15px 0;">
                    <input type="text" placeholder="{current_form_placeholder}" style="
                        width: 100%;
                        padding: 10px;
                        margin-bottom: 15px;
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        font-size: 16px;
                    ">
                    <button style="
                        background-color: {current_button_color};
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">{current_button_text}</button>
                </div>
            </div>
            """
        else:
            # Default banner preview
            preview_html = f"""
            <div style="
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
                {background_image_css}
                background-size: cover;
                background-position: center;
                text-align: center;
                width: 100%;
                max-width: 500px;
                margin: 0 auto;
            ">
                <h3 style="color: {current_primary_color};">{current_title}</h3>
                <p>{current_text}</p>
                <button style="
                    background-color: {current_button_color};
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                ">{current_button_text}</button>
            </div>
            """
        st.markdown(preview_html, unsafe_allow_html=True)

        # Add Demo Blog button below the preview
        st.markdown("""
        <div style="margin-top: 30px; text-align: center;">
            <a href="http://localhost:8000/demo_blog/index.html" target="_blank" style="text-decoration: none;">
                <button style="background-color: #FF6B6B; color: white; border: none; border-radius: 4px; padding: 8px 16px; cursor: pointer; font-weight: bold; display: inline-block;">
                    📝 Open Demo Blog
                </button>
            </a>
        </div>
        """, unsafe_allow_html=True)

# Function should only be called when needed, not on import
