"""
UI component for channel selection in OpenEngage marketing automation platform.
"""
import streamlit as st
import pandas as pd
import numpy as np
import os
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go

from core.channel_selector import ChannelSelector, load_user_engagement_from_performance_data

def display_channel_selection():
    """Display the channel selection UI component."""
    st.title("Channel Selection")
    
    st.markdown("""
    Select the optimal marketing channel (Email or WhatsApp) for your audience based on:
    
    1. **Channel Availability**: Whether users have email addresses and/or phone numbers
    2. **Past Engagement**: How users have engaged with previous communications
    3. **Content Suitability**: What type of content you're sending
    4. **Message Urgency**: How time-sensitive your message is
    """)
    
    # Create tabs for different sections
    tab1, tab2, tab3 = st.tabs(["Channel Selection", "Configuration", "Analytics"])
    
    # Tab 1: Channel Selection
    with tab1:
        st.header("Select Channels for Your Campaign")
        
        # File uploader for user data CSV
        st.subheader("Upload User Data")
        user_data_file = st.file_uploader(
            "Upload a CSV file with user data (must include user_email and/or phone_number columns)",
            type=["csv"]
        )
        
        # Content type and urgency
        col1, col2 = st.columns(2)
        with col1:
            content_type = st.selectbox(
                "Content Type",
                options=[
                    "general",
                    "newsletter", 
                    "detailed_update", 
                    "visual_rich", 
                    "promotional", 
                    "long_form",
                    "alert", 
                    "reminder", 
                    "confirmation", 
                    "short_update", 
                    "quick_response"
                ],
                help="Select the type of content you're sending. This affects channel selection."
            )
            
        with col2:
            urgency_level = st.selectbox(
                "Urgency Level",
                options=["low", "medium", "high"],
                index=1,
                help="Select the urgency level of your message. High urgency favors WhatsApp."
            )
            
        # Performance data for engagement calculation
        st.subheader("Engagement Data (Optional)")
        performance_file = st.file_uploader(
            "Upload a CSV file with previous performance data",
            type=["csv"],
            help="If provided, will use actual engagement data. Otherwise will estimate engagement."
        )
        
        # Process button
        process_button = st.button("Process and Select Channels", type="primary")
        
        if process_button and user_data_file:
            try:
                # Load user data
                user_df = pd.read_csv(user_data_file)
                
                # Check required columns
                if "user_email" not in user_df.columns and "phone_number" not in user_df.columns:
                    st.error("CSV file must contain at least one of these columns: user_email, phone_number")
                    return
                    
                # Convert to list of dictionaries
                users_data = user_df.to_dict('records')
                
                # Initialize channel selector
                channel_selector = ChannelSelector()
                
                # Process engagement data if available
                engagement_data = None
                if performance_file:
                    # Save performance file to temp location
                    temp_perf_path = "temp_performance.csv"
                    with open(temp_perf_path, "wb") as f:
                        f.write(performance_file.getvalue())
                    
                    # Load engagement data
                    engagement_data = load_user_engagement_from_performance_data(temp_perf_path)
                    
                    # Remove temp file
                    if os.path.exists(temp_perf_path):
                        os.remove(temp_perf_path)
                
                # Process user channels
                with st.spinner("Processing channel selection..."):
                    # Individual channel selection with scores for each user
                    results = []
                    for user_data in users_data:
                        channel, scores = channel_selector.select_channel(
                            user_data,
                            content_type=content_type,
                            urgency_level=urgency_level,
                            engagement_data=engagement_data
                        )
                        
                        # Add results to list
                        result = {
                            "selected_channel": channel,
                            "email_score": scores.get("email", 0),
                            "whatsapp_score": scores.get("whatsapp", 0),
                        }
                        
                        # Add user identifiers
                        if "user_email" in user_data:
                            result["user_email"] = user_data["user_email"]
                        if "phone_number" in user_data:
                            result["phone_number"] = user_data["phone_number"]
                            
                        results.append(result)
                    
                    # Create results dataframe
                    results_df = pd.DataFrame(results)
                    
                    # Group users by channel
                    channels = channel_selector.batch_select_channels(
                        users_data, 
                        content_type=content_type,
                        urgency_level=urgency_level
                    )
                
                # Display results
                st.subheader("Channel Selection Results")
                
                # Summary metrics
                col1, col2, col3 = st.columns(3)
                with col1:
                    email_count = len(channels["email"])
                    st.metric("Email Users", email_count)
                
                with col2:
                    whatsapp_count = len(channels["whatsapp"])
                    st.metric("WhatsApp Users", whatsapp_count)
                    
                with col3:
                    unavailable_count = len(channels["unavailable"])
                    st.metric("Unavailable Users", unavailable_count)
                
                # Display pie chart of channel distribution
                fig = px.pie(
                    values=[email_count, whatsapp_count, unavailable_count],
                    names=["Email", "WhatsApp", "Unavailable"],
                    title="Channel Distribution",
                    color_discrete_sequence=["#8D06FE", "#02B9C9", "#E74C3C"]
                )
                st.plotly_chart(fig)
                
                # Display detailed results
                st.subheader("Detailed User Channel Selection")
                st.dataframe(results_df)
                
                # Add download buttons for segmented lists
                st.subheader("Download Segmented User Lists")
                
                col1, col2 = st.columns(2)
                with col1:
                    # Create email users CSV
                    email_df = user_df[user_df["user_email"].isin([u.get("user_email") for u in channels["email"]])]
                    email_csv = email_df.to_csv(index=False)
                    st.download_button(
                        label="Download Email Users CSV",
                        data=email_csv,
                        file_name="email_users.csv",
                        mime="text/csv",
                    )
                
                with col2:
                    # Create WhatsApp users CSV
                    if "phone_number" in user_df.columns:
                        whatsapp_df = user_df[user_df["phone_number"].isin([u.get("phone_number") for u in channels["whatsapp"]])]
                        whatsapp_csv = whatsapp_df.to_csv(index=False)
                        st.download_button(
                            label="Download WhatsApp Users CSV",
                            data=whatsapp_csv,
                            file_name="whatsapp_users.csv",
                            mime="text/csv",
                        )
                    else:
                        st.warning("Phone number column not found in user data.")
                
            except Exception as e:
                st.error(f"Error processing user data: {str(e)}")
    
    # Tab 2: Configuration
    with tab2:
        st.header("Channel Selection Configuration")
        
        # Weights configuration
        st.subheader("Factor Weights")
        st.info("Adjust the importance of each factor in the channel selection algorithm.")
        
        # Get current weights
        channel_selector = ChannelSelector()
        current_weights = channel_selector.weights
        
        # Create sliders for each weight
        availability_weight = st.slider(
            "Availability Weight", 
            min_value=0.1, 
            max_value=1.0, 
            value=float(current_weights.get("availability", 0.4)),
            step=0.1,
            help="How important is channel availability in the decision."
        )
        
        engagement_weight = st.slider(
            "Engagement Weight", 
            min_value=0.1, 
            max_value=1.0, 
            value=float(current_weights.get("engagement", 0.3)),
            step=0.1,
            help="How important is past engagement in the decision."
        )
        
        content_weight = st.slider(
            "Content Suitability Weight", 
            min_value=0.1, 
            max_value=1.0, 
            value=float(current_weights.get("content_suitability", 0.2)),
            step=0.1,
            help="How important is content suitability in the decision."
        )
        
        urgency_weight = st.slider(
            "Urgency Weight", 
            min_value=0.1, 
            max_value=1.0, 
            value=float(current_weights.get("urgency", 0.1)),
            step=0.1,
            help="How important is message urgency in the decision."
        )
        
        # Normalize weights
        total = availability_weight + engagement_weight + content_weight + urgency_weight
        normalized_weights = {
            "availability": availability_weight / total,
            "engagement": engagement_weight / total,
            "content_suitability": content_weight / total,
            "urgency": urgency_weight / total
        }
        
        # Display normalized weights
        st.subheader("Normalized Weights")
        for key, value in normalized_weights.items():
            st.text(f"{key}: {value:.2f}")
            
        # Save configuration button
        if st.button("Save Configuration"):
            try:
                # Create config directory if it doesn't exist
                config_dir = os.path.join("data", "config")
                os.makedirs(config_dir, exist_ok=True)
                
                # Save weights to config file
                config_file = os.path.join(config_dir, "channel_selector_config.json")
                with open(config_file, "w") as f:
                    json.dump({"weights": normalized_weights}, f, indent=2)
                    
                st.success(f"Configuration saved successfully to {config_file}")
            except Exception as e:
                st.error(f"Error saving configuration: {str(e)}")
    
    # Tab 3: Analytics
    with tab3:
        st.header("Channel Selection Analytics")
        
        # Mock data for distribution visualization
        st.subheader("Factor Influence Analysis")
        
        # Create mock data for visualization
        mock_data = pd.DataFrame({
            "Content Type": ["newsletter", "alert", "promotional", "reminder", "confirmation"],
            "Email Selected (%)": [85, 30, 70, 40, 45],
            "WhatsApp Selected (%)": [15, 70, 30, 60, 55]
        })
        
        # Create bar chart
        fig = px.bar(
            mock_data, 
            x="Content Type", 
            y=["Email Selected (%)", "WhatsApp Selected (%)"],
            title="Channel Selection by Content Type",
            barmode="stack",
            color_discrete_sequence=["#8D06FE", "#02B9C9"]
        )
        st.plotly_chart(fig)
        
        # Urgency influence chart
        urgency_data = pd.DataFrame({
            "Urgency": ["Low", "Medium", "High"],
            "Email": [70, 50, 30],
            "WhatsApp": [30, 50, 70]
        })
        
        fig2 = px.bar(
            urgency_data, 
            x="Urgency", 
            y=["Email", "WhatsApp"],
            title="Channel Selection by Urgency Level",
            barmode="stack",
            color_discrete_sequence=["#8D06FE", "#02B9C9"]
        )
        st.plotly_chart(fig2)
        
        # User engagement correlation
        st.subheader("Engagement Correlation")
        st.info("This chart shows how user engagement correlates with channel selection.")
        
        # Create fake data for visualization
        np.random.seed(42)
        email_opens = np.random.uniform(0, 100, 100)
        whatsapp_reads = np.random.uniform(0, 100, 100)
        
        # Create correlation scatter plot
        fig3 = px.scatter(
            x=email_opens,
            y=whatsapp_reads,
            title="Email Open Rate vs WhatsApp Read Rate",
            labels={"x": "Email Open Rate (%)", "y": "WhatsApp Read Rate (%)"}
        )
        
        # Add a line to divide the chart into regions
        fig3.add_shape(
            type="line",
            x0=0,
            y0=0,
            x1=100,
            y1=100,
            line=dict(color="gray", width=1, dash="dash"),
        )
        
        # Add annotations
        fig3.add_annotation(
            x=30, 
            y=70,
            text="WhatsApp Preferred",
            showarrow=False,
            font=dict(size=14)
        )
        
        fig3.add_annotation(
            x=70, 
            y=30,
            text="Email Preferred",
            showarrow=False,
            font=dict(size=14)
        )
        
        st.plotly_chart(fig3)
