"""
OpenEngage - Insights Chatbot UI Component
Provides a chat interface for querying the campaign database using natural language.
"""
import streamlit as st
import sys
import os
import subprocess
import time
import re
import sqlite3
import json
import traceback

# Add parent directory to path to import insights_agent
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from insights_agent import run_query

# Import groq for error handling
try:
    import groq
except ImportError:
    groq = None

def run_script(script_path):
    """Run a Python script as a subprocess and return its output"""
    print(f"[DEBUG] Running script: {script_path}")
    try:
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"[DEBUG] Script execution successful. Output length: {len(result.stdout)} bytes")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"[DEBUG] Script execution failed with error: {e.stderr}")
        return False, f"Error: {e.stderr}"
        
def check_database_status(db_path):
    """Check the status of the database and return diagnostic information"""
    results = {}
    print(f"\n[DEBUG] Checking database status: {db_path}")
    
    # Check if file exists
    if not os.path.exists(db_path):
        print(f"[DEBUG] Database file does not exist: {db_path}")
        return {"exists": False, "error": f"Database file does not exist at {db_path}"}
    
    results["exists"] = True
    results["file_size"] = os.path.getsize(db_path)
    print(f"[DEBUG] Database file exists. Size: {results['file_size']} bytes")
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        results["tables"] = [t[0] for t in tables]
        print(f"[DEBUG] Tables in database: {results['tables']}")
        
        # Get row counts for each table
        table_stats = {}
        for table in results["tables"]:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_stats[table] = {"count": count}
                
                # Get column info for the table
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                table_stats[table]["columns"] = [col[1] for col in columns]
                
                print(f"[DEBUG] Table '{table}' has {count} rows and {len(columns)} columns")
                
                # Get a sample row if table is not empty
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 1")
                    sample = cursor.fetchone()
                    table_stats[table]["sample_row"] = sample
                    print(f"[DEBUG] Sample row from '{table}': {sample[:3]}...")
            except Exception as e:
                table_stats[table] = {"error": str(e)}
                print(f"[DEBUG] Error analyzing table '{table}': {str(e)}")
        
        results["table_stats"] = table_stats
        
        # Check total record count across all tables
        total_records = sum(stats.get("count", 0) for table, stats in table_stats.items() if isinstance(stats, dict))
        results["total_records"] = total_records
        print(f"[DEBUG] Total records across all tables: {total_records}")
        
        conn.close()
    except Exception as e:
        print(f"[DEBUG] Database connection error: {str(e)}")
        results["error"] = str(e)
        results["traceback"] = traceback.format_exc()
    
    return results

def display_insights_chatbot():
    """Display the insights chatbot interface"""
    
    # Add a debug log to the Streamlit app
    debug_log = st.expander("Debug Information", expanded=False)
    with debug_log:
        st.text("Debug information will be displayed here when available.")
        
    # Check database status
    # Calculate path to the root directory (openengage) and locate DB there
    # Need to go up 4 levels: ui -> openengage -> src -> openengage(root)
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    print("Here it is:-",root_dir)
    db_path = os.path.join(root_dir, "email_marketing.db")
    print(f"[DEBUG] Using database path: {db_path}")
    db_status = check_database_status(db_path)
    
    with debug_log:
        st.subheader("Database Status")
        st.json(json.dumps(db_status, default=str))
    
    # Get the absolute paths to the scripts
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    configure_db_path = os.path.join(parent_dir, 'configure_db.py')
    process_data_path = os.path.join(parent_dir, 'process_performance_data.py')
    
    # Create a container for the header and buttons
    header_container = st.container()
    
    with header_container:
        # Create a layout with columns for header and buttons
        header_col, btn1_col, btn2_col = st.columns([0.7, 0.15, 0.15])
        
        with header_col:
            # Set up page header
            st.markdown("""
                <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;'>
                    <h2 style='color: #FFFFFF; margin: 0; font-size: 1.5rem;'>Insights Chatbot</h2>
                    <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                        Ask questions about your campaign data using natural language
                    </p>
                </div>
            """, unsafe_allow_html=True)
        
        with btn1_col:
            # Button to run configure_db.py
            if st.button('Setup DB', key='setup_db_btn', help='Initialize database with sample data'):
                with st.spinner('Setting up database...'):
                    # Check database before setup
                    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                    db_path = os.path.join(root_dir, "email_marketing.db")
                    pre_status = check_database_status(db_path)
                    
                    # Run the setup script
                    success, output = run_script(configure_db_path)
                    print(f"[DEBUG] configure_db.py output:\n{output}")
                    
                    # Check database after setup
                    post_status = check_database_status(db_path)
                    
                    # Log debug info
                    with debug_log:
                        st.subheader("Database Setup Results")
                        st.write("**Before Setup:**")
                        st.json(json.dumps(pre_status, default=str))
                        st.write("**After Setup:**")
                        st.json(json.dumps(post_status, default=str))
                        st.write("**Script Output:**")
                        st.code(output)
                    
                    if success:
                        # Verify the database actually has data now
                        if post_status.get("total_records", 0) > 0:
                            st.success(f'Database setup complete with {post_status.get("total_records")} total records!')
                        else:
                            st.warning(f'Database setup completed but database appears to still be empty!')
                            
                        # Add a system message to the chat history
                        if 'insights_chat_history' in st.session_state:
                            st.session_state.insights_chat_history.append({
                                "role": "assistant", 
                                "content": f"✅ Database has been reset with fresh sample data. {post_status.get('total_records', 0)} records found. You can now query the database."
                            })
                    else:
                        st.error('Database setup failed!')
                        st.error(output)
        
        with btn2_col:
            # Button to run process_performance_data.py
            if st.button('Process Data', key='process_data_btn', help='Process performance data'):
                with st.spinner('Processing performance data...'):
                    success, output = run_script(process_data_path)
                    if success:
                        st.success('Data processing complete!')
                        # Add a system message to the chat history
                        if 'insights_chat_history' in st.session_state:
                            st.session_state.insights_chat_history.append({
                                "role": "assistant", 
                                "content": "✅ Performance data has been processed and loaded into the database. You can now query the updated data."
                            })
                    else:
                        st.error('Data processing failed!')
                        st.error(output)
    
    # Initialize chat history in session state if it doesn't exist
    if "insights_chat_history" not in st.session_state:
        st.session_state.insights_chat_history = []
    
    # Welcome message
    if not st.session_state.insights_chat_history:
        st.info("Hello! Ask me anything you want from your database.")
    
    # Display chat history
    for message in st.session_state.insights_chat_history:
        if message["role"] == "user":
            with st.chat_message("user", avatar="👤"):
                st.markdown(message["content"])
        else:
            with st.chat_message("assistant", avatar="🤖"):
                st.markdown(message["content"])
    
    # Chat input
    query = st.chat_input("Type your database query here...")
    
    # Process the query when submitted
    if query:
        # Add user message to chat history
        st.session_state.insights_chat_history.append({"role": "user", "content": query})
        
        # Display user message
        with st.chat_message("user", avatar="👤"):
            st.markdown(query)
        
        # Process query and display response
        with st.chat_message("assistant", avatar="🤖"):
            with st.spinner("Analyzing your data..."):
                # Check database status before querying
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                db_path = os.path.join(root_dir, "email_marketing.db")
                db_status = check_database_status(db_path)
                
                # Log debug info about database state before query
                with debug_log:
                    st.subheader("Pre-Query Database Status")
                    st.json(json.dumps(db_status, default=str))
                    
                # If database is empty, warn the user
                if not db_status.get("exists", False) or db_status.get("total_records", 0) == 0:
                    error_msg = "Database is empty or does not exist. Please set up the database first using the 'Setup DB' button."
                    st.error(error_msg)
                    st.session_state.insights_chat_history.append({"role": "assistant", "content": f"⚠️ {error_msg}"})
                    
                    # Display the debug info directly in chat for clarity
                    st.code(f"Database path: {db_path}\nExists: {db_status.get('exists', False)}\nTables: {db_status.get('tables', [])}\nTotal records: {db_status.get('total_records', 0)}")
                    return
                
                # Initialize variables for retry logic
                max_retries = 3
                retry_count = 0
                retry_delay = 5  # Start with 5 seconds
                
                while retry_count <= max_retries:
                    try:
                        # Log what we're about to do
                        print(f"\n[DEBUG] Executing query: '{query}'")
                        st.info(f"Querying database with: '{query}'")
                        
                        # Call the run_query function from insights_agent.py
                        response = run_query(query)
                        
                        # Log the response
                        print(f"[DEBUG] Got response from agent: {response[:100]}...")
                        with debug_log:
                            st.subheader("Agent Response")
                            st.text(response)
                            
                        # Check for problematic response patterns
                        if "7669 entries" in response and db_status.get("total_records", 0) != 7669:
                            st.warning(f"⚠️ Agent reports 7669 entries but database actually has {db_status.get('total_records', 0)} records. This may indicate a problem with the agent's understanding of the database.")
                        
                        # Add assistant response to chat history
                        st.session_state.insights_chat_history.append({"role": "assistant", "content": response})
                        
                        # Display the response
                        st.markdown(response)
                        break  # Success, exit the retry loop
                        
                    except Exception as e:
                        error_str = str(e)
                        
                        # Check if it's a rate limit error
                        if "rate_limit_exceeded" in error_str or "Rate limit reached" in error_str:
                            retry_count += 1
                            
                            # Extract wait time if available
                            wait_time_match = re.search(r'try again in (\d+\.?\d*)s', error_str)
                            wait_time = float(wait_time_match.group(1)) if wait_time_match else retry_delay
                            
                            # If we have more retries left, wait and try again
                            if retry_count <= max_retries:
                                retry_message = f"Rate limit reached. Waiting {wait_time:.1f} seconds and retrying... (Attempt {retry_count}/{max_retries})"
                                st.warning(retry_message)
                                time.sleep(wait_time)
                                continue
                            else:
                                error_message = "Rate limit exceeded. Maximum retry attempts reached. Please try again later."
                        else:
                            # For other errors, don't retry
                            error_message = f"Error processing your query: {error_str}"
                            break
                
                # If we've exhausted all retries or encountered a non-rate-limit error
                if retry_count > max_retries or 'error_message' in locals():
                    st.error(error_message)
                    st.session_state.insights_chat_history.append({"role": "assistant", "content": error_message})
