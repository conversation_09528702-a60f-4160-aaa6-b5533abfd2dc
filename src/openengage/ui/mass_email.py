"""
Mass email generator UI component for OpenEngage.
"""
import os
import streamlit as st
from datetime import datetime, timedelta

# Try to import pandas
try:
    import pandas as pd
except ImportError:
    st.error("Pandas module not found. Please install it with 'pip install pandas'")

# Import for relative imports
import sys
from pathlib import Path

# Add the src directory to the Python path if needed
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.append(str(src_path))

def display_mass_email_generator():
    """Display the mass email generator interface"""

    # Initialize session state for default data usage
    if 'use_default_data' not in st.session_state:
        st.session_state.use_default_data = False

    if 'default_data_loaded' not in st.session_state:
        st.session_state.default_data_loaded = False
        st.session_state.default_data_df = None

    # Create a layout with two columns - title on left, channel selector on right
    title_col, channel_col = st.columns([3, 1])

    with title_col:
        st.write("## 📧 Mass Campaign Generator")
        st.write("Upload a CSV file containing user details or use our default user data to generate personalized campaigns at scale.")

    with channel_col:
        # Initialize session state for selected channel if not already set
        if 'selected_sending_channel' not in st.session_state:
            st.session_state.selected_sending_channel = "Email"

        # Add a selectbox for channel selection
        st.write("### 📤 Sending Channel")
        selected_channel = st.selectbox(
            "Choose channel:",
            options=["Email", "WhatsApp"],
            index=0 if st.session_state.selected_sending_channel == "Email" else 1,
            key="channel_selector"
        )

        # Update session state when selection changes
        if selected_channel != st.session_state.selected_sending_channel:
            st.session_state.selected_sending_channel = selected_channel
            st.success(f"Switched to {selected_channel} channel")

        # Show the currently selected channel with an icon
        channel_icon = "📧" if st.session_state.selected_sending_channel == "Email" else "💬"
        st.info(f"{channel_icon} Using {st.session_state.selected_sending_channel} channel")

    # Show sample format based on selected channel
    with st.expander("📝 Required CSV Format"):
        if st.session_state.selected_sending_channel == "Email":
            st.write("""
            ### Email Channel Format

            Your CSV file must have these exact columns:
            - **user_email**: Email address of the recipient
            - **first_name**: First name of the recipient
            - **user_behaviour**: Description of user's behavior or preferences
            - **user_stage**: User journey stage (must match one of your template stages)

            Sample CSV Format:
            ```csv
            user_email,first_name,user_behaviour,user_stage
            <EMAIL>,John,Viewed product page 3 times,New
            <EMAIL>,Jane,Added to cart but didn't purchase,Product Viewed
            ```
            """)
        else:  # WhatsApp
            st.write("""
            ### WhatsApp Channel Format

            Your CSV file must have these exact columns:
            - **phone_number**: Phone number of the recipient (with country code, e.g., 919876543210)
            - **first_name**: First name of the recipient (will be used as {{1}} in templates)
            - **user_behaviour**: Description of user's behavior or preferences (will be used to generate {{2}} content)
            - **user_stage**: User journey stage (must match one of your template stages)
            - **product**: (Optional) Product name - if not provided, a product will be assigned based on user behavior

            Sample CSV Format:
            ```csv
            phone_number,first_name,user_behaviour,user_stage
            919876543210,John,Viewed product page 3 times,New
            918765432109,Jane,Added to cart but didn't purchase,Product Viewed
            ```

            Note: Make sure your phone numbers include the country code without any spaces or special characters.
            """)

    # Show offer details format based on selected channel
    with st.expander("🎁 Optional Offer Details Format"):
        if st.session_state.selected_sending_channel == "Email":
            st.write("""
            ### Email Offer Details

            If you want to include special offers in your emails, you can upload a CSV file with offer details.
            The CSV file should have these columns:
            - **offer_start_time**: Start time of the offer in ISO format (e.g., '2023-06-01T00:00:00')
            - **offer_end_time**: End time of the offer in ISO format (e.g., '2023-06-30T23:59:59')
            - **offer_details**: Description of the offer (e.g., '20% discount', 'Buy one get one free')

            Sample Offer CSV Format:
            ```csv
            offer_start_time,offer_end_time,offer_details
            2023-06-01T00:00:00,2023-06-03T23:59:59,20% discount on all products
            2023-06-15T00:00:00,2023-06-22T23:59:59,Free shipping on orders over $50
            ```

            The system will apply the first valid offer (where current time is between start and end time) to all users.
            All times should be in UTC format.
            """)
        else:  # WhatsApp
            st.write("""
            ### WhatsApp Offer Details

            For WhatsApp, offers are included directly in the template content as part of the {{2}} parameter.

            Due to WhatsApp's template restrictions, separate offer files are not supported. Instead:

            1. Make sure your templates are approved by WhatsApp/Gupshup
            2. The system will generate personalized content for the {{2}} parameter that includes relevant offers
            3. Any offer details should be brief and comply with WhatsApp's content policies

            Note: WhatsApp templates must be pre-approved and cannot be modified at send time. The {{2}} parameter
            will be used to include personalized content that may mention offers, but cannot include HTML or complex formatting.
            """)

    # Create three columns for file uploaders and default data button
    col1, col2, col3 = st.columns(3)

    with col1:
        # Main CSV file uploader - label changes based on selected channel
        if st.session_state.selected_sending_channel == "Email":
            st.write("### Email User Data CSV")
            uploaded_file = st.file_uploader("Choose a CSV file with email user data", type='csv', key="user_data_csv")
        else:
            st.write("### WhatsApp User Data CSV")
            uploaded_file = st.file_uploader("Choose a CSV file with WhatsApp user data", type='csv', key="whatsapp_user_data_csv")

    with col2:
        # Optional offer details uploader - only shown for Email channel
        if st.session_state.selected_sending_channel == "Email":
            st.write("### Offer Details CSV (Optional)")
            offer_file = st.file_uploader("Choose a CSV file with offer details", type='csv', key="offer_details_csv")
        else:
            st.write("### WhatsApp Templates")
            st.info("Make sure you have configured your WhatsApp templates in the Templates section before proceeding.")
            # Hidden placeholder to maintain offer_file variable
            offer_file = None

        # Sample offer data download button - only for Email channel
        if st.session_state.selected_sending_channel == "Email" and not offer_file:
            try:
                # Get current time
                now = datetime.now()

                # Create sample offer data
                sample_offers = [
                    {
                        'offer_start_time': now.isoformat(),
                        'offer_end_time': (now + timedelta(days=7)).isoformat(),
                        'offer_details': '15% discount on your next purchase'
                    },
                    {
                        'offer_start_time': (now + timedelta(days=10)).isoformat(),
                        'offer_end_time': (now + timedelta(days=20)).isoformat(),
                        'offer_details': 'Buy one get one free on selected items'
                    }
                ]

                # Convert to DataFrame and then to CSV
                sample_df = pd.DataFrame(sample_offers)
                csv = sample_df.to_csv(index=False)

                # Create a download button for the sample
                st.download_button(
                    label="Download Sample Offer CSV",
                    data=csv,
                    file_name="sample_offers.csv",
                    mime="text/csv",
                    key="download_sample_offer_csv"
                )
            except Exception as e:
                st.error(f"Error generating sample offer data: {str(e)}")
        elif st.session_state.selected_sending_channel == "WhatsApp":
            # Show a link to the WhatsApp template configuration
            st.markdown("[Configure WhatsApp Templates →](/?page=Templates)", unsafe_allow_html=True)

    with col3:
        # Default user data button
        st.write("### Realtime Generation")

        # Check if we should reset the use_default_data state (when user uploads a file)
        if uploaded_file is not None and st.session_state.use_default_data:
            st.session_state.use_default_data = False
            st.session_state.default_data_loaded = False
            st.session_state.default_data_df = None

        # Create two columns for the buttons
        realtime_col, popup_col = st.columns(2)

        with realtime_col:
            # Use button to toggle default data usage
            if st.button("📊 Realtime Data", help="Realtime data processing and generation"):
                from openengage.core.user_data_processor import UserDataProcessor
                from openengage.core.data_integration import DataIntegration

                # Create a progress container and elements
                progress_container = st.container()
                with progress_container:
                    st.write("### Processing User Data")
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    # Update status for initialization
                    status_text.text("Initializing data refresh and processing...")
                    progress_bar.progress(5)
                    st.write("")  # Add some spacing

                # Define a callback function to update progress for both refresh and processing
                def progress_callback(stage, percentage):
                    # Ensure the progress bar is updated properly
                    with progress_container:
                        status_text.text(f"Processing: {stage}")
                        # For data refresh, scale from 5-50%
                        # For data processing, scale from 50-100%
                        if 'refresh' in stage.lower():
                            current_progress = int(5 + percentage * 0.45)  # Scale to 5-50%
                        else:
                            current_progress = int(50 + percentage * 0.5)  # Scale to 50-100%
                        progress_bar.progress(current_progress)
                        # Force a small sleep to ensure UI updates
                        import time
                        time.sleep(0.05)

                # First, refresh the data using DataIntegration
                with progress_container:
                    status_text.text("Refreshing data from external sources...")
                    progress_bar.progress(5)

                # Create data integration instance and refresh data
                data_integration = DataIntegration()

                # Define a separate callback for data refresh to properly scale progress
                def refresh_callback(stage, percentage):
                    progress_callback(f"Refreshing: {stage}", percentage)

                # Refresh the data
                data_integration.refresh_data(progress_callback=refresh_callback)

                with progress_container:
                    status_text.text("Data refresh completed. Starting data processing...")
                    progress_bar.progress(50)

                # Create processor and process data with progress updates
                user_data_processor = UserDataProcessor()

                # Run the data processing
                user_data_processor.generate_user_behaviour_data(progress_callback)

                # Final update
                with progress_container:
                    progress_bar.progress(100)
                    status_text.text("Data processing completed!")
                    st.success("User data has been processed and is ready for campaign generation")

                # Set session state to use the processed data
                st.session_state.use_default_data = True
                st.session_state.default_data_loaded = False  # Force reload of the new data

        with popup_col:
            # Popup generation button
            if st.button("🎯 Popup Generation", help="Generate personalized popup content using AI"):
                from openengage.core.popup_batch_generator import generate_popup_content_batch

                # Create a progress container and elements
                popup_progress_container = st.container()
                with popup_progress_container:
                    st.write("### Generating Popup Content")
                    popup_progress_bar = st.progress(0)
                    popup_status_text = st.empty()

                    # Update status for initialization
                    popup_status_text.text("Loading user data for popup generation...")
                    popup_progress_bar.progress(5)
                    st.write("")  # Add some spacing

                # Define a callback function to update progress
                def popup_progress_callback(current, total, message=""):
                    with popup_progress_container:
                        # Handle division by zero case
                        if total > 0:
                            progress_percent = int(min(current / total * 100, 100))
                            popup_progress_bar.progress(progress_percent)
                            popup_status_text.text(f"Processing: {current+1}/{total} users complete ({progress_percent}%) - {message}")
                        else:
                            # Handle case where total is 0 (e.g., during data loading)
                            popup_progress_bar.progress(0)
                            popup_status_text.text(f"{message}")
                        # Force a small sleep to ensure UI updates
                        import time
                        time.sleep(0.01)

                # Load user data for popup generation
                try:
                    # Generate popup content using the batch generator (it will automatically use user_popup_data.csv)
                    result_df = generate_popup_content_batch(progress_callback=popup_progress_callback)

                    # Final update
                    with popup_progress_container:
                        popup_progress_bar.progress(100)
                        popup_status_text.text(f"Completed popup generation for {len(result_df)} users!")
                        st.success("✅ Popup content has been successfully generated and saved to popup_text.csv")

                except Exception as e:
                    st.error(f"Error generating popup content: {str(e)}")

        # Show indicator when using default data
        if st.session_state.use_default_data:
            st.success("Using realtime user data")

        # Load default data if needed
        if st.session_state.use_default_data and not st.session_state.default_data_loaded:
            try:
                # Load the default user behavior data from user_popup_data.csv (primary source)
                default_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                                               'Sample Data For Mass Generation', 'user_popup_data.csv')

                # Fallback to processed_user_data.csv if user_popup_data.csv doesn't exist
                if not os.path.exists(default_data_path):
                    default_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                                                   'Sample Data For Mass Generation', 'processed_user_data.csv')

                df = pd.read_csv(default_data_path)

                # Store in session state
                st.session_state.default_data_df = df
                st.session_state.default_data_loaded = True
            except Exception as e:
                st.error(f"Error loading default user data: {str(e)}")
                st.session_state.use_default_data = False
                st.session_state.default_data_loaded = False
                return

    # Add session state variables for tracking campaign generation and sending
    if "campaign_generated" not in st.session_state:
        st.session_state.campaign_generated = False

    if "campaign_file_path" not in st.session_state:
        st.session_state.campaign_file_path = None

    if "send_campaign_clicked" not in st.session_state:
        st.session_state.send_campaign_clicked = False

    # Use either uploaded file or default data
    if uploaded_file is not None or st.session_state.use_default_data:
        try:
            # Get data from appropriate source
            if st.session_state.use_default_data:
                df = st.session_state.default_data_df
            else:
                df = pd.read_csv(uploaded_file)

            # Validate columns based on selected channel
            if st.session_state.selected_sending_channel == "Email":
                required_columns = ['user_email', 'first_name', 'user_behaviour', 'user_stage']
            else:  # WhatsApp
                required_columns = ['phone_number', 'first_name', 'user_behaviour', 'user_stage']

            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                st.error(f"Missing required columns for {st.session_state.selected_sending_channel} channel: {', '.join(missing_columns)}")

                # Show more detailed error message with expected format
                if st.session_state.selected_sending_channel == "Email":
                    st.error("Expected columns: user_email, first_name, user_behaviour, user_stage")
                else:
                    st.error("Expected columns: phone_number, first_name, user_behaviour, user_stage")
                return

            # Show data preview
            st.write("### Data Preview")
            st.dataframe(df.head())

            # Add option to use batch API
            use_batch_api = st.checkbox("Use OpenAI Batch API (Economical but slower for small userbase)", value=True,
                                       help="Uses OpenAI's Batch API for economical processing. Disable if you encounter issues.")

            # Process button - label changes based on selected channel
            button_label = f"Generate Mass {st.session_state.selected_sending_channel} Campaigns"
            if st.button(button_label, type="primary"):
                with st.spinner("Generating personalized campaigns..."):
                    # Create progress tracking elements
                    campaign_progress = st.container()
                    with campaign_progress:
                        st.write(f"### Generating {st.session_state.selected_sending_channel} Campaigns")
                        progress_bar = st.progress(0)
                        progress_text = st.empty()
                        st.write("")  # Add some spacing

                    # Define callback for progress updates
                    def update_progress(current, total, message=""):
                        with campaign_progress:
                            # Handle division by zero case
                            if total > 0:
                                progress_percent = int(min(current / total * 100, 100))
                                progress_bar.progress(progress_percent)
                                progress_text.text(f"Processing: {current+1}/{total} users complete ({progress_percent}%) - {message}")
                            else:
                                # Handle case where total is 0
                                progress_bar.progress(0)
                                progress_text.text(f"{message}")
                            # Force a small sleep to ensure UI updates
                            import time
                            time.sleep(0.01)

                    # Process data with progress tracking based on selected channel
                    if st.session_state.selected_sending_channel == "Email":
                        # Process email data
                        from openengage.core.mass_email_generator import process_mass_email_data
                        result_df = process_mass_email_data(df, progress_callback=update_progress, use_batch_api=use_batch_api)
                    else:
                        # Process WhatsApp data using the simplified generator
                        from openengage.core.simplified_whatsapp_generator import process_simplified_whatsapp_data
                        result_df = process_simplified_whatsapp_data(df, progress_callback=update_progress)

                    # Update UI with completion message
                    with campaign_progress:
                        progress_bar.progress(100)
                        progress_text.text(f"Completed processing {len(df)} users!")
                        st.success(f"✅ {st.session_state.selected_sending_channel} campaigns have been successfully generated")

                    # Process offer data if available
                    if offer_file is not None:
                        try:
                            # Read offer CSV
                            offers_df = pd.read_csv(offer_file)

                            # Validate offer columns
                            required_offer_columns = ['offer_start_time', 'offer_end_time', 'offer_details']
                            missing_offer_columns = [col for col in required_offer_columns if col not in offers_df.columns]

                            if not missing_offer_columns:
                                # Process offers
                                try:
                                    from openengage.core.offer_generator import process_offers_for_mass_campaign
                                    result_df = process_offers_for_mass_campaign(result_df, offers_df)
                                except Exception as e:
                                    st.error(f"Error processing offers: {str(e)}")
                                st.success("Offer details processed successfully!")
                            else:
                                st.warning(f"Offer file is missing required columns: {', '.join(missing_offer_columns)}. Offers will not be included.")
                        except Exception as e:
                            st.warning(f"Error processing offer details: {str(e)}. Campaigns will be generated without offers.")

                    # Save to CSV
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_filename = f"mass_campaign_{timestamp}.csv"

                    # Ensure the campaigns directory exists
                    campaigns_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "campaigns")
                    os.makedirs(campaigns_dir, exist_ok=True)
                    # Print path for debugging
                    print(f"Saving campaign to: {campaigns_dir}")

                    output_path = os.path.join(campaigns_dir, output_filename)
                    result_df.to_csv(output_path, index=False)

                    # Store the campaign file path in session state for sending later
                    st.session_state.campaign_file_path = output_path
                    st.session_state.campaign_generated = True

                    # Show results
                    st.write(f"### Generated {st.session_state.selected_sending_channel} Campaigns")
                    st.dataframe(result_df)

                    # Show HTML previews only for Email channel
                    if st.session_state.selected_sending_channel == "Email" and 'HTML_Content' in result_df.columns:
                        st.write("### HTML Email Previews")

                        # Create a scrollable container style for the HTML previews
                        st.markdown("""
                        <style>
                        .email-preview-container {
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            height: 600px;
                            overflow-y: auto;
                            background-color: white;
                            padding: 10px;
                            margin: 0;
                            width: 100%;
                        }
                        iframe {
                            width: 100%;
                            height: 100%;
                            border: none;
                        }
                        </style>
                        """, unsafe_allow_html=True)

                        for _, row in result_df.head(5).iterrows():
                            if row.get('HTML_Content'):
                                with st.expander(f"Preview: {row['first_name']} - {row['Subject']}"):
                                    # Wrap the HTML content in a scrollable div
                                    # Include offer HTML if available
                                    offer_html = row.get('Offer_HTML', '')

                                    # Insert offer HTML just above "Best Regards" if it exists
                                    if offer_html and row['HTML_Content']:
                                        # Common closing phrases to look for
                                        closing_phrases = [
                                            "Best Regards",
                                            "Best regards",
                                            "Regards",
                                            "regards",
                                            "Sincerely",
                                            "sincerely",
                                            "Thank you",
                                            "thank you",
                                            "Thanks",
                                            "thanks"
                                        ]

                                        html_content = row['HTML_Content']
                                        for phrase in closing_phrases:
                                            if phrase in html_content:
                                                html_content = html_content.replace(
                                                    phrase,
                                                    f"{offer_html}\n\n{phrase}"
                                                )
                                                break
                                        else:
                                            # If no closing phrase found, append at the end
                                            html_content += f"\n\n{offer_html}"
                                    else:
                                        html_content = row['HTML_Content']

                                    # Use st.components.v1.html instead of st.markdown for proper HTML rendering
                                    # Add proper HTML document structure to ensure correct rendering
                                    full_html = f"""
                                    <!DOCTYPE html>
                                    <html>
                                    <head>
                                        <meta charset="UTF-8">
                                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                        <style>
                                            body {{ margin: 0; padding: 10px; font-family: Arial, sans-serif; }}
                                            img {{ max-width: 100%; height: auto; }}
                                            table {{ width: 100%; border-collapse: collapse; }}
                                            td {{ padding: 8px; }}
                                        </style>
                                    </head>
                                    <body>
                                        {html_content}
                                    </body>
                                    </html>
                                    """

                                    st.components.v1.html(
                                        full_html,
                                        height=3000,  # Set a fixed height for the component
                                        scrolling=True  # Enable scrolling
                                    )

                                    # Also add a download button for this specific email
                                    st.download_button(
                                        label="Download this email as HTML",
                                        data=full_html,
                                        file_name=f"email_{row['first_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
                                        mime="text/html",
                                        key=f"download_email_html_{row['first_name']}_{_}"
                                    )

                    # Create download button for the generated campaigns
                    try:
                        with open(output_path, "rb") as file:
                            st.download_button(
                                label="Download Generated Campaigns",
                                data=file,
                                file_name=output_filename,
                                mime="text/csv",
                                key="download_generated_campaigns"
                            )
                    except Exception as e:
                        st.error(f"Error creating download button: {str(e)}")

                    # Add Send Campaign button - note that we've moved it out of this block in the code below

        except Exception as e:
            st.error(f"Error processing data: {str(e)}")
            import traceback
            st.error(f"Detailed error: {traceback.format_exc()}")

    # Campaign sending section - moved outside the campaign generation block
    # This ensures it persists across reruns
    if st.session_state.campaign_generated:
        st.divider()
        st.write(f"### 🚀 {st.session_state.selected_sending_channel} Campaign Actions")

        # Initialize session state for schedule button
        if "schedule_campaign_clicked" not in st.session_state:
            st.session_state.schedule_campaign_clicked = False

        # Create two columns for the buttons
        send_col1, send_col2 = st.columns(2)

        # Execute the sending logic if the Send Now button was clicked
        with send_col1:
            # Change button label based on selected channel
            send_button_label = "Send Emails Now" if st.session_state.selected_sending_channel == "Email" else "Send WhatsApp Messages One by One"

            if st.button(send_button_label, type="primary"):
                try:
                    st.session_state.send_campaign_clicked = True

                    # Use the stored campaign path from session state if available
                    if st.session_state.campaign_file_path and os.path.exists(st.session_state.campaign_file_path):
                        campaign_path = st.session_state.campaign_file_path
                    else:
                        # Fallback to finding the latest campaign file
                        campaign_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "campaigns")

                        campaign_files = [f for f in os.listdir(campaign_dir) if f.endswith('.csv')]
                        if not campaign_files:
                            st.error("No campaign files found in data/campaigns directory")
                            return

                        # Get the latest file based on modification time
                        latest_file = max(
                            campaign_files,
                            key=lambda f: os.path.getmtime(os.path.join(campaign_dir, f))
                        )
                        campaign_path = os.path.join(campaign_dir, latest_file)

                    # Read the campaign data
                    campaign_df = pd.read_csv(campaign_path)

                    # Check which channel to use
                    if st.session_state.selected_sending_channel == "Email":
                        # Email sending logic
                        # Import required components
                        from openengage.core.email_sender import create_email_sender
                        from openengage.ui.sending_config import get_esp_api_keys, get_sender_details

                        # Get API keys and sender details
                        api_keys = get_esp_api_keys()
                        sender_details = get_sender_details()

                        st.write(f"Got API keys and sender details")

                        # Find active email sender configuration
                        active_config = None
                        for config in sender_details:
                            if config.get("channel") == "Email" and config.get("active"):
                                active_config = config
                                break

                        if not active_config:
                            st.error("No active email sender configuration found. Please configure one in the Sending Configuration.")
                            return

                        st.write(f"Using ESP: {active_config.get('esp')}")

                        # Get the appropriate API key
                        esp = active_config.get("esp")
                        api_key = None

                        if esp == "SparkPost":
                            api_key = api_keys.get("sparkpost")
                        elif esp == "Mailmodo":
                            api_key = api_keys.get("mailmodo")
                        elif esp == "Amazon SES":
                            api_key = api_keys.get("amazon_ses")

                        if not api_key:
                            st.error(f"No API key found for {esp}. Please configure it in the Sending Configuration.")
                            return

                        # Create email sender
                        sender = create_email_sender(esp.lower().replace(" ", "_"), api_key)

                        # Set sender details
                        sender.set_sender_details(
                            active_config.get("sender_name"),
                            active_config.get("sender_email"),
                            active_config.get("reply_to_email")
                        )

                        st.write("Sending email campaign...")

                        # Send the campaign
                        with st.spinner(f"Sending email campaign via {esp}..."):
                            results = sender.send_emails(campaign_df)

                            if results.get("errors"):
                                st.error(f"Encountered {len(results['errors'])} errors while sending campaign")
                                for error in results["errors"]:
                                    st.error(error)
                            else:
                                st.success(f"Successfully sent email campaign to {results['total_accepted']} recipients!")

                    else:
                        # WhatsApp sending logic using simplified generator
                        from openengage.core.simplified_whatsapp_generator import send_whatsapp_messages_one_by_one

                        # Send the campaign
                        with st.spinner("Sending WhatsApp messages..."):
                            # Create progress elements for sending
                            send_progress_bar = st.progress(0)
                            send_progress_text = st.empty()

                            # Define progress callback for sending
                            def send_progress(current, total, message):
                                progress = current / total
                                send_progress_bar.progress(progress)
                                send_progress_text.text(message)

                            # Send the campaign using our one-by-one sender
                            results = send_whatsapp_messages_one_by_one(
                                campaign_df=campaign_df,
                                progress_callback=send_progress
                            )

                            # Store results in session state
                            st.session_state.results = results

                            # Display results
                            if results.get("success") == False:
                                st.error(f"Failed to send WhatsApp campaign: {results.get('error', 'Unknown error')}")
                            elif results.get("errors"):
                                st.error(f"Encountered {len(results['errors'])} errors while sending campaign")
                                for error in results["errors"]:
                                    st.error(error)
                            else:
                                st.success(f"Successfully sent WhatsApp campaign to {results.get('total_accepted', 0)} recipients!")

                        # Save results to CSV in campaign_results folder if any messages were sent
                        if st.session_state.get('results') and "message_data" in st.session_state.results and not st.session_state.results["message_data"].empty:
                            # Create timestamp for the filename
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            results_filename = f"whatsapp_results_{timestamp}.csv"

                            # Create campaign_results directory
                            project_root = os.path.join(os.path.dirname(__file__), "..", "..", "..")
                            results_dir = os.path.join(project_root, "data", "campaign_results")
                            os.makedirs(results_dir, exist_ok=True)

                            # Save results to file
                            results_path = os.path.join(results_dir, results_filename)
                            st.session_state.results["message_data"].to_csv(results_path, index=False)

                            # Log the save location
                            st.info(f"Campaign results saved to: {results_path}")

                            # Provide download button
                            csv = st.session_state.results["message_data"].to_csv(index=False)
                            st.download_button(
                                label="Download WhatsApp Campaign Results",
                                data=csv,
                                file_name=results_filename,
                                mime="text/csv",
                                key="download_whatsapp_results"
                            )

                            # Delete the used campaign file
                            try:
                                if os.path.exists(campaign_path) and campaign_path.endswith(".csv") and "mass_campaign" in os.path.basename(campaign_path):
                                    os.remove(campaign_path)
                                    st.success(f"Used campaign file deleted: {os.path.basename(campaign_path)}")
                            except Exception as e:
                                st.warning(f"Could not delete campaign file: {str(e)}")
                    # Reset the state for next time
                    st.session_state.send_campaign_clicked = False
                except Exception as e:
                    st.error(f"Error sending campaign: {str(e)}")
                    import traceback
                    st.error(traceback.format_exc())

        # Execute the scheduling logic if the Schedule Mails button was clicked
        with send_col2:
            # Only show scheduling option for Email channel
            if st.session_state.selected_sending_channel == "Email":
                schedule_button_label = "Schedule Mails"

                if st.button(schedule_button_label, type="secondary"):
                    try:
                        st.session_state.schedule_campaign_clicked = True

                        # Use the stored campaign path from session state if available
                        if st.session_state.campaign_file_path and os.path.exists(st.session_state.campaign_file_path):
                            campaign_path = st.session_state.campaign_file_path
                            campaign_filename = os.path.basename(campaign_path)
                            st.write(f"Using campaign file: {campaign_filename}")
                        else:
                            # Fallback to finding the latest campaign file
                            campaign_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "campaigns")
                            st.write(f"Looking for campaigns in: {campaign_dir}")

                            campaign_files = [f for f in os.listdir(campaign_dir) if f.endswith('.csv')]
                            if not campaign_files:
                                st.error("No campaign files found in data/campaigns directory")
                                return

                            st.write(f"Found {len(campaign_files)} campaign files")

                            # Get the latest file based on modification time
                            latest_file = max(
                                campaign_files,
                                key=lambda f: os.path.getmtime(os.path.join(campaign_dir, f))
                            )
                            campaign_path = os.path.join(campaign_dir, latest_file)
                            st.write(f"Latest campaign file: {latest_file}")

                        # Read the campaign data
                        campaign_df = pd.read_csv(campaign_path)
                        st.write(f"Loaded campaign with {len(campaign_df)} rows")

                        # Check if Send_Time column exists
                        if 'Send_Time' not in campaign_df.columns:
                            st.error("The campaign data does not have a Send_Time column. Please process the data with time configuration settings.")
                            return

                        # Import required components
                        from openengage.core.email_sender import create_email_sender
                        from openengage.ui.sending_config import get_esp_api_keys, get_sender_details

                        # Get API keys and sender details
                        api_keys = get_esp_api_keys()
                        sender_details = get_sender_details()

                        st.write(f"Got API keys and sender details")

                        # Find active email sender configuration
                        active_config = None
                        for config in sender_details:
                            if config.get("channel") == "Email" and config.get("active"):
                                active_config = config
                                break

                        if not active_config:
                            st.error("No active email sender configuration found. Please configure one in the Sending Configuration.")
                            return

                        st.write(f"Using ESP: {active_config.get('esp')}")

                        # Get the appropriate API key
                        esp = active_config.get("esp")
                        api_key = None

                        if esp == "SparkPost":
                            api_key = api_keys.get("sparkpost")
                        elif esp == "Mailmodo":
                            api_key = api_keys.get("mailmodo")
                        elif esp == "Amazon SES":
                            api_key = api_keys.get("amazon_ses")

                        if not api_key:
                            st.error(f"No API key found for {esp}. Please configure it in the Sending Configuration.")
                            return

                        # Create email sender
                        sender = create_email_sender(esp.lower().replace(" ", "_"), api_key)

                        # Set sender details
                        sender.set_sender_details(
                            active_config.get("sender_name"),
                            active_config.get("sender_email"),
                            active_config.get("reply_to_email")
                        )

                        st.write("Scheduling campaign...")

                        # Schedule the campaign
                        with st.spinner(f"Scheduling campaign via {esp}..."):
                            results = sender.schedule_emails(campaign_df)

                            if "email_data" in results:
                                    # Create timestamp for the filename
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    results_filename = f"scheduled_campaign_results_{timestamp}.csv"

                                    # Create campaign_results directory
                                    project_root = os.path.join(os.path.dirname(__file__), "..", "..", "..")
                                    results_dir = os.path.join(project_root, "data", "campaign_results")
                                    os.makedirs(results_dir, exist_ok=True)

                                    # Save results to file
                                    results_path = os.path.join(results_dir, results_filename)
                                    results["email_data"].to_csv(results_path, index=False)

                                    # Log the save location
                                    st.info(f"Scheduled campaign results saved to: {results_path}")

                                    # Provide download button
                                    csv = results["email_data"].to_csv(index=False)
                                    st.download_button(
                                        label="Download Scheduled Campaign Results",
                                        data=csv,
                                        file_name=results_filename,
                                        mime="text/csv",
                                        key="download_scheduled_campaign_results"
                                    )

                            if results.get("errors"):
                                st.error(f"Encountered {len(results['errors'])} errors while scheduling campaign")
                                for error in results["errors"]:
                                    st.error(error)
                            else:
                                st.success(f"Successfully scheduled campaign for {results['total_accepted']} recipients!")
                                # Delete the used campaign file
                                try:
                                    if os.path.exists(campaign_path) and campaign_path.endswith(".csv") and "mass_campaign" in os.path.basename(campaign_path):
                                        os.remove(campaign_path)
                                        st.success(f"Used campaign file deleted: {os.path.basename(campaign_path)}")
                                except Exception as e:
                                    st.warning(f"Could not delete campaign file: {str(e)}")
                        # Reset the state for next time
                        st.session_state.schedule_campaign_clicked = False
                    except Exception as e:
                        st.error(f"Error scheduling campaign: {str(e)}")
                        import traceback
                        st.error(traceback.format_exc())
