"""
Trend analysis UI component for OpenEngage.
"""
import streamlit as st
import pandas as pd
import altair as alt
from typing import Dict, Any

from utils.file_utils import get_all_products, save_custom_keywords, load_custom_keywords
from core.trend_analyzer import get_trend_data_for_product

def display_trend_analysis():
    """Display the trend analysis dashboard"""
    st.title("📈 Trend Analysis")
    st.write("Analyze trends and news related to your product keywords.")

    # Get all products
    all_products = get_all_products()
    product_names = [p.get("Product_Name", "") for p in all_products if p.get("Product_Name")]

    if not product_names:
        st.warning("No products found. Please add products first.")
        return

    # Initialize session state for selected product if not already done
    if "trend_analysis_product" not in st.session_state:
        st.session_state.trend_analysis_product = product_names[0] if product_names else None

    # Product selector
    selected_product = st.selectbox(
        "Select a product to analyze:",
        options=product_names,
        index=product_names.index(st.session_state.trend_analysis_product) if st.session_state.trend_analysis_product in product_names else 0
    )

    # Update session state if product selection changed
    if selected_product != st.session_state.trend_analysis_product:
        st.session_state.trend_analysis_product = selected_product
        # Clear any cached data
        if "trend_data" in st.session_state:
            del st.session_state.trend_data
        # Reset edit mode
        st.session_state.edit_keywords_mode = False
        # Clear keyword editor
        if "keyword_editor" in st.session_state:
            del st.session_state.keyword_editor

    # Get trend data for the selected product
    if "trend_data" not in st.session_state or st.button("Refresh Data", type="primary"):
        with st.spinner("Fetching trend data..."):
            st.session_state.trend_data = get_trend_data_for_product(selected_product)

    # Display the trend analysis
    if "trend_data" in st.session_state:
        display_trend_results(st.session_state.trend_data, selected_product)

def display_trend_results(trend_data: Dict[str, Any], selected_product: str):
    """
    Display trend analysis results.

    Args:
        trend_data: Dictionary containing trend analysis data
        selected_product: Name of the selected product
    """
    keywords = trend_data.get('keywords', [])
    extracted_keywords = trend_data.get('extracted_keywords', [])
    trend_data_dict = trend_data.get('trend_data', {})
    related_queries = trend_data.get('related_queries', {})
    related_topics = trend_data.get('related_topics', {})
    news = trend_data.get('news', [])

    if not keywords:
        st.warning("No keywords found for this product.")
        return

    # Display keywords with edit button
    col1, col2 = st.columns([3, 1])
    with col1:
        st.subheader("Keywords")
    with col2:
        edit_keywords = st.button("✏️ Edit Keywords", type="secondary")

    # Check if we're in edit mode
    if "edit_keywords_mode" not in st.session_state:
        st.session_state.edit_keywords_mode = False

    # Toggle edit mode if button clicked
    if edit_keywords:
        st.session_state.edit_keywords_mode = not st.session_state.edit_keywords_mode

    # Initialize keyword editor state if needed
    if "keyword_editor" not in st.session_state:
        st.session_state.keyword_editor = ", ".join(keywords)

    # Show either the keyword editor or the keyword pills
    if st.session_state.edit_keywords_mode:
        st.write("Edit the keywords below (comma-separated):")

        # Show the extracted keywords as a reference
        if extracted_keywords:
            st.write("Extracted keywords from product data:")
            extracted_html = ""
            for keyword in extracted_keywords:
                extracted_html += f'<span style="background-color: #e0e0e0; color: #333; padding: 0.2rem 0.5rem; margin-right: 0.5rem; border-radius: 1rem; font-size: 0.8rem;">{keyword}</span>'
            st.markdown(f'<div style="margin-bottom: 1rem;">{extracted_html}</div>', unsafe_allow_html=True)

        # Add a button to try popular keywords
        if st.button("🔍 Try Popular Keywords", help="Use general keywords that are likely to have trend data"):
            popular_keywords = [
                "digital marketing",
                "technology trends",
                "software tools",
                "business solutions",
                "productivity"
            ]

            # Get the product type if available
            product_type = ""
            if extracted_keywords and len(extracted_keywords) > 1:
                product_type = extracted_keywords[1]  # Often the second extracted keyword is the product type

            if product_type:
                # Add product type related keywords
                popular_keywords = [
                    product_type,
                    f"{product_type} trends",
                    "technology",
                    "innovation",
                    "digital transformation"
                ]

            # Update the keyword editor
            st.session_state.keyword_editor = ", ".join(popular_keywords)
            st.rerun()

        # Keyword editor
        new_keywords = st.text_area("Custom Keywords",
                                   value=st.session_state.keyword_editor,
                                   height=100,
                                   help="Enter keywords separated by commas")

        # Save, reset, and cancel buttons
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("💾 Save Keywords", type="primary"):
                # Process the keywords
                processed_keywords = [k.strip() for k in new_keywords.split(",") if k.strip()]

                if processed_keywords:
                    # Save to file
                    save_custom_keywords(selected_product, processed_keywords)

                    # Update session state
                    st.session_state.keyword_editor = ", ".join(processed_keywords)
                    st.session_state.edit_keywords_mode = False

                    # Clear cached data to force refresh
                    if "trend_data" in st.session_state:
                        del st.session_state.trend_data

                    st.success("Keywords saved successfully!")
                    st.rerun()
                else:
                    st.error("Please enter at least one keyword")

        with col2:
            if st.button("🔄 Reset to Default", type="secondary", help="Reset to automatically extracted keywords"):
                # Remove custom keywords for this product by saving an empty list
                save_custom_keywords(selected_product, [])

                # Update session state with extracted keywords
                st.session_state.keyword_editor = ", ".join(extracted_keywords)

                # Clear cached data to force refresh
                if "trend_data" in st.session_state:
                    del st.session_state.trend_data

                st.success("Reset to default keywords!")
                st.rerun()

        with col3:
            if st.button("❌ Cancel", type="secondary"):
                st.session_state.edit_keywords_mode = False
                st.rerun()
    else:
        # Display keywords as pills
        st.write("The following keywords are being used for analysis:")
        keyword_html = ""
        for keyword in keywords:
            keyword_html += f'<span style="background-color: #8D06FE; color: white; padding: 0.3rem 0.6rem; margin-right: 0.5rem; border-radius: 1rem; font-size: 0.9rem;">{keyword}</span>'

        st.markdown(f'<div style="margin-bottom: 1.5rem;">{keyword_html}</div>', unsafe_allow_html=True)

        # Show a note if using custom keywords
        custom_keywords = load_custom_keywords().get(selected_product, [])
        if custom_keywords:
            st.info("You are using custom keywords. Click 'Edit Keywords' to modify them.")
        else:
            st.info("These keywords were automatically extracted from the product data. Click 'Edit Keywords' to customize them.")

    # Display trend chart header
    st.subheader("Trend Analysis (Last 3 Months)")

    # Check if trend data is available
    has_data = bool(trend_data_dict) and any(isinstance(df, pd.DataFrame) and not df.empty for df in trend_data_dict.values())

    if not has_data:
        st.warning("No trend data available for the selected keywords.")
        st.info("This could be due to one of the following reasons:")
        st.markdown("""
        - The keywords may be too specific or have very low search volume
        - There might be a temporary issue with the Google Trends website
        - Try using more general keywords or fewer keywords
        """)

        # Show suggestions for better keywords
        st.markdown("### Try using more general keywords")
        st.write("Edit your keywords to use more general terms that might have more search data available.")

        # Display the current keywords
        if keywords:
            st.write("Current keywords:")
            keyword_html = ""
            for keyword in keywords:
                keyword_html += f'<span style="background-color: #e0e0e0; color: #333; padding: 0.3rem 0.6rem; margin-right: 0.5rem; border-radius: 1rem; font-size: 0.9rem;">{keyword}</span>'
            st.markdown(f'<div style="margin-bottom: 1.5rem;">{keyword_html}</div>', unsafe_allow_html=True)

            # Add a button to edit keywords
            if st.button("✏️ Edit Keywords Now", type="primary"):
                st.session_state.edit_keywords_mode = True
                st.rerun()

        # Show a placeholder chart with a message
        placeholder_df = pd.DataFrame({
            'date': pd.date_range(start='today', periods=26, freq='W'),
            'value': [0] * 26
        })

        placeholder_chart = alt.Chart(placeholder_df).mark_line().encode(
            x=alt.X('date:T', title='Date'),
            y=alt.Y('value:Q', title='Search Interest', scale=alt.Scale(domain=[0, 100]))
        ).properties(
            height=300
        ).configure_axis(
            grid=False
        )

        st.altair_chart(placeholder_chart, use_container_width=True)

        # Suggest some popular keywords
        st.subheader("Suggested Keywords")
        st.write("Try using some of these more general keywords:")

        # Get the product type or category
        product_type = ""
        if "trend_data" in st.session_state:
            extracted_keywords = st.session_state.trend_data.get('extracted_keywords', [])
            if extracted_keywords and len(extracted_keywords) > 1:
                product_type = extracted_keywords[1]  # Often the second extracted keyword is the product type

        # Suggest general keywords based on product type
        if product_type:
            suggestions = [
                product_type,
                f"best {product_type}",
                f"{product_type} trends",
                f"{product_type} technology",
                f"new {product_type}"
            ]
        else:
            suggestions = [
                "digital marketing",
                "technology trends",
                "software tools",
                "business solutions",
                "productivity tools"
            ]

        # Display suggestions as clickable buttons
        st.write("Click on a suggestion to use it:")

        # Create buttons for each suggestion
        for i, suggestion in enumerate(suggestions):
            col1, col2 = st.columns([3, 1])
            with col1:
                if st.button(f"Use: {suggestion}", key=f"suggestion_{i}", use_container_width=True):
                    # Set edit mode to true
                    st.session_state.edit_keywords_mode = True
                    # Update the keyword editor with this suggestion
                    st.session_state.keyword_editor = suggestion
                    st.rerun()
            with col2:
                if st.button(f"Add", key=f"add_suggestion_{i}", use_container_width=True):
                    # Set edit mode to true
                    st.session_state.edit_keywords_mode = True
                    # Add this suggestion to existing keywords
                    current_keywords = st.session_state.keyword_editor.split(",") if "keyword_editor" in st.session_state else []
                    current_keywords = [k.strip() for k in current_keywords if k.strip()]
                    if suggestion not in current_keywords:
                        current_keywords.append(suggestion)
                    st.session_state.keyword_editor = ", ".join(current_keywords)
                    st.rerun()

    else:
        # Create tabs for each keyword
        if trend_data_dict and len(trend_data_dict) > 1:
            # Create tabs for each keyword
            tabs = st.tabs(list(trend_data_dict.keys()))

            # Display trend data for each keyword in its own tab
            for i, (keyword, df) in enumerate(trend_data_dict.items()):
                with tabs[i]:
                    # Display the keyword as a header
                    st.markdown(f"#### {keyword}")

                    # Reset index to make date a column
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        chart_df = df.reset_index()

                        # Create the chart with enhanced styling
                        chart = alt.Chart(chart_df).mark_line(
                            point=alt.OverlayMarkDef(color="#8D06FE", size=60),
                            color="#8D06FE",
                            strokeWidth=3
                        ).encode(
                            x=alt.X('date:T', title='Date', axis=alt.Axis(labelAngle=-45, format='%b %d, %Y')),
                            y=alt.Y(f'{keyword}:Q', title='Search Interest', scale=alt.Scale(domain=[0, 100])),
                            tooltip=[
                                alt.Tooltip('date:T', title='Date', format='%b %d, %Y'),
                                alt.Tooltip(f'{keyword}:Q', title='Interest', format='.1f')
                            ]
                        ).properties(
                            height=350
                        ).interactive()

                        st.altair_chart(chart, use_container_width=True)
                    else:
                        st.info(f"No trend data available for '{keyword}'")
        else:
            # If there's only one keyword or no keywords, display without tabs
            keyword = list(trend_data_dict.keys())[0] if trend_data_dict and len(trend_data_dict) > 0 else ""
            df = list(trend_data_dict.values())[0] if trend_data_dict and len(trend_data_dict) > 0 else pd.DataFrame()

            # Display the keyword as a header if it exists
            if keyword:
                st.markdown(f"#### {keyword}")

            # Reset index to make date a column
            if isinstance(df, pd.DataFrame) and not df.empty:
                chart_df = df.reset_index()

                # Create the chart with enhanced styling
                chart = alt.Chart(chart_df).mark_line(
                    point=alt.OverlayMarkDef(color="#8D06FE", size=60),
                    color="#8D06FE",
                    strokeWidth=3
                ).encode(
                    x=alt.X('date:T', title='Date', axis=alt.Axis(labelAngle=-45, format='%b %d, %Y')),
                    y=alt.Y(f'{keyword}:Q', title='Search Interest', scale=alt.Scale(domain=[0, 100])),
                    tooltip=[
                        alt.Tooltip('date:T', title='Date', format='%b %d, %Y'),
                        alt.Tooltip(f'{keyword}:Q', title='Interest', format='.1f')
                    ]
                ).properties(
                    height=400
                ).interactive()

                st.altair_chart(chart, use_container_width=True)
            else:
                st.info(f"No trend data available for '{keyword}'")

        # Add explanation
        st.info("The chart shows the relative search interest for each keyword over time. Values are normalized to 100, where 100 is the maximum search interest for the time period.")

    # Display rising related queries and topics
    st.subheader("Rising Search Trends")

    # Check if we have any keywords with related data
    has_rising_data = False

    # Create tabs for each keyword
    if keywords and related_queries:
        keyword_tabs = st.tabs(keywords)

        for i, keyword in enumerate(keywords):
            with keyword_tabs[i]:
                # Get rising queries and topics for this keyword
                keyword_queries = related_queries.get(keyword, {}).get('rising', [])
                keyword_topics = related_topics.get(keyword, {}).get('rising', [])

                # Display a header for this keyword's trends
                st.markdown(f"#### Trends for '{keyword}'")

                # Check if we have any rising data for this keyword
                if not keyword_queries and not keyword_topics:
                    st.warning(f"No rising search trends available for '{keyword}'.")
                    continue

                has_rising_data = True

                # Create tabs for queries and topics
                query_topic_tabs = st.tabs(["Rising Queries", "Rising Topics"])

                with query_topic_tabs[0]:
                    if not keyword_queries:
                        st.info("No rising queries available. Check the Rising Topics tab.")
                    else:
                        st.write(f"These are trending searches related to '{keyword}':")

                        # Display top 5 rising queries
                        rising_df = pd.DataFrame(keyword_queries[:5])

                        if isinstance(rising_df, pd.DataFrame) and not rising_df.empty and 'query' in rising_df.columns and 'value' in rising_df.columns:
                            # Create a horizontal bar chart with enhanced styling
                            chart = alt.Chart(rising_df).mark_bar(
                                cornerRadius=5
                            ).encode(
                                y=alt.Y('query:N', title='Query', sort='-x', axis=alt.Axis(labelLimit=200)),
                                x=alt.X('value:Q', title='Growth (%)', scale=alt.Scale(domain=[0, rising_df['value'].max() * 1.1])),
                                color=alt.value('#8D06FE'),
                                tooltip=[
                                    alt.Tooltip('query:N', title='Query'),
                                    alt.Tooltip('value:Q', title='Growth', format='+.1f%')
                                ]
                            ).properties(
                                height=min(200, len(rising_df) * 40)
                            )

                            st.altair_chart(chart, use_container_width=True)

                            # Add explanation
                            st.info("These are trending searches related to your keyword, showing percentage growth compared to the previous period.")
                        else:
                            # Fallback to table display
                            st.dataframe(rising_df, use_container_width=True)

                with query_topic_tabs[1]:
                    if not keyword_topics:
                        st.info("No rising topics available. Check the Rising Queries tab.")
                    else:
                        st.write(f"These are trending topics related to '{keyword}':")

                        # Display top 5 rising topics
                        rising_topics_df = pd.DataFrame(keyword_topics[:5])

                        if isinstance(rising_topics_df, pd.DataFrame) and not rising_topics_df.empty and 'value' in rising_topics_df.columns:
                            # Check if we have 'topic_title' or 'topic_type' columns
                            title_col = 'topic_title' if 'topic_title' in rising_topics_df.columns else 'value'

                            # Create a horizontal bar chart with enhanced styling
                            chart = alt.Chart(rising_topics_df).mark_bar(
                                cornerRadius=5
                            ).encode(
                                y=alt.Y(f'{title_col}:N', title='Topic', sort='-x', axis=alt.Axis(labelLimit=200)),
                                x=alt.X('value:Q', title='Growth (%)', scale=alt.Scale(domain=[0, rising_topics_df['value'].max() * 1.1])),
                                color=alt.value('#EA4335'),
                                tooltip=[
                                    alt.Tooltip(f'{title_col}:N', title='Topic'),
                                    alt.Tooltip('value:Q', title='Growth', format='+.1f%')
                                ]
                            ).properties(
                                height=min(200, len(rising_topics_df) * 40)
                            )

                            st.altair_chart(chart, use_container_width=True)

                            # Add explanation
                            st.info("These are trending topics related to your keyword, showing percentage growth compared to the previous period.")
                        else:
                            # Fallback to table display
                            st.dataframe(rising_topics_df, use_container_width=True)

    # If no rising data was found for any keyword
    if not has_rising_data:
        st.warning("No rising search trends available for any of the keywords.")

        # Show some general information about rising trends
        if has_data:
            st.info("Try using more popular keywords to see rising search trends.")

        # Add a button to edit keywords
        if st.button("✏️ Edit Keywords", key="edit_keywords_rising", type="primary"):
            st.session_state.edit_keywords_mode = True
            st.rerun()

    # Display recent news
    st.subheader(f"Latest News Related to Product Keywords")

    if not news:
        with st.spinner("Searching for latest news..."):
            # This gives the impression we're actively searching rather than having no results
            st.info("No recent news found for the selected keywords. Try refreshing or selecting a different product.")
    else:
        # Display news in a more visually appealing grid
        # Create rows of 2 columns for news articles
        for i in range(0, len(news), 2):
            cols = st.columns(2)

            # First article in this row
            with cols[0]:
                article = news[i]
                st.markdown(f"""
                <div style="border: 1px solid #ddd; border-radius: 8px; padding: 1.2rem; margin-bottom: 1rem; height: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <h4 style="margin-top: 0; color: #8D06FE;">{article.get('title', 'No title')}</h4>
                    <p style="color: #666; font-size: 0.8rem; margin-bottom: 0.8rem;">
                        <span style="background-color: #f0f0f0; padding: 0.2rem 0.5rem; border-radius: 4px;">
                            Source: {article.get('source', 'Unknown')}
                        </span>
                    </p>
                    <p style="font-size: 0.95rem;">{article.get('description', 'No description available.')}</p>
                    <a href="{article.get('url', '#')}" target="_blank" style="display: inline-block; background-color: #8D06FE; color: white; padding: 0.4rem 0.8rem; border-radius: 4px; text-decoration: none; margin-top: 0.5rem;">Read full article</a>
                </div>
                """, unsafe_allow_html=True)

            # Second article in this row (if available)
            if i + 1 < len(news):
                with cols[1]:
                    article = news[i + 1]
                    st.markdown(f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 1.2rem; margin-bottom: 1rem; height: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <h4 style="margin-top: 0; color: #8D06FE;">{article.get('title', 'No title')}</h4>
                        <p style="color: #666; font-size: 0.8rem; margin-bottom: 0.8rem;">
                            <span style="background-color: #f0f0f0; padding: 0.2rem 0.5rem; border-radius: 4px;">
                                Source: {article.get('source', 'Unknown')}
                            </span>
                        </p>
                        <p style="font-size: 0.95rem;">{article.get('description', 'No description available.')}</p>
                        <a href="{article.get('url', '#')}" target="_blank" style="display: inline-block; background-color: #8D06FE; color: white; padding: 0.4rem 0.8rem; border-radius: 4px; text-decoration: none; margin-top: 0.5rem;">Read full article</a>
                    </div>
                    """, unsafe_allow_html=True)


