"""
Fully personalized email generator UI component for OpenEngage.
This module provides a UI for generating emails without templates, focusing on complete personalization.
"""
import os
import streamlit as st
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Try to import pandas
try:
    import pandas as pd
except ImportError:
    st.error("Pandas module not found. Please install it with 'pip install pandas'")

# Import for relative imports
import sys
from pathlib import Path

# Add the src directory to the Python path if needed
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.append(str(src_path))

def display_personalized_email_generator():
    """Display the fully personalized email generator interface"""

    # Initialize session state for default data usage
    if 'use_default_data' not in st.session_state:
        st.session_state.use_default_data = False

    if 'default_data_loaded' not in st.session_state:
        st.session_state.default_data_loaded = False
        st.session_state.default_data_df = None

    # Create a layout with two columns - title on left, channel selector on right
    title_col, channel_col = st.columns([3, 1])

    with title_col:
        st.write("## 🎯 Fully Personalized Email Generator")
        st.write("Generate completely personalized emails without templates, tailored to each user's specific behavior and needs.")

    with channel_col:
        # Initialize session state for selected channel if not already set
        if 'selected_sending_channel' not in st.session_state:
            st.session_state.selected_sending_channel = "Email"

        # Add a selectbox for channel selection (only Email for now)
        st.write("### 📤 Sending Channel")
        st.info("📧 Using Email channel")

    # Show sample format
    with st.expander("📝 Required CSV Format"):
        st.write("""
        ### Email Channel Format

        Your CSV file must have these exact columns:
        - **user_email**: Email address of the recipient
        - **first_name**: First name of the recipient
        - **user_behaviour**: Description of user's behavior or preferences
        - **user_stage**: User journey stage

        Sample CSV Format:
        ```csv
        user_email,first_name,user_behaviour,user_stage
        <EMAIL>,John,Viewed product page 3 times,New
        <EMAIL>,Jane,Added to cart but didn't purchase,Product Viewed
        ```
        """)

    # Show offer details format
    with st.expander("🎁 Optional Offer Details Format"):
        st.write("""
        ### Email Offer Details

        If you want to include special offers in your emails, you can upload a CSV file with offer details.
        The CSV file should have these columns:
        - **offer_start_time**: Start time of the offer in ISO format (e.g., '2023-06-01T00:00:00')
        - **offer_end_time**: End time of the offer in ISO format (e.g., '2023-06-30T23:59:59')
        - **offer_details**: Description of the offer (e.g., '20% discount', 'Buy one get one free')

        Sample Offer CSV Format:
        ```csv
        offer_start_time,offer_end_time,offer_details
        2023-06-01T00:00:00,2023-06-03T23:59:59,20% discount on all products
        2023-06-15T00:00:00,2023-06-22T23:59:59,Free shipping on orders over $50
        ```

        The system will apply the first valid offer (where current time is between start and end time) to all users.
        All times should be in UTC format.
        """)

    # Add model selection section
    st.write("### 🤖 AI Model Selection")

    # Check available models
    available_models = []

    # Check OpenAI models
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if openai_api_key:
        available_models.extend([
            "gpt-4.1",
            "gpt-4.1-mini",
            "gpt-4o",
            "gpt-4o-mini",
            "o4-mini-2025-04-16",
            "gpt-3.5-turbo",
            "ft:gpt-4o-mini-2024-07-18:analytics-vidhya:personalizedmailgeneratorendtoend:BfREaoS1"
        ])

    # Check Claude models
    claude_api_key = os.getenv("ANTHROPIC_API_KEY")
    if claude_api_key:
        available_models.extend([
            "claude-opus-4-20250514",      # Claude Opus 4 - Most capable model
            "claude-sonnet-4-20250514",    # Claude Sonnet 4 - High-performance model
            "claude-3-7-sonnet-20250219",  # Claude Sonnet 3.7 - High-performance with extended thinking
            "claude-3-5-sonnet-20241022",  # Claude Sonnet 3.5 v2 - Previous intelligent model
            "claude-3-5-haiku-20241022",   # Claude Haiku 3.5 - Fastest model
            "claude-3-opus-20240229",      # Claude Opus 3 - Powerful for complex tasks
            "claude-3-haiku-20240307"      # Claude Haiku 3 - Fast and compact
        ])

    # Add crew fallback option
    available_models.append("crew-agent")

    if not available_models:
        st.error("No AI models available. Please configure OpenAI or Claude API keys.")
        return

    # Model selection dropdown
    selected_model = st.selectbox(
        "Select AI Model for Email Generation:",
        options=available_models,
        index=0 if available_models else None,
        help="Choose the AI model to generate personalized emails. Different models may produce different writing styles and quality."
    )

    # Show model information and connection test
    if selected_model:
        if selected_model.startswith("gpt"):
            st.info(f"🤖 Using OpenAI model: {selected_model}")
        elif selected_model.startswith("claude"):
            st.info(f"🤖 Using Claude model: {selected_model}")
            st.info("✨ Claude responses are automatically cleaned to remove structural headings (HOOK, PITCH, TRUST, etc.)")
        elif selected_model == "crew-agent":
            st.info("🤖 Using internal crew agent for email generation")

    # Add connection test button
    if st.button("🔍 Test AI Connections", help="Test connectivity to AI services"):
        with st.spinner("Testing AI connections..."):
            try:
                from openengage.core.personalized_email_generator import test_ai_connections
                status = test_ai_connections()

                st.write("### Connection Test Results:")

                if status['openai']:
                    st.success("✅ OpenAI: Connection successful")
                elif openai_api_key:
                    st.error("❌ OpenAI: Connection failed")
                else:
                    st.warning("⚠️ OpenAI: No API key configured")

                if status['claude']:
                    st.success("✅ Claude: Connection successful")
                elif claude_api_key:
                    st.error("❌ Claude: Connection failed")
                else:
                    st.warning("⚠️ Claude: No API key configured")

                if status['errors']:
                    st.write("**Error Details:**")
                    for error in status['errors']:
                        st.error(error)

                if not status['openai'] and not status['claude']:
                    st.warning("⚠️ No AI services are currently available. The system will use crew agents as fallback.")

            except Exception as e:
                st.error(f"Error testing connections: {str(e)}")

    st.divider()

    # Create three columns for file uploaders and default data button
    col1, col2, col3 = st.columns(3)

    with col1:
        # Main CSV file uploader
        st.write("### Email User Data CSV")
        uploaded_file = st.file_uploader("Choose a CSV file with email user data", type='csv', key="personalized_user_data_csv")

    with col2:
        # Optional offer details uploader
        st.write("### Offer Details CSV (Optional)")
        offer_file = st.file_uploader("Choose a CSV file with offer details", type='csv', key="personalized_offer_details_csv")

        # Sample offer data download button
        if not offer_file:
            try:
                # Get current time
                now = datetime.now()

                # Create sample offer data
                sample_offers = [
                    {
                        'offer_start_time': now.isoformat(),
                        'offer_end_time': (now + timedelta(days=7)).isoformat(),
                        'offer_details': '15% discount on your next purchase'
                    },
                    {
                        'offer_start_time': (now + timedelta(days=10)).isoformat(),
                        'offer_end_time': (now + timedelta(days=20)).isoformat(),
                        'offer_details': 'Buy one get one free on selected items'
                    }
                ]

                # Convert to DataFrame and then to CSV
                sample_df = pd.DataFrame(sample_offers)
                csv = sample_df.to_csv(index=False)

                # Create a download button for the sample
                st.download_button(
                    label="Download Sample Offer CSV",
                    data=csv,
                    file_name="sample_offers.csv",
                    mime="text/csv",
                    key="download_sample_personalized_offer_csv"
                )
            except Exception as e:
                st.error(f"Error generating sample offer data: {str(e)}")

    with col3:
        # Default user data button
        st.write("### Realtime Generation")

        # Check if we should reset the use_default_data state (when user uploads a file)
        if uploaded_file is not None and st.session_state.use_default_data:
            st.session_state.use_default_data = False
            st.session_state.default_data_loaded = False
            st.session_state.default_data_df = None

        # Use button to toggle default data usage
        if st.button("📊 Use Realtime Data", help="Realtime data processing and generation", key="personalized_realtime_data"):
            from openengage.core.user_data_processor import UserDataProcessor
            from openengage.core.data_integration import DataIntegration

            # Create a progress container and elements
            progress_container = st.container()
            with progress_container:
                st.write("### Processing User Data")
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Update status for initialization
                status_text.text("Initializing data refresh and processing...")
                progress_bar.progress(5)
                st.write("")  # Add some spacing

            # Define a callback function to update progress for both refresh and processing
            def progress_callback(stage, percentage):
                # Ensure the progress bar is updated properly
                with progress_container:
                    status_text.text(f"Processing: {stage}")
                    # For data refresh, scale from 5-50%
                    # For data processing, scale from 50-100%
                    if 'refresh' in stage.lower():
                        current_progress = int(5 + percentage * 0.45)  # Scale to 5-50%
                    else:
                        current_progress = int(50 + percentage * 0.5)  # Scale to 50-100%
                    progress_bar.progress(current_progress)
                    # Force a small sleep to ensure UI updates
                    import time
                    time.sleep(0.05)

            # First, refresh the data using DataIntegration
            with progress_container:
                status_text.text("Refreshing data from external sources...")
                progress_bar.progress(5)

            # Create data integration instance and refresh data
            data_integration = DataIntegration()

            # Define a separate callback for data refresh to properly scale progress
            def refresh_callback(stage, percentage):
                progress_callback(f"Refreshing: {stage}", percentage)

            # Refresh the data
            data_integration.refresh_data(progress_callback=refresh_callback)

            with progress_container:
                status_text.text("Data refresh completed. Starting data processing...")
                progress_bar.progress(50)

            # Create processor and process data with progress updates
            user_data_processor = UserDataProcessor()

            # Run the data processing
            user_data_processor.generate_user_behaviour_data(progress_callback)

            # Final update
            with progress_container:
                progress_bar.progress(100)
                status_text.text("Data processing completed!")
                st.success("User data has been processed and is ready for campaign generation")

            # Set session state to use the processed data
            st.session_state.use_default_data = True
            st.session_state.default_data_loaded = False  # Force reload of the new data

        # Show indicator when using default data
        if st.session_state.use_default_data:
            st.success("Using realtime user data")

        # Load default data if needed
        if st.session_state.use_default_data and not st.session_state.default_data_loaded:
            try:
                # Load the default user behavior data from the new location
                default_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                                               'Sample Data For Mass Generation', 'processed_user_data.csv')

                df = pd.read_csv(default_data_path)

                # Store in session state
                st.session_state.default_data_df = df
                st.session_state.default_data_loaded = True
            except Exception as e:
                st.error(f"Error loading default user data: {str(e)}")
                st.session_state.use_default_data = False
                st.session_state.default_data_loaded = False
                return

    # Add session state variables for tracking campaign generation and sending
    if "personalized_campaign_generated" not in st.session_state:
        st.session_state.personalized_campaign_generated = False

    if "personalized_campaign_file_path" not in st.session_state:
        st.session_state.personalized_campaign_file_path = None

    if "personalized_send_campaign_clicked" not in st.session_state:
        st.session_state.personalized_send_campaign_clicked = False

    # Use either uploaded file or default data
    if uploaded_file is not None or st.session_state.use_default_data:
        try:
            # Get data from appropriate source
            if st.session_state.use_default_data:
                df = st.session_state.default_data_df
            else:
                df = pd.read_csv(uploaded_file)

            # Validate columns
            required_columns = ['user_email', 'first_name', 'user_behaviour', 'user_stage']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                st.error(f"Missing required columns: {', '.join(missing_columns)}")
                st.error("Expected columns: user_email, first_name, user_behaviour, user_stage")
                return

            # Show data preview
            st.write("### Data Preview")
            st.dataframe(df.head())

            # Process button
            if st.button("Generate Fully Personalized Emails", type="primary", key="generate_personalized_emails"):
                with st.spinner("Generating fully personalized emails..."):
                    # Create progress tracking elements
                    campaign_progress = st.container()
                    with campaign_progress:
                        st.write("### Generating Personalized Emails")
                        progress_bar = st.progress(0)
                        progress_text = st.empty()
                        st.write("")  # Add some spacing

                    # Define callback for progress updates
                    def update_progress(current, total, message=""):
                        with campaign_progress:
                            progress_percent = int(min(current / total * 100, 100))
                            progress_bar.progress(progress_percent)
                            progress_text.text(f"Processing: {current+1}/{total} users complete ({progress_percent}%) - {message}")
                            # Force a small sleep to ensure UI updates
                            import time
                            time.sleep(0.01)

                    # Process data with progress tracking
                    from openengage.core.personalized_email_generator import generate_personalized_emails
                    result_df = generate_personalized_emails(df, progress_callback=update_progress, selected_model=selected_model)

                    # Update UI with completion message
                    with campaign_progress:
                        progress_bar.progress(100)
                        progress_text.text(f"Completed processing {len(df)} users!")
                        st.success("✅ Personalized emails have been successfully generated")

                    # Process offer data if available
                    if offer_file is not None:
                        try:
                            # Read offer CSV
                            offers_df = pd.read_csv(offer_file)

                            # Validate offer columns
                            required_offer_columns = ['offer_start_time', 'offer_end_time', 'offer_details']
                            missing_offer_columns = [col for col in required_offer_columns if col not in offers_df.columns]

                            if not missing_offer_columns:
                                # Process offers
                                try:
                                    from openengage.core.offer_generator import process_offers_for_mass_campaign
                                    result_df = process_offers_for_mass_campaign(result_df, offers_df)
                                except Exception as e:
                                    st.error(f"Error processing offers: {str(e)}")
                                st.success("Offer details processed successfully!")
                            else:
                                st.warning(f"Offer file is missing required columns: {', '.join(missing_offer_columns)}. Offers will not be included.")
                        except Exception as e:
                            st.warning(f"Error processing offer details: {str(e)}. Campaigns will be generated without offers.")

                    # Save to CSV
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_filename = f"personalized_campaign_{timestamp}.csv"

                    # Ensure the campaigns directory exists
                    campaigns_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "campaigns")
                    os.makedirs(campaigns_dir, exist_ok=True)

                    output_path = os.path.join(campaigns_dir, output_filename)
                    result_df.to_csv(output_path, index=False)

                    # Store the campaign file path in session state for sending later
                    st.session_state.personalized_campaign_file_path = output_path
                    st.session_state.personalized_campaign_generated = True

                    # Show results
                    st.write("### Generated Personalized Emails")
                    st.dataframe(result_df)

                    # Show HTML previews
                    if 'HTML_Content' in result_df.columns:
                        st.write("### HTML Email Previews")

                        # Create a scrollable container style for the HTML previews
                        st.markdown("""
                        <style>
                        .email-preview-container {
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            height: 600px;
                            overflow-y: auto;
                            background-color: white;
                            padding: 10px;
                            margin: 0;
                            width: 100%;
                        }
                        iframe {
                            width: 100%;
                            height: 100%;
                            border: none;
                        }
                        </style>
                        """, unsafe_allow_html=True)

                        for _, row in result_df.head(5).iterrows():
                            if row.get('HTML_Content'):
                                with st.expander(f"Preview: {row['first_name']} - {row['Subject']}"):
                                    # Wrap the HTML content in a scrollable div
                                    # Include offer HTML if available
                                    offer_html = row.get('Offer_HTML', '')

                                    # Insert offer HTML just above "Best Regards" if it exists
                                    if offer_html and row['HTML_Content']:
                                        # Common closing phrases to look for
                                        closing_phrases = [
                                            "Best Regards",
                                            "Best regards",
                                            "Regards",
                                            "regards",
                                            "Sincerely",
                                            "sincerely",
                                            "Thank you",
                                            "thank you",
                                            "Thanks",
                                            "thanks"
                                        ]

                                        html_content = row['HTML_Content']
                                        for phrase in closing_phrases:
                                            if phrase in html_content:
                                                html_content = html_content.replace(
                                                    phrase,
                                                    f"{offer_html}\n\n{phrase}"
                                                )
                                                break
                                        else:
                                            # If no closing phrase found, append at the end
                                            html_content += f"\n\n{offer_html}"
                                    else:
                                        html_content = row['HTML_Content']

                                    # Use st.components.v1.html for proper HTML rendering
                                    full_html = f"""
                                    <!DOCTYPE html>
                                    <html>
                                    <head>
                                        <meta charset="UTF-8">
                                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                        <style>
                                            body {{ margin: 0; padding: 10px; font-family: Arial, sans-serif; }}
                                            img {{ max-width: 100%; height: auto; }}
                                            table {{ width: 100%; border-collapse: collapse; }}
                                            td {{ padding: 8px; }}
                                        </style>
                                    </head>
                                    <body>
                                        {html_content}
                                    </body>
                                    </html>
                                    """

                                    st.components.v1.html(
                                        full_html,
                                        height=3000,
                                        scrolling=True
                                    )

                                    # Also add a download button for this specific email
                                    st.download_button(
                                        label="Download this email as HTML",
                                        data=full_html,
                                        file_name=f"personalized_email_{row['first_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
                                        mime="text/html",
                                        key=f"download_personalized_email_html_{row['first_name']}_{_}"
                                    )

                    # Create download button for the generated campaigns
                    try:
                        with open(output_path, "rb") as file:
                            st.download_button(
                                label="Download Generated Personalized Campaigns",
                                data=file,
                                file_name=output_filename,
                                mime="text/csv",
                                key="download_personalized_campaigns"
                            )
                    except Exception as e:
                        st.error(f"Error creating download button: {str(e)}")

        except Exception as e:
            st.error(f"Error processing data: {str(e)}")
            import traceback
            st.error(f"Detailed error: {traceback.format_exc()}")

    # Campaign sending section - moved outside the campaign generation block
    # This ensures it persists across reruns
    if st.session_state.personalized_campaign_generated:
        st.divider()
        st.write("### 🚀 Email Campaign Actions")

        # Initialize session state for schedule button
        if "personalized_schedule_campaign_clicked" not in st.session_state:
            st.session_state.personalized_schedule_campaign_clicked = False

        # Create two columns for the buttons
        send_col1, send_col2 = st.columns(2)

        # Execute the sending logic if the Send Now button was clicked
        with send_col1:
            if st.button("Send Emails Now", type="primary", key="send_personalized_emails_now"):
                try:
                    st.session_state.personalized_send_campaign_clicked = True

                    # Use the stored campaign path from session state if available
                    if st.session_state.personalized_campaign_file_path and os.path.exists(st.session_state.personalized_campaign_file_path):
                        campaign_path = st.session_state.personalized_campaign_file_path
                    else:
                        # Fallback to finding the latest campaign file
                        campaign_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "campaigns")

                        campaign_files = [f for f in os.listdir(campaign_dir) if f.startswith('personalized_campaign_') and f.endswith('.csv')]
                        if not campaign_files:
                            st.error("No personalized campaign files found in data/campaigns directory")
                            return

                        # Get the latest file based on modification time
                        latest_file = max(
                            campaign_files,
                            key=lambda f: os.path.getmtime(os.path.join(campaign_dir, f))
                        )
                        campaign_path = os.path.join(campaign_dir, latest_file)

                    # Read the campaign data
                    campaign_df = pd.read_csv(campaign_path)

                    # Import required components
                    from openengage.core.email_sender import create_email_sender
                    from openengage.ui.sending_config import get_esp_api_keys, get_sender_details

                    # Get API keys and sender details
                    api_keys = get_esp_api_keys()
                    sender_details = get_sender_details()

                    st.write(f"Got API keys and sender details")

                    # Find active email sender configuration
                    active_config = None
                    for config in sender_details:
                        if config.get("channel") == "Email" and config.get("active"):
                            active_config = config
                            break

                    if not active_config:
                        st.error("No active email sender configuration found. Please configure one in the Sending Configuration.")
                        return

                    st.write(f"Using ESP: {active_config.get('esp')}")

                    # Get the appropriate API key
                    esp = active_config.get("esp")
                    api_key = None

                    if esp == "SparkPost":
                        api_key = api_keys.get("sparkpost")
                    elif esp == "Mailmodo":
                        api_key = api_keys.get("mailmodo")
                    elif esp == "Amazon SES":
                        api_key = api_keys.get("amazon_ses")

                    if not api_key:
                        st.error(f"No API key found for {esp}. Please configure it in the Sending Configuration.")
                        return

                    # Create email sender
                    sender = create_email_sender(esp.lower().replace(" ", "_"), api_key)

                    # Set sender details
                    sender.set_sender_details(
                        active_config.get("sender_name"),
                        active_config.get("sender_email"),
                        active_config.get("reply_to_email")
                    )

                    st.write("Sending personalized email campaign...")

                    # Send the campaign
                    with st.spinner(f"Sending personalized email campaign via {esp}..."):
                        results = sender.send_emails(campaign_df)

                        if results.get("errors"):
                            st.error(f"Encountered {len(results['errors'])} errors while sending campaign")
                            for error in results["errors"]:
                                st.error(error)
                        else:
                            st.success(f"Successfully sent personalized email campaign to {results['total_accepted']} recipients!")

                    # Reset the state for next time
                    st.session_state.personalized_send_campaign_clicked = False
                except Exception as e:
                    st.error(f"Error sending personalized campaign: {str(e)}")
                    import traceback
                    st.error(traceback.format_exc())

        # Execute the scheduling logic if the Schedule Mails button was clicked
        with send_col2:
            if st.button("Schedule Mails", type="secondary", key="schedule_personalized_emails"):
                try:
                    st.session_state.personalized_schedule_campaign_clicked = True

                    # Use the stored campaign path from session state if available
                    if st.session_state.personalized_campaign_file_path and os.path.exists(st.session_state.personalized_campaign_file_path):
                        campaign_path = st.session_state.personalized_campaign_file_path
                        campaign_filename = os.path.basename(campaign_path)
                        st.write(f"Using campaign file: {campaign_filename}")
                    else:
                        # Fallback to finding the latest campaign file
                        campaign_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "campaigns")
                        st.write(f"Looking for campaigns in: {campaign_dir}")

                        campaign_files = [f for f in os.listdir(campaign_dir) if f.startswith('personalized_campaign_') and f.endswith('.csv')]
                        if not campaign_files:
                            st.error("No personalized campaign files found in data/campaigns directory")
                            return

                        st.write(f"Found {len(campaign_files)} campaign files")

                        # Get the latest file based on modification time
                        latest_file = max(
                            campaign_files,
                            key=lambda f: os.path.getmtime(os.path.join(campaign_dir, f))
                        )
                        campaign_path = os.path.join(campaign_dir, latest_file)
                        st.write(f"Latest campaign file: {latest_file}")

                    # Read the campaign data
                    campaign_df = pd.read_csv(campaign_path)
                    st.write(f"Loaded campaign with {len(campaign_df)} rows")

                    # Check if Send_Time column exists
                    if 'Send_Time' not in campaign_df.columns:
                        st.error("The campaign data does not have a Send_Time column. Please process the data with time configuration settings.")
                        return

                    # Import required components
                    from openengage.core.email_sender import create_email_sender
                    from openengage.ui.sending_config import get_esp_api_keys, get_sender_details

                    # Get API keys and sender details
                    api_keys = get_esp_api_keys()
                    sender_details = get_sender_details()

                    st.write(f"Got API keys and sender details")

                    # Find active email sender configuration
                    active_config = None
                    for config in sender_details:
                        if config.get("channel") == "Email" and config.get("active"):
                            active_config = config
                            break

                    if not active_config:
                        st.error("No active email sender configuration found. Please configure one in the Sending Configuration.")
                        return

                    st.write(f"Using ESP: {active_config.get('esp')}")

                    # Get the appropriate API key
                    esp = active_config.get("esp")
                    api_key = None

                    if esp == "SparkPost":
                        api_key = api_keys.get("sparkpost")
                    elif esp == "Mailmodo":
                        api_key = api_keys.get("mailmodo")
                    elif esp == "Amazon SES":
                        api_key = api_keys.get("amazon_ses")

                    if not api_key:
                        st.error(f"No API key found for {esp}. Please configure it in the Sending Configuration.")
                        return

                    # Create email sender
                    sender = create_email_sender(esp.lower().replace(" ", "_"), api_key)

                    # Set sender details
                    sender.set_sender_details(
                        active_config.get("sender_name"),
                        active_config.get("sender_email"),
                        active_config.get("reply_to_email")
                    )

                    st.write("Scheduling personalized campaign...")

                    # Schedule the campaign
                    with st.spinner(f"Scheduling personalized campaign via {esp}..."):
                        results = sender.schedule_emails(campaign_df)

                        if "email_data" in results:
                                # Create timestamp for the filename
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                results_filename = f"scheduled_personalized_campaign_results_{timestamp}.csv"

                                # Create campaign_results directory
                                project_root = os.path.join(os.path.dirname(__file__), "..", "..", "..")
                                results_dir = os.path.join(project_root, "data", "campaign_results")
                                os.makedirs(results_dir, exist_ok=True)

                                # Save results to file
                                results_path = os.path.join(results_dir, results_filename)
                                results["email_data"].to_csv(results_path, index=False)

                                # Log the save location
                                st.info(f"Scheduled personalized campaign results saved to: {results_path}")

                                # Provide download button
                                csv = results["email_data"].to_csv(index=False)
                                st.download_button(
                                    label="Download Scheduled Personalized Campaign Results",
                                    data=csv,
                                    file_name=results_filename,
                                    mime="text/csv",
                                    key="download_scheduled_personalized_campaign_results"
                                )

                        if results.get("errors"):
                            st.error(f"Encountered {len(results['errors'])} errors while scheduling personalized campaign")
                            for error in results["errors"]:
                                st.error(error)
                        else:
                            st.success(f"Successfully scheduled personalized campaign for {results['total_accepted']} recipients!")
                            # Delete the used campaign file
                            try:
                                if os.path.exists(campaign_path) and campaign_path.endswith(".csv") and "personalized_campaign" in os.path.basename(campaign_path):
                                    os.remove(campaign_path)
                                    st.success(f"Used campaign file deleted: {os.path.basename(campaign_path)}")
                            except Exception as e:
                                st.warning(f"Could not delete campaign file: {str(e)}")
                    # Reset the state for next time
                    st.session_state.personalized_schedule_campaign_clicked = False
                except Exception as e:
                    st.error(f"Error scheduling personalized campaign: {str(e)}")
                    import traceback
                    st.error(traceback.format_exc())
