"""
Organization editor UI component for OpenEngage.
"""
import os
import json
import re
import streamlit as st
from utils.file_utils import (
    load_communication_settings, save_communication_settings,
    load_organization_data, save_organization_data, get_all_organizations
)
from src.openengage.ui.brand_guidelines import display_brand_guidelines, reload_brand_guidelines
from utils.file_utils import load_brand_guidelines, save_brand_guidelines

# Import brand analyzer
from src.openengage.agents.brand_analyzer_updated import WebsiteBrandAnalyzerTool
from src.openengage.agents.archetype_analyzer import analyze_brand_archetypes

# Removed display_organization_update_option function - now using display_organization_summary approach

def check_existing_organization_data():
    """Check if organization data already exists and load it into session state."""
    # Add custom CSS for full-width layout
    st.markdown("""
        <style>
        .stApp {
            max-width: 100%;
        }
        .main .block-container {
            max-width: 100%;
            padding-top: 2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        </style>
    """, unsafe_allow_html=True)

    # Show analysis in progress if flagged
    if hasattr(st.session_state, 'analyzing_organization') and st.session_state.analyzing_organization:
        display_organization_analysis()
        return True

    # Get organization URL from session state
    organization_url = st.session_state.organization_url

    # Check if we already have data for this organization
    all_orgs = load_organization_data()
    if organization_url in all_orgs:
        # Use existing data instead of re-analyzing
        org_data = all_orgs[organization_url]

        # Make sure URL is in the data
        org_data["url"] = organization_url

        # Store in session state
        st.session_state.organization_data = org_data
        st.session_state.crew.organization_data = org_data

        # Load brand guidelines if they exist
        brand_guidelines = load_brand_guidelines()
        if organization_url in brand_guidelines:
            st.session_state.brand_guidelines = brand_guidelines[organization_url]

        # Load communication settings if they exist
        st.session_state.communication_settings = load_communication_settings(organization_url=organization_url)

        # Only show summary if show_org_summary flag is not explicitly set to False
        if not hasattr(st.session_state, 'show_org_summary') or st.session_state.show_org_summary != False:
            # Show organization summary directly when data already exists
            st.session_state.show_org_summary = True
            st.session_state.org_setup_step = 4
            display_organization_summary()
            return True

    # No existing data found, return False to continue with normal flow
    return False

def display_organization_analysis():
    """
    Display the organization analysis progress.
    """
    # Add custom CSS for full-width layout
    st.markdown("""
        <style>
        .stApp {
            max-width: 100%;
        }
        .main .block-container {
            max-width: 100%;
            padding-top: 2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        </style>
    """, unsafe_allow_html=True)

    # Show analysis in progress message
    with st.spinner("🔍 Analyzing your organization's website. This will take a few seconds..."):
        try:
            # Make sure the organization data file exists and is in the right format
            # This is done by the load_organization_data function
            # which automatically fixes any format issues
            _ = load_organization_data()

            # Get organization URL from session state
            organization_url = st.session_state.organization_url

            # Scrape website content
            content = st.session_state.crew.scraper._run(organization_url)

            # Analyze the organization
            org_data = json.loads(st.session_state.crew.org_analyzer._run(content))

            # Add the URL to the organization data
            org_data["url"] = organization_url

            # Make sure the 'Class' field is included (force it if needed)
            if "Class" not in org_data or not org_data["Class"]:
                # Determine class based on domain keywords
                domain = org_data.get("Domain", "").lower()

                # Mapping of domain keywords to classes
                domain_to_class = {
                    "edtech": "EdTech",
                    "education": "EdTech",
                    "learning": "EdTech",
                    "academic": "EdTech",
                    "e-commerce": "E-commerce",
                    "ecommerce": "E-commerce",
                    "retail": "E-commerce",
                    "shop": "E-commerce",
                    "store": "E-commerce",
                    "skincare": "E-commerce",
                    "beauty": "E-commerce",
                    "wellness": "E-commerce",
                    "insurance": "Insurance (InsurTech)",
                    "insurtech": "Insurance (InsurTech)",
                    "policy": "Insurance (InsurTech)",
                    "bank": "Banking",
                    "finance": "Banking",
                    "fintech": "Banking",
                    "food": "Quick-Commerce / Food",
                    "delivery": "Quick-Commerce / Food",
                    "restaurant": "Quick-Commerce / Food",
                    "grocery": "Quick-Commerce / Food",
                    "travel": "TravelTech",
                    "tourism": "TravelTech",
                    "hotel": "TravelTech",
                    "flight": "TravelTech",
                    "creator": "Creator Economy Platform (e.g., Substack, Patreon)",
                    "content": "Creator Economy Platform (e.g., Substack, Patreon)",
                    "platform": "Creator Economy Platform (e.g., Substack, Patreon)"
                }

                # Try to match domain keywords
                determined_class = None
                for keyword, class_name in domain_to_class.items():
                    if keyword in domain:
                        determined_class = class_name
                        break

                # Set default if no match found
                org_data["Class"] = determined_class or "E-commerce"

            # Store in session state
            st.session_state.organization_data = org_data
            st.session_state.crew.organization_data = org_data

            # Create brand analyzer tool
            brand_analyzer = WebsiteBrandAnalyzerTool()

            # Show brand analysis in progress
            st.info("🎨 Analyzing brand elements from website...")

            # Analyze brand guidelines
            try:
                brand_guidelines = brand_analyzer._run(organization_url)

                # Make sure organization_url is in the brand guidelines
                brand_guidelines["organization_url"] = organization_url

                # Analyze archetypes with percentage-based scoring
                st.info("🎭 Analyzing brand archetypes...")
                try:
                    archetype_analysis = analyze_brand_archetypes(brand_guidelines)

                    # Update brand guidelines with archetype scores
                    brand_guidelines["archetype_scores"] = archetype_analysis["archetype_scores"]

                    # Get the primary archetype from the analysis
                    primary_archetype = archetype_analysis.get("primary_archetype", "")
                    primary_reasoning = archetype_analysis.get("primary_reasoning", "")

                    # Store the primary archetype as brand_personality for backward compatibility
                    if primary_archetype:
                        brand_guidelines["brand_personality"] = primary_archetype
                        brand_guidelines["brand_personality_reasoning"] = primary_reasoning

                    st.success("✅ Archetype analysis complete!")
                except Exception as arch_e:
                    st.warning(f"Archetype analysis failed: {str(arch_e)}")

                # Store brand guidelines in session state
                st.session_state.brand_guidelines = brand_guidelines

                # Save brand guidelines for this organization
                save_brand_guidelines(brand_guidelines, organization_url)

                st.success("✅ Brand analysis complete!")
            except Exception as brand_e:
                st.warning(f"Brand analysis partial or failed: {str(brand_e)}")

            # Load existing organization data to ensure we preserve multiple organizations
            all_orgs = load_organization_data()
            # Update the organization in our data
            all_orgs[organization_url] = org_data
            # Now save all organization data
            # We need to save the individual organization data for compatibility
            save_organization_data(org_data, organization_url)

            st.success("✅ Organization analysis complete!")

            # Reset the analysis flag and show summary
            st.session_state.analyzing_organization = False
            st.session_state.show_org_summary = True
            st.session_state.org_setup_step = 4
            st.rerun()
        except Exception as e:
            st.error(f"Failed to analyze organization: {str(e)}")
            st.session_state.analyzing_organization = False
            st.rerun()

def display_product_options():
    """
    Display product options for the current organization.
    """
    # Get current organization URL from session state
    org_url = st.session_state.organization_url if hasattr(st.session_state, 'organization_url') else None

    if not org_url:
        st.error("Organization URL not found. Please complete organization setup first.")
        return

    try:
        # Check if organization products visibility is enabled
        org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

        with open("data/product_details.json", "r") as f:
            all_products = json.load(f)

        # Filter products for this organization
        if org_filter_enabled:
            org_products = [p for p in all_products if p.get("Company_URL", "") == org_url or
                                                    p.get("organization_url", "") == org_url]
        else:
            # Even if filter is disabled, still filter by organization URL for this specific section
            org_products = [p for p in all_products if p.get("Company_URL", "") == org_url or
                                                    p.get("organization_url", "") == org_url]

        if org_products:
            st.session_state.existing_products = org_products
            st.session_state.show_product_options = True

            # Set the first product as the current product data
            if org_products and len(org_products) > 0:
                st.session_state.product_data = org_products[0]

            # Redirect to product setup
            st.session_state.show_product_editor = False
            st.session_state.show_org_editor = False
            st.rerun()
        else:
            # No products found, ask for URLs
            st.info("No products found for your organization. Please add product URLs to continue.")

            # Add a message to the chat
            if "messages" in st.session_state:
                # Check if we already have this message to avoid duplicates
                product_url_message = "I don't see any products in your database. Please add the product URLs separated by commas so I can analyze them for you."
                if not any(product_url_message in msg.get("content", "") for msg in st.session_state.messages):
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": product_url_message
                    })

            # Make sure we're showing the chat interface
            st.session_state.show_org_editor = False
            st.session_state.show_product_editor = False
            st.session_state.show_product_options = False

            # Redirect to main view to allow URL input
            st.rerun()

    except (FileNotFoundError, json.JSONDecodeError):
        # No products found, ask for URLs
        st.info("No products found for your organization. Please add product URLs to continue.")

        # Add a message to the chat
        if "messages" in st.session_state:
            # Check if we already have this message to avoid duplicates
            product_url_message = "I don't see any products in your database. Please add the product URLs separated by commas so I can analyze them for you."
            if not any(product_url_message in msg.get("content", "") for msg in st.session_state.messages):
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": product_url_message
                })

        # Make sure we're showing the chat interface
        st.session_state.show_org_editor = False
        st.session_state.show_product_editor = False
        st.session_state.show_product_options = False

        # Redirect to main view to allow URL input
        st.rerun()

def display_organization_summary():
    """
    Display a summary of the organization setup with options to edit or proceed to Product Setup.
    """
    # Set full-width layout
    st.markdown("""
        <style>
        .stApp {
            max-width: 100%;
        }
        .main .block-container {
            max-width: 100%;
            padding-top: 2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        </style>
    """, unsafe_allow_html=True)

    # Ensure we're using the current organization's settings
    if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
        org_url = st.session_state.organization_url
        # Reload communication settings for this specific organization
        st.session_state.communication_settings = load_communication_settings(organization_url=org_url)

    # Header
    st.markdown("""
        <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;'>
            <h2 style='color: #FFFFFF; margin: 0; font-size: 1.2rem;'>Organization Setup Complete</h2>
            <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                Review your organization details below or proceed to Product Setup.
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Organization Details Section
    st.subheader("1. Organization Details")
    if hasattr(st.session_state, 'organization_data') and st.session_state.organization_data:
        org_data = st.session_state.organization_data
        st.markdown(f"**Business Domain:** {org_data.get('Domain', '')}")
        st.markdown(f"**Objective:** {org_data.get('Purpose', '')}")

        with st.expander("More Details"):
            st.markdown(f"**What We Do:** {org_data.get('WhatWeDo', '')}")
            st.markdown(f"**About Us:** {org_data.get('AboutUs', '')}")
            st.markdown(f"**Organization URL:** {org_data.get('url', '')}")

    # Brand Guidelines Section
    st.subheader("2. Brand Guidelines")

    # Force reload brand guidelines to ensure we have the latest data
    reload_brand_guidelines()

    # Display brand guidelines if available
    if hasattr(st.session_state, 'brand_guidelines') and st.session_state.brand_guidelines:
        guidelines = st.session_state.brand_guidelines

        # Display colors
        st.markdown("**Brand Colors:**")
        color_types = [
            ("primary_color", "Primary"),
            ("secondary_color", "Secondary"),
            ("accent_color", "Accent"),
            ("neutral_color", "Neutral"),
            ("background_color", "Background"),
            ("text_color", "Text")
        ]

        # Filter out empty colors
        colors_to_display = [(key, label) for key, label in color_types if guidelines.get(key, "")]

        if colors_to_display:
            color_cols = st.columns(min(5, len(colors_to_display) + 1))
            for i, (color_key, color_label) in enumerate(colors_to_display):
                color_value = guidelines.get(color_key, "")
                with color_cols[i % 5]:
                    st.markdown(f"""
                        <div style='background-color: {color_value}; width: 50px; height: 50px; border-radius: 5px; margin-bottom: 5px;'></div>
                        <p style='font-size: 0.8rem;'>{color_label}: {color_value}</p>
                    """, unsafe_allow_html=True)

        # Display CTA guidelines
        st.markdown(f"**CTA Type:** {guidelines.get('cta_type', '')}")
        st.markdown(f"**CTA Size:** {guidelines.get('cta_size', '')}")
        if guidelines.get('cta_type', '') == 'Button':
            st.markdown(f"**Button Style:** {guidelines.get('button_style', '')}")
            if guidelines.get('button_style', '') == 'Rounded':
                st.markdown(f"**Border Radius:** {guidelines.get('border_radius', '')}")

        # Display typography guidelines
        st.markdown(f"**Primary Font:** {guidelines.get('font', '')}")
        st.markdown(f"**Font Size:** {guidelines.get('font_size', '')}")
        st.markdown(f"**Font Weight:** {guidelines.get('font_weight', '')}")

    # Communication Settings Section
    st.subheader("3. Communication Settings")
    if hasattr(st.session_state, 'communication_settings') and st.session_state.communication_settings:
        comm_settings = st.session_state.communication_settings

        # Display channels
        if hasattr(st.session_state, 'selected_channels') and st.session_state.selected_channels:
            channels = [k for k, v in st.session_state.selected_channels.items() if v]
            st.markdown(f"**Communication Channels:** {', '.join(channels)}")

        # Brand-related settings
        st.markdown("##### Brand Communication Style")
        st.markdown(f"**Sender Name:** {comm_settings.get('sender_name', '')}")

        # Check if the communication style matches any of the brand tones
        style = comm_settings.get('style', '')
        tone_of_voice = comm_settings.get('tone_of_voice') if comm_settings.get('tone_of_voice') else guidelines.get('tone_of_voice','')

        # Extract all tone descriptors
        tone_descriptors = [tone.strip().lower() for tone in tone_of_voice.split(',') if tone.strip()] if tone_of_voice else []

        # Check if current style matches any brand tone or standard style that's in brand tones
        is_brand_tone = False

        if tone_descriptors and style:
            # Check direct match with any tone descriptor
            for tone in tone_descriptors:
                if style.lower() == tone.lower():
                    is_brand_tone = True
                    break

            # Also check if it's a standard style that's also in the brand tones
            if not is_brand_tone:
                standard_styles = ["friendly", "persuasive", "authoritative", "direct", "informative", "conversational"]
                if style.lower() in [s.lower() for s in standard_styles]:
                    for tone in tone_descriptors:
                        if style.lower() == tone.lower():
                            is_brand_tone = True
                            break

        # Highlight if using brand tone
        if is_brand_tone:
            st.markdown(f"""
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Communication Style:</span>
                <span style="background-color: #f0e6ff; padding: 2px 8px; border-radius: 4px; color: #8D06FE;">
                    ✨ {style} (Brand Tone)
                </span>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"**Communication Style:** {style}")

        st.markdown(f"**Email Length:** {comm_settings.get('length', '')}")

        # Display brand personality if available
        if comm_settings.get('brand_personality'):
            st.markdown(f"**Brand Personality:** {comm_settings.get('brand_personality', '')}")
        else:
            st.markdown(f"**Brand Personality:** {guidelines.get('brand_personality', '')}")

        # UTM Parameters with clear separation
        st.markdown("##### UTM Parameters")
        st.markdown(f"**UTM Source:** {comm_settings.get('utm_source', '')}")
        st.markdown(f"**UTM Medium:** {comm_settings.get('utm_medium', '')}")
        st.markdown(f"**UTM Campaign:** {comm_settings.get('utm_campaign', '')}")
        st.markdown(f"**UTM Content:** {comm_settings.get('utm_content', '')}")

    # Action buttons
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("Edit Organization ( Manual )", type="secondary", use_container_width=True):
            # Reset to first step to allow editing
            st.session_state.org_setup_step = 1
            st.session_state.show_org_summary = False

            # Reset saved flag to allow editing of organization data
            if hasattr(st.session_state, 'organization_data') and st.session_state.organization_data:
                org_data = st.session_state.organization_data.copy()
                org_data["saved"] = False
                st.session_state.organization_data = org_data

            st.rerun()

    with col2:
        if st.button("Update Organization( AI )", type="secondary", use_container_width=True):
            # Trigger AI re-analysis of the organization
            st.session_state.analyzing_organization = True
            st.session_state.show_org_summary = False
            st.rerun()

    with col3:
        if st.button("Proceed to Product Setup", type="primary", use_container_width=True):
            st.session_state.show_org_summary = False
            st.session_state.show_org_editor = False
            st.session_state.show_product_options = True
            st.session_state.show_product_editor = False
            st.session_state.show_editor = False
            # Check for existing products and show them
            display_product_options()

def display_organization_editor():
    """
    Display the organization data editor interface.
    This is a standalone component that handles the entire organization setup process.
    """
    # First check if organization data already exists and show summary if it does
    if check_existing_organization_data():
        return

    # Set full-width layout for the organization editor
    st.markdown("""
        <style>
        .stApp {
            max-width: 100%;
        }
        .main .block-container {
            max-width: 100%;
            padding-top: 2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        </style>
    """, unsafe_allow_html=True)

    # Check if we should show the organization summary
    if hasattr(st.session_state, 'show_org_summary') and st.session_state.show_org_summary:
        display_organization_summary()
        return

    # Header
    st.markdown("""
        <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;'>
            <h2 style='color: #FFFFFF; margin: 0; font-size: 1.2rem;'>Organization Setup</h2>
            <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                Configure your organization details, brand guidelines, and communication settings.
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Check if we already have data for this organization - now handled in the first display_organization_editor function

    # Show organization analysis progress if active
    if hasattr(st.session_state, 'analyzing_organization') and st.session_state.analyzing_organization:
        display_organization_analysis()
        return

    # Initialize step state if not already done
    if "org_setup_step" not in st.session_state:
        st.session_state.org_setup_step = 1

    # Step 4: Organization Summary (handled by display_organization_summary)
    if st.session_state.org_setup_step == 4:
        display_organization_summary()
        return

    # Brand guidelines initialization is handled in brand_guidelines.py

    # Initialize communication settings if not already done
    if "communication_settings" not in st.session_state:
        # Try to load from file first
        if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
            # Try to load by organization URL first
            org_url = st.session_state.organization_url
            st.session_state.communication_settings = load_communication_settings(organization_url=org_url)
        else:
            # Use default settings if no organization data is available
            # Try to get organization name from organization_data if available
            org_name = "OpenEngage Team"

            # First try to get organization name from current user
            if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
                user_org = st.session_state.current_user.get('organization', {})
                if user_org:
                    org_name = user_org.get('name', org_name)

            # If not found in current user, try organization_data
            if org_name == "OpenEngage Team" and hasattr(st.session_state, 'organization_data') and st.session_state.organization_data:
                # Try to extract organization name from domain or URL
                org_data = st.session_state.organization_data
                domain = org_data.get("Domain", "")
                if domain:
                    org_name = domain.split(" ")[0]  # Use first word of domain
                else:
                    # Extract name from URL
                    org_url = org_data.get("url", "")
                    if org_url:
                        from urllib.parse import urlparse
                        parsed_url = urlparse(org_url)
                        netloc = parsed_url.netloc or parsed_url.path
                        # Remove www. and .com/.org/etc.
                        org_name = netloc.replace("www.", "").split(".")[0].capitalize()

            st.session_state.communication_settings = {
                "sender_name": org_name,
                "style": "friendly",
                "length": "100-150 words",
                "utm_source": "email",
                "utm_medium": "email",
                "utm_campaign": "product_launch",
                "utm_content": "initial",
                "organization_url": st.session_state.organization_url if hasattr(st.session_state, 'organization_url') else ""
            }

    # Sequential steps for organization setup

    # Step 1: Organization Details
    if st.session_state.org_setup_step == 1:
        st.subheader("1. Organization Details")

        if not st.session_state.organization_data.get("saved", False):
            with st.form("org_editor"):
                # Pre-fill with analyzed data from crew
                domain = st.text_input("Business Domain",
                                    value=st.session_state.organization_data.get("Domain", ""),
                                    help="Your business domain/industry (e.g., Technology, Healthcare, Finance)")
                what_we_do = st.text_area("What We Do",
                                        value=st.session_state.organization_data.get("WhatWeDo", ""))
                about_us = st.text_area("About Us",
                                    value=st.session_state.organization_data.get("AboutUs", ""))

                purpose_options = ["Marketing", "Customer Engagement", "Sales", "Other"]
                purpose = st.selectbox("Objective", options=purpose_options,
                                    index=purpose_options.index(st.session_state.organization_data.get("Purpose", "Marketing")) if st.session_state.organization_data.get("Purpose", "") in purpose_options else 0,
                                    help="What's your objective?")

                col1, col2 = st.columns([1, 1])
                with col1:
                    if st.form_submit_button("Save Organization Details", type="primary"):
                        # Save to JSON file
                        org_data = {
                            "Domain": domain,
                            "WhatWeDo": what_we_do,
                            "AboutUs": about_us,
                            "Purpose": purpose,
                            "saved": True,
                            "url": st.session_state.organization_url  # Ensure URL is always saved
                        }

                        # Update both session state and crew's organization data
                        st.session_state.organization_data = org_data
                        st.session_state.crew.organization_data = org_data

                        # Save to data directory using the utility function
                        # This preserves multi-organization format
                        save_organization_data(org_data, st.session_state.organization_url)

                        st.success("Organization details saved successfully!")
                        st.rerun()
                with col2:
                    if st.form_submit_button("Proceed to Brand Guidelines", type="secondary"):
                        # Save to JSON file
                        org_data = {
                            "Domain": domain,
                            "WhatWeDo": what_we_do,
                            "AboutUs": about_us,
                            "Purpose": purpose,
                            "saved": True,
                            "url": st.session_state.organization_url  # Ensure URL is always saved
                        }

                        # Update both session state and crew's organization data
                        st.session_state.organization_data = org_data
                        st.session_state.crew.organization_data = org_data

                        # Save to data directory using the utility function
                        # This preserves multi-organization format
                        save_organization_data(org_data, st.session_state.organization_url)

                        # Force reload brand guidelines to ensure form fields are initialized
                        reload_brand_guidelines()

                        # Move to brand guidelines
                        st.session_state.org_setup_step = 2
                        st.rerun()
        else:
            # Display saved organization details in read-only format
            st.markdown(f"**Business Domain:** {st.session_state.organization_data.get('Domain', '')}")
            st.markdown(f"**Objective:** {st.session_state.organization_data.get('Purpose', '')}")

            with st.expander("More Details"):
                st.markdown(f"**What We Do:** {st.session_state.organization_data.get('WhatWeDo', '')}")
                st.markdown(f"**About Us:** {st.session_state.organization_data.get('AboutUs', '')}")
                st.markdown(f"**Organization URL:** {st.session_state.organization_data.get('url', '')}")

            col1, col2 = st.columns(2)
            with col1:
                if st.button("Edit Organization Details", type="secondary"):
                    # Reset saved flag to allow editing
                    org_data = st.session_state.organization_data
                    org_data["saved"] = False
                    st.session_state.organization_data = org_data
                    st.rerun()

            with col2:
                if st.button("Continue to Brand Guidelines", type="primary"):
                    # Force reload brand guidelines to ensure form fields are initialized
                    reload_brand_guidelines()

                    # Move to brand guidelines step
                    st.session_state.org_setup_step = 2
                    st.rerun()

    # Step 2: Brand Guidelines
    elif st.session_state.org_setup_step == 2:
        # Call the display_brand_guidelines function from brand_guidelines.py
        display_brand_guidelines()

    # Step 3: Communication Settings
    elif st.session_state.org_setup_step == 3:
        st.subheader("3. Communication Settings")
        st.write("Configure how you want to communicate with your users.")

        # Ensure we're using the current organization's settings
        if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
            org_url = st.session_state.organization_url
            # Reload communication settings for this specific organization
            st.session_state.communication_settings = load_communication_settings(organization_url=org_url)

            # Load brand guidelines to get personality and tone if not already in session state
            if not hasattr(st.session_state, 'brand_guidelines') or not st.session_state.brand_guidelines:
                # Try to load from file
                all_guidelines = load_brand_guidelines()
                if org_url in all_guidelines:
                    st.session_state.brand_guidelines = all_guidelines[org_url]

        with st.form("communication_settings_form"):
            # Communication Channels Section
            st.write("#### Communication Channels")
            st.write("Select the channels you want to use for communication:")

            channels_col1, channels_col2 = st.columns(2)

            with channels_col1:
                email_channel = st.checkbox(
                    "Email",
                    value=st.session_state.selected_channels.get("Email", True),
                    key="org_email_channel"
                )

            with channels_col2:
                whatsapp_channel = st.checkbox(
                    "WhatsApp",
                    value=st.session_state.selected_channels.get("WhatsApp", False),
                    key="org_whatsapp_channel"
                )

            # Brand-related settings section with clear separation
            st.write("---")
            st.write("#### Brand Communication Style")
            st.write("These settings define how your brand communicates with users.")

            # Get brand personality and tone from brand guidelines
            brand_personality = ""
            tone_of_voice = ""
            if hasattr(st.session_state, 'brand_guidelines'):
                brand_personality = st.session_state.brand_guidelines.get("brand_personality", "")
                tone_of_voice = st.session_state.brand_guidelines.get("tone_of_voice", "")

            # Create a list of standard communication styles
            standard_styles = ["friendly", "persuasive", "authoritative", "direct", "informative", "conversational"]

            # Add all brand tone of voice values if available
            style_options = standard_styles.copy()
            brand_tone_options = []

            if tone_of_voice and tone_of_voice.strip():
                # Split tone of voice by commas and process each tone
                tone_descriptors = [tone.strip().lower() for tone in tone_of_voice.split(',') if tone.strip()]

                # Add each unique tone descriptor to the options
                for tone_descriptor in tone_descriptors:
                    if tone_descriptor:
                        # Check if this tone is already in standard styles (case insensitive)
                        is_standard = False
                        standard_index = -1
                        for i, std_style in enumerate(standard_styles):
                            if tone_descriptor.lower() == std_style.lower():
                                is_standard = True
                                standard_index = i
                                break

                        # Check if already added to brand tone options
                        if tone_descriptor.lower() not in [b.lower().replace("✨ ", "").replace(" (brand tone)", "") for b in brand_tone_options]:
                            if is_standard:
                                # Replace the standard style with the highlighted version
                                standard_styles[standard_index] = f"✨ {standard_styles[standard_index]} (Brand Tone)"
                            else:
                                # Add as a new brand tone option
                                brand_tone_option = f"✨ {tone_descriptor.capitalize()} (Brand Tone)"
                                brand_tone_options.append(brand_tone_option)

                # Create the final style options list with highlighted standard styles
                style_options = brand_tone_options.copy()

                # Add all standard styles (some may now be highlighted)
                for style in standard_styles:
                    style_options.append(style)

            col1, col2 = st.columns(2)

            with col1:
                sender_name = st.text_input(
                    "Sender Name",
                    value=st.session_state.communication_settings.get("sender_name", "OpenEngage Team"),
                    help="Name that will appear as the email sender"
                )

                # Determine the index for the style selectbox
                current_style = st.session_state.communication_settings.get("style", "friendly")

                # Find the best matching style option
                style_index = 0  # Default to first option

                # First, check for exact matches (case-insensitive)
                for i, option in enumerate(style_options):
                    # Strip the brand tone markers for comparison
                    clean_option = option.replace("✨ ", "").replace(" (Brand Tone)", "").lower()
                    if current_style.lower() == clean_option:
                        style_index = i
                        break

                # If no exact match found, check if it's a brand tone that should be highlighted
                if style_index == 0 and tone_descriptors:
                    for tone in tone_descriptors:
                        if current_style.lower() == tone.lower():
                            # It's a brand tone, find the highlighted version
                            for i, option in enumerate(style_options):
                                if "✨" in option and tone.lower() in option.lower():
                                    style_index = i
                                    break
                            break

                # Display style options with brand tone highlighted
                style = st.selectbox(
                    "Communication Style",
                    style_options,
                    index=style_index,
                    help="Select a communication style for your messages"
                )

                # Store the actual style value without the "✨ (Brand Tone)" suffix
                actual_style = style.split(' (Brand Tone)')[0].replace('✨ ', '') if '(Brand Tone)' in style else style
                # Ensure proper capitalization for brand tones (lowercase for standard styles)
                if '(Brand Tone)' in style:
                    actual_style = actual_style.lower()

                length = st.selectbox(
                    "Email Length",
                    ["<100 words", "100-150 words", "150-200 words", ">200 words"],
                    index=["<100 words", "100-150 words", "150-200 words", ">200 words"].index(
                        st.session_state.communication_settings.get("length", "100-150 words"))
                        if st.session_state.communication_settings.get("length", "100-150 words") in
                        ["<100 words", "100-150 words", "150-200 words", ">200 words"] else 1
                )

            with col2:
                # Brand personality from brand analysis
                brand_personality_input = st.text_area(
                    "Brand Personality",
                    value=brand_personality,
                    help="Key personality traits of your brand (e.g., Innovative, Trustworthy, Bold)",
                    height=80
                )

                # Display brand tone as read-only with a note that it's used in Communication Style
                st.markdown(f"""
                <div style="border-left: 3px solid #8D06FE; padding-left: 10px; margin-top: 16px;">
                    <p style="margin-bottom: 5px; font-weight: bold;">Brand Tone of Voice</p>
                    <p style="margin-top: 0; font-style: italic;">{tone_of_voice}</p>
                    <p style="margin-top: 5px; font-size: 0.8rem; color: #666;">
                        ✨ Brand tone is available as a Communication Style option
                    </p>
                </div>
                """, unsafe_allow_html=True)

            # Clear separation for UTM parameters
            st.write("---")
            st.write("#### UTM Parameters")
            st.write("These parameters help track campaign performance in analytics tools.")

            utm_cols = st.columns(4)

            with utm_cols[0]:
                utm_source = st.text_input(
                    "UTM Source",
                    value=st.session_state.communication_settings.get("utm_source", "email"),
                    help="e.g., email, newsletter"
                )

            with utm_cols[1]:
                utm_medium = st.text_input(
                    "UTM Medium",
                    value=st.session_state.communication_settings.get("utm_medium", "email"),
                    help="e.g., marketing, product_update"
                )

            with utm_cols[2]:
                utm_campaign = st.text_input(
                    "UTM Campaign",
                    value=st.session_state.communication_settings.get("utm_campaign", "product_launch"),
                    help="e.g., spring_sale, product_launch"
                )

            with utm_cols[3]:
                utm_content = st.text_input(
                    "UTM Content",
                    value=st.session_state.communication_settings.get("utm_content", "initial"),
                    help="e.g., button, header_link"
                )

            if st.form_submit_button("Save Settings", type="primary"):
                # Update selected channels
                st.session_state.selected_channels = {
                    "Email": email_channel,
                    "WhatsApp": whatsapp_channel
                }

                # Save channels to preferences.json
                selected = [k for k, v in st.session_state.selected_channels.items() if v]
                preferences = {"communication_channels": selected}
                with open('data/preferences.json', 'w') as f:
                    json.dump(preferences, f)

                # Update communication settings
                st.session_state.communication_settings.update({
                    "sender_name": sender_name,
                    "style": actual_style,  # Use the cleaned style value without the brand tone marker
                    "length": length,
                    "utm_source": utm_source,
                    "utm_medium": utm_medium,
                    "utm_campaign": utm_campaign,
                    "utm_content": utm_content,
                    "utm_medium": ", ".join(selected),  # Join selected channels
                    "brand_personality": brand_personality_input,
                    "tone_of_voice": tone_of_voice  # Keep the original tone of voice from brand guidelines
                })

                # Get organization URL from session state
                org_url = ""
                if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                    org_url = st.session_state.organization_url

                # Save with sender name from form and organization URL
                save_communication_settings(
                    settings=st.session_state.communication_settings,
                    sender_name=sender_name,  # Use the sender_name from the form
                    organization_url=org_url
                )

                st.success("Organization setup complete!")

                # Set a flag to show the organization summary
                st.session_state.show_org_summary = True

                # Reset step for next time but keep org editor active
                st.session_state.org_setup_step = 4  # Use a new step for summary

                # Rerun to show the summary
                st.rerun()
