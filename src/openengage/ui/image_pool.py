"""
Image Pool configuration UI component for OpenEngage.
This module provides a UI for managing a pool of images that can be used in email campaigns.
"""
import os
import streamlit as st
import pandas as pd
import base64
from datetime import datetime
from PIL import Image
import io
import json
import uuid
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import tempfile
from pathlib import Path

# Import for relative imports
import sys
from pathlib import Path

# Add the src directory to the Python path if needed
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.append(str(src_path))

from utils.file_utils import get_all_products

def display_image_pool():
    """Display the image pool configuration interface"""
    
    st.write("## 🖼️ Image Pool Configuration")
    st.write("Manage images for use in your email and messaging campaigns.")
    
    # Ensure image directory exists
    image_dir = os.path.join('data', 'images')
    os.makedirs(image_dir, exist_ok=True)
    
    # Image metadata file path
    metadata_file = os.path.join(image_dir, 'metadata.json')
    
    # Initialize or load image metadata
    if os.path.exists(metadata_file):
        with open(metadata_file, 'r') as f:
            try:
                image_metadata = json.load(f)
            except json.JSONDecodeError:
                image_metadata = {'images': []}
    else:
        image_metadata = {'images': []}
    
    # Create tabs for different functions
    tab1, tab2 = st.tabs(["📤 Upload Images", "🖼️ View & Manage Images"])
    
    with tab1:
        st.write("### Upload New Images")
        
        # Get all products for the organization
        org_url = None
        if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
            org_url = st.session_state.current_user.get('organization', {}).get('url', None)
            
        # Get organization filter toggle setting
        org_filter_enabled = st.session_state.get('feature_toggles', {}).get('org_products_visibility', True)
        
        # Get all products
        all_products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)
        
        # Extract product names
        product_names = ["All Products"] 
        if all_products:
            if isinstance(all_products, list):
                for product in all_products:
                    if isinstance(product, dict) and 'Product_Name' in product:
                        product_names.append(product['Product_Name'])
        
        # Create two columns for upload sources
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("#### From Local Files")
            # Image upload form
            with st.form("image_upload_form"):
                uploaded_files = st.file_uploader("Choose image files", type=['png', 'jpg', 'jpeg', 'webp', 'gif'], accept_multiple_files=True)
                
                # Optional image name field
                image_name = st.text_input(
                    "Image Name (optional)",
                    help="Enter a descriptive name for the image(s). If left empty, original filename will be used."
                )
                
                # Use in templates
                template_usage = st.selectbox(
                    "Image Usage", 
                    options=product_names,
                    help="Specify which product these images are for. Select 'All Products' to use with any product."
                )
                
                submit_upload = st.form_submit_button("Upload Images")
        
        with col2:
            st.write("#### From Web URL")
            # URL scraping form
            with st.form("url_scrape_form"):
                url_to_scrape = st.text_input(
                    "Enter URL to scrape images from",
                    placeholder="https://example.com/page-with-images"
                )
                
                # Image type filter
                image_types = st.multiselect(
                    "Image types to download",
                    options=['jpg', 'jpeg', 'png', 'webp', 'gif'],
                    default=['jpg', 'jpeg', 'png'],
                    help="Select which image types to download from the webpage"
                )
                
                # Min width/height filter
                min_size = st.slider("Minimum image size (pixels)", 0, 1000, 100, help="Skip images smaller than this size")
                
                # Use in templates
                url_template_usage = st.selectbox(
                    "Image Usage",
                    options=product_names,
                    help="Specify which product these images are for. Select 'All Products' to use with any product.",
                    key="url_usage"
                )
                
                submit_scrape = st.form_submit_button("Scrape & Upload Images")
        
        # Define a function to process uploaded files
        def process_uploaded_files(files, usage, custom_name=None, progress_bar=None, status_text=None):
            """Process uploaded files and add them to the image pool"""
            results = []
            
            # Create progress tracking if not provided
            if progress_bar is None:
                progress_bar = st.progress(0)
            if status_text is None:
                status_text = st.empty()
            
            # Process each uploaded file
            for i, file_data in enumerate(files):
                try:
                    # Update progress
                    progress = (i / len(files))
                    progress_bar.progress(progress)
                    
                    if isinstance(file_data, tuple):
                        # This is from URL scraping (filename, file_content)
                        original_filename, file_content = file_data
                        status_text.text(f"Processing image {i+1} of {len(files)}: {original_filename}")
                    else:
                        # This is from direct upload
                        original_filename = file_data.name
                        file_content = file_data.getbuffer()
                        status_text.text(f"Processing image {i+1} of {len(files)}")
                    
                    # Generate a unique ID for the image
                    image_id = str(uuid.uuid4())
                    
                    # Create filename with original extension
                    file_ext = os.path.splitext(original_filename)[1]
                    if not file_ext:  # Ensure we have an extension
                        file_ext = '.jpg'  # Default to jpg if no extension
                    filename = f"{image_id}{file_ext}"
                    
                    # Save image to disk
                    save_path = os.path.join(image_dir, filename)
                    with open(save_path, "wb") as f:
                        f.write(file_content)
                    
                    # For multiple files, append the index to custom name if provided
                    display_name = original_filename
                    if custom_name:
                        if len(files) > 1:
                            # Use format: custom_name (1), custom_name (2), etc.
                            display_name = f"{custom_name} ({i+1})"
                        else:
                            display_name = custom_name
                    
                    results.append((filename, original_filename, save_path, display_name))
                except Exception as e:
                    st.error(f"Error processing image {original_filename}: {str(e)}")
            
            # Update progress to completion
            progress_bar.progress(1.0)
            return results
        
        def process_image_metadata(results, usage):
            """Process metadata for uploaded images"""
            for filename, original_filename, save_path, display_name in results:
                try:
                    # Generate thumbnail and get dimensions
                    img = Image.open(save_path)
                    width, height = img.size
                    
                    # Create thumbnail
                    img.thumbnail((100, 100))
                    thumb_buffer = io.BytesIO()
                    img.save(thumb_buffer, format="PNG")
                    thumbnail_b64 = base64.b64encode(thumb_buffer.getvalue()).decode('utf-8')
                    
                    # Add metadata
                    image_metadata['images'].append({
                        'id': filename.split('.')[0],  # Use filename without extension as ID
                        'filename': filename,
                        'original_filename': original_filename,
                        'name': display_name,
                        'upload_date': datetime.now().isoformat(),
                        'categories': [],
                        'usage': usage,
                        'notes': "",
                        'width': width,
                        'height': height,
                        'thumbnail': thumbnail_b64
                    })
                except Exception as e:
                    st.error(f"Error processing metadata for {original_filename}: {str(e)}")
            
            # Save updated metadata
            with open(metadata_file, 'w') as f:
                json.dump(image_metadata, f, indent=2)
        
        # Function to scrape images from a URL
        def scrape_images_from_url(url, image_types, min_size):
            """Scrape images from a URL and return them as file-like objects"""
            try:
                # Download the webpage
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
                }
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()  # Raise an exception for HTTP errors
                
                # Parse the HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find all image tags
                img_tags = soup.find_all('img')
                
                # Progress indicators
                st.info(f"Found {len(img_tags)} image tags on the page. Processing...")
                img_progress = st.progress(0)
                img_status = st.empty()
                
                # Process each image
                image_files = []
                for i, img in enumerate(img_tags):
                    img_progress.progress((i + 1) / len(img_tags))
                    img_status.text(f"Processing image {i+1} of {len(img_tags)}")
                    
                    # Get image URL
                    img_url = img.get('src')
                    if not img_url:
                        continue
                    
                    # Make URL absolute
                    img_url = urljoin(url, img_url)
                    
                    # Check file extension
                    parsed_url = urlparse(img_url)
                    ext = os.path.splitext(parsed_url.path)[1].lower().lstrip('.')
                    
                    # Skip if not in desired image types
                    if ext not in image_types and f".{ext}" not in image_types:
                        continue
                    
                    try:
                        # Download image
                        img_response = requests.get(img_url, headers=headers, timeout=5)
                        img_response.raise_for_status()
                        
                        # Check image size using PIL
                        with Image.open(io.BytesIO(img_response.content)) as img:
                            width, height = img.size
                            if width < min_size or height < min_size:
                                continue  # Skip small images
                        
                        # Create a filename from URL
                        img_filename = os.path.basename(parsed_url.path)
                        if not img_filename or '.' not in img_filename:
                            img_filename = f"scraped_image_{i}.{ext if ext else 'jpg'}"
                        
                        # Add to results
                        image_files.append((img_filename, img_response.content))
                        
                    except Exception as e:
                        st.error(f"Error downloading image {img_url}: {str(e)}")
                        continue
                
                img_progress.progress(1.0)
                img_status.empty()
                
                return image_files
                
            except Exception as e:
                st.error(f"Error scraping URL {url}: {str(e)}")
                return []
        
        # Handle local file uploads
        if submit_upload and uploaded_files:
            # Create progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Process uploaded files
            results = process_uploaded_files(uploaded_files, template_usage, image_name, progress_bar, status_text)
            
            # Process metadata
            process_image_metadata(results, template_usage)
            
            status_text.text("Upload complete!")
            st.success(f"Successfully uploaded {len(results)} images!")
            st.rerun()
        
        # Handle URL scraping
        if submit_scrape and url_to_scrape:
            if not image_types:
                st.error("Please select at least one image type to download")
            else:
                with st.spinner(f"Scraping images from {url_to_scrape}..."):
                    # Scrape images from URL
                    scraped_images = scrape_images_from_url(url_to_scrape, image_types, min_size)
                    
                    if scraped_images:
                        # Create progress indicators
                        progress_bar = st.progress(0)
                        status_text = st.empty()
                        
                        # Process scraped images
                        results = process_uploaded_files(scraped_images, url_template_usage, None, progress_bar, status_text)
                        
                        # Process metadata
                        process_image_metadata(results, url_template_usage)
                        
                        status_text.text("Upload complete!")
                        st.success(f"Successfully uploaded {len(results)} images from {url_to_scrape}!")
                        st.rerun()
                    else:
                        st.warning(f"No suitable images found at {url_to_scrape}")
                    

    
    with tab2:
        st.write("### View & Manage Images")
        
        # Add a delete all button with a red background
        delete_all_col1, delete_all_col2 = st.columns([3, 1])
        with delete_all_col1:
            st.write("#### Manage All Images")
        with delete_all_col2:
            if st.button("🗑️ Delete All Images", type="primary", use_container_width=True, key="btn_delete_all"):
                st.session_state.show_delete_all_confirmation = True
        
        # Show confirmation dialog for delete all
        if st.session_state.get('show_delete_all_confirmation', False):
            with st.container():
                st.warning(f"⚠️ Are you sure you want to delete ALL images? This action cannot be undone.")
                conf_col1, conf_col2 = st.columns(2)
                with conf_col1:
                    if st.button("Yes, Delete All Images", type="primary", key="confirm_delete_all"):
                        try:
                            # Delete all image files
                            deleted_count = 0
                            for img in image_metadata['images']:
                                # Delete the actual file
                                image_path = os.path.join(image_dir, img['filename'])
                                if os.path.exists(image_path):
                                    os.remove(image_path)
                                    deleted_count += 1
                            
                            # Clear the metadata
                            image_metadata['images'] = []
                            
                            # Save updated metadata
                            with open(metadata_file, 'w') as f:
                                json.dump(image_metadata, f, indent=2)
                            
                            st.session_state.show_delete_all_confirmation = False
                            st.success(f"Successfully deleted all {deleted_count} images!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"Error deleting images: {str(e)}")
                with conf_col2:
                    if st.button("Cancel", key="cancel_delete_all"):
                        st.session_state.show_delete_all_confirmation = False
                        st.rerun()
        
        # Filtering options
        st.write("#### Filter Images")
        
        # Get all product names for usage filter
        org_url = None
        if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
            org_url = st.session_state.current_user.get('organization', {}).get('url', None)
            
        # Get organization filter toggle setting
        org_filter_enabled = st.session_state.get('feature_toggles', {}).get('org_products_visibility', True)
        
        # Get all products
        all_products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)
        
        # Extract product names
        product_names = ["All Products"] 
        if all_products:
            if isinstance(all_products, list):
                for product in all_products:
                    if isinstance(product, dict) and 'Product_Name' in product:
                        product_names.append(product['Product_Name'])
        
        # Usage filter
        filter_usage = st.multiselect(
            "Filter by Product Usage",
            options=product_names,
            help="Select products to filter images"
        )
        
        # Search by name
        search_term = st.text_input("Search by Name", help="Enter text to search in image names")
        
        # Apply filters
        filtered_images = image_metadata['images']
        
        if filter_usage:
            filtered_images = [img for img in filtered_images if img.get('usage', '') in filter_usage]
        
        if search_term:
            search_term = search_term.lower()
            filtered_images = [img for img in filtered_images if search_term in img.get('name', '').lower() 
                              or search_term in img.get('original_filename', '').lower()
                              or search_term in img.get('notes', '').lower()]
        
        # Display images in a grid
        if not filtered_images:
            st.info("No images found matching the selected filters.")
        else:
            st.write(f"#### Showing {len(filtered_images)} Images")
            
            # Display images in a grid - 4 columns
            cols = st.columns(4)
            
            for i, img in enumerate(filtered_images):
                col_idx = i % 4
                with cols[col_idx]:
                    # Create a container for each image
                    with st.container():
                        # Display thumbnail if available
                        if 'thumbnail' in img and img['thumbnail']:
                            st.markdown(f"<img src='data:image/png;base64,{img['thumbnail']}' style='width:100%; max-width:150px; margin-bottom:5px;'>", unsafe_allow_html=True)
                        else:
                            # Display filename if no thumbnail
                            st.write(f"📄 {img.get('original_filename', 'Unknown')}")
                        
                        # Display image details
                        st.write(f"**{img.get('name', 'Unnamed')}**")
                        st.write(f"Size: {img.get('width', 0)}×{img.get('height', 0)} pixels")
                        file_size = os.path.getsize(os.path.join(image_dir, img['filename'])) if os.path.exists(os.path.join(image_dir, img['filename'])) else 0
                        st.write(f"File size: {file_size/1024:.1f} KB")
                        
                        # Categories as pills
                        if 'categories' in img and img['categories']:
                            cats_html = ' '.join([f"<span style='background-color:#f0f0f0; padding:2px 6px; border-radius:10px; font-size:0.8em; margin-right:4px;'>{cat}</span>" for cat in img['categories'][:2]])
                            if len(img['categories']) > 2:
                                cats_html += f"<span style='background-color:#f0f0f0; padding:2px 6px; border-radius:10px; font-size:0.8em;'>+{len(img['categories'])-2}</span>"
                            st.markdown(f"{cats_html}", unsafe_allow_html=True)
                        
                        # Action buttons
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button("🔍 View", key=f"view_{img['id']}"):
                                st.session_state.selected_image = img
                        with col2:
                            if st.button("🗑️ Delete", key=f"delete_{img['id']}"):
                                # Store ID for deletion confirmation
                                st.session_state.delete_image_id = img['id']
                                st.session_state.delete_image_name = img.get('name', img.get('original_filename', 'this image'))
            
            # Handle image view
            if 'selected_image' in st.session_state:
                img = st.session_state.selected_image
                
                with st.popover("Image Details", use_container_width=True):
                    col1, col2 = st.columns([1, 1])
                    
                    with col1:
                        # Display image
                        image_path = os.path.join(image_dir, img['filename'])
                        if os.path.exists(image_path):
                            displayed_image = Image.open(image_path)
                            st.image(displayed_image, use_column_width=True)
                    
                    with col2:
                        # Display metadata
                        st.write(f"### {img.get('name', 'Unnamed')}")
                        st.write(f"**Original Filename:** {img.get('original_filename', 'Unknown')}")
                        st.write(f"**Upload Date:** {datetime.fromisoformat(img.get('upload_date', '')).strftime('%Y-%m-%d %H:%M:%S')}")
                        st.write(f"**Dimensions:** {img.get('width', 0)}×{img.get('height', 0)}")
                        st.write(f"**Usage:** {img.get('usage', 'Unknown')}")
                        
                        if 'categories' in img and img['categories']:
                            st.write("**Categories:**")
                            for cat in img['categories']:
                                st.write(f"- {cat}")
                        
                        if 'notes' in img and img['notes']:
                            st.write("**Notes:**")
                            st.write(img['notes'])
                        
                        # Copy image path button
                        image_url = f"data/images/{img['filename']}"
                        if st.button("📋 Copy Image Path"):
                            st.code(image_url, language="text")
                            st.success("Image path copied!")
                        
                        # Close button
                        if st.button("Close"):
                            del st.session_state.selected_image
                            st.rerun()
            
            # Handle image deletion
            if 'delete_image_id' in st.session_state:
                with st.popover(f"Confirm deletion of {st.session_state.delete_image_name}?", use_container_width=True):
                    st.warning("This action cannot be undone.")
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("🗑️ Yes, Delete"):
                            # Find the image in metadata
                            image_id = st.session_state.delete_image_id
                            for i, img in enumerate(image_metadata['images']):
                                if img['id'] == image_id:
                                    # Delete the actual file
                                    image_path = os.path.join(image_dir, img['filename'])
                                    if os.path.exists(image_path):
                                        os.remove(image_path)
                                    
                                    # Remove from metadata
                                    del image_metadata['images'][i]
                                    
                                    # Save updated metadata
                                    with open(metadata_file, 'w') as f:
                                        json.dump(image_metadata, f, indent=2)
                                    
                                    st.success(f"Image '{st.session_state.delete_image_name}' deleted successfully!")
                                    
                                    # Clean up session state
                                    del st.session_state.delete_image_id
                                    del st.session_state.delete_image_name
                                    if 'selected_image' in st.session_state:
                                        del st.session_state.selected_image
                                    
                                    st.rerun()
                                    break
                    with col2:
                        if st.button("❌ Cancel"):
                            del st.session_state.delete_image_id
                            del st.session_state.delete_image_name
                            st.rerun()
    
    
    # Add instructions on how to use images in templates
    with st.expander("How to Use Images in Templates"):
        st.write("""
        ### Using Images in Email Templates
        
        You can include images in your email templates using the image path in the HTML:
        
        ```html
        <img src="{BASE_URL}/data/images/your_image_filename.jpg" alt="Image description">
        ```
        
        The `{BASE_URL}` will be automatically replaced with the appropriate URL when the email is sent.
        
        ### Image Sizing Guidelines
        
        - **Header Images**: 600px width, 200px height
        - **Product Images**: 300px × 300px
        - **Logo Images**: 150px × 50px
        - **Banner Images**: 600px × 100px
        
        For best results, maintain these aspect ratios in your images.
        """)
