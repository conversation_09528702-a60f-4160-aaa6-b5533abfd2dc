"""
Competitive analysis UI component for OpenEngage.
"""
import os
import json
import pandas as pd
import streamlit as st
from datetime import datetime

from core.competitive_analysis import (
    save_competitive_analysis,
    load_competitive_analysis,
    create_comparison_dataframe,
    summarize_comparison
)
from utils.file_utils import load_product_details, get_all_products
from utils.competitor_tools import CompanyCompetitorFinder, CompetitorProductAnalyzer

def display_competitive_analysis():
    """Display competitive analysis dashboard"""
    st.title("🔍 Competitive Analysis")

    # Initialize session state variables if they don't exist
    if "competitor_urls" not in st.session_state:
        st.session_state.competitor_urls = []
    if "competitor_data" not in st.session_state:
        st.session_state.competitor_data = {}
    if "analysis_complete" not in st.session_state:
        st.session_state.analysis_complete = False
    if "selected_product" not in st.session_state:
        st.session_state.selected_product = None

    # Create tabs for different steps of the analysis
    tab1, tab2 = st.tabs(["Competitor Search", "Comparison Analysis"])

    with tab1:
        st.header("Find Competitors")

        # Check if organization products visibility is enabled
        org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

        # Get organization URL from session state if available
        org_url = None
        if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
            org_url = st.session_state.current_user.get('organization', {}).get('url', None)

        # Load product details for the dropdown with optional filtering
        products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)

        if not products:
            if org_filter_enabled and org_url:
                st.warning(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
            else:
                st.warning("No products found in product_details.json. Please add products first.")
        else:
            # Create product selection dropdown
            product_names = [p.get("Product_Name", "") for p in products]
            selected_product_name = st.selectbox(
                "Select Your Product",
                options=product_names,
                key="product_selector"
            )

            # Store the selected product in session state
            st.session_state.selected_product = selected_product_name

            # Get the selected product details
            product = next((p for p in products if p.get("Product_Name") == selected_product_name), None)

            if product:
                # Display product information
                st.write(f"**Product:** {product.get('Product_Name')}")
                st.write(f"**Company:** {product.get('Company_Name')}")
                st.write(f"**Type:** {product.get('Type_of_Product')}")

                # Create two columns for the competitor search options
                col1, col2 = st.columns(2)

                with col1:
                    st.subheader("Option 1: Find Competitors")
                    # Number of competitors to find
                    num_competitors = st.radio(
                        "Number of competitors to find",
                        options=[1, 3, 5],
                        horizontal=True,
                        index=1  # Default to 3
                    )

                    # Button to find competitors
                    if st.button("🔍 Find Competitors", type="primary"):
                        with st.spinner(f"Finding {num_competitors} competitors for {selected_product_name}..."):
                            # Initialize the competitor finder
                            finder = CompanyCompetitorFinder()

                            # Find competitors
                            competitors = finder.find_competitors(
                                company_name=product.get("Company_Name", ""),
                                product_name=product.get("Product_Name", ""),
                                product_type=product.get("Type_of_Product", ""),
                                max_competitors=num_competitors
                            )

                            if competitors:
                                # Store the competitors in session state
                                st.session_state.competitor_urls = competitors
                                st.success(f"Found {len(competitors)} competitors!")
                            else:
                                st.error("No competitors found. Please try again.")

                with col2:
                    st.subheader("Option 2: Add Competitors Manually")
                    # Form for adding competitor URLs manually
                    with st.form(key="competitor_form"):
                        st.markdown("Enter competitor details")
                        competitor_name = st.text_input("Competitor Name")
                        competitor_url = st.text_input("Competitor URL", placeholder="https://example.com")
                        submit_button = st.form_submit_button(label="Add Competitor")

                        if submit_button and competitor_name and competitor_url:
                            # Validate URL
                            if not competitor_url.startswith("http"):
                                competitor_url = "https://" + competitor_url

                            # Add to session state
                            new_competitor = {"name": competitor_name, "url": competitor_url}

                            # Check if already exists
                            exists = False
                            for comp in st.session_state.competitor_urls:
                                if comp.get("url") == competitor_url:
                                    exists = True
                                    break

                            if not exists:
                                st.session_state.competitor_urls.append(new_competitor)
                                st.success(f"Added {competitor_name} to competitors list!")
                            else:
                                st.warning(f"Competitor with URL {competitor_url} already exists in the list.")
            else:
                st.error("Could not find the selected product in product_details.json.")

        # Display current list of competitors
        if st.session_state.competitor_urls:
            st.subheader("Selected Competitors")

            # Create a container for the competitor list
            competitor_container = st.container()

            with competitor_container:
                for i, competitor in enumerate(st.session_state.competitor_urls):
                    col1, col2 = st.columns([5, 1])
                    with col1:
                        st.write(f"**{competitor.get('name', 'Unknown')}** - [{competitor.get('url', '#')}]({competitor.get('url', '#')})")
                    with col2:
                        if st.button("❌", key=f"remove_{i}"):
                            st.session_state.competitor_urls.pop(i)
                            st.rerun()

            # Button to analyze competitors
            if st.button("Analyze Competitors", type="primary"):
                with st.spinner("Analyzing competitors... This may take a few minutes."):
                    # Get the selected product
                    product_name = st.session_state.selected_product

                    if not product_name:
                        st.error("No product selected. Please select a product first.")
                    else:
                        # Get product details
                        our_product = load_product_details(product_name)

                        if not our_product:
                            st.error(f"Could not load details for product: {product_name}")
                        else:
                            # Initialize the product analyzer
                            analyzer = CompetitorProductAnalyzer()

                            # Run the analysis
                            results = analyzer.analyze_competitors(
                                competitors=st.session_state.competitor_urls,
                                our_product=our_product
                            )

                            # Store the results in session state
                            st.session_state.competitor_data = results
                            st.session_state.analysis_complete = True

                            # Save the analysis data
                            filename = save_competitive_analysis(results)
                            st.success(f"Analysis saved to {filename}")

                            # Switch to the comparison tab
                            st.rerun()

    with tab2:
        st.header("Competitor Comparison")

        # Check if we have analysis data
        if not st.session_state.analysis_complete and not st.session_state.competitor_data:
            # Try to load previous analyses
            analysis_files = os.listdir("data/competitive_analysis") if os.path.exists("data/competitive_analysis") else []
            analysis_files = [f for f in analysis_files if f.endswith('.json')]

            if analysis_files:
                st.subheader("Load Previous Analysis")
                # Sort by modification time (most recent first)
                analysis_files.sort(key=lambda x: os.path.getmtime(f"data/competitive_analysis/{x}"), reverse=True)
                # Create a dropdown to select a file
                selected_file = st.selectbox("Select a previous analysis", analysis_files)
                if st.button("Load Selected Analysis"):
                    # Load the selected file
                    analysis_data = load_competitive_analysis(selected_file)
                    if analysis_data:
                        st.session_state.competitor_data = analysis_data
                        st.session_state.analysis_complete = True
                        st.rerun()
                    else:
                        st.error("Failed to load the selected analysis file.")

            st.info("Please find and analyze competitors in the 'Competitor Search' tab first.")
        else:
            # Load product details
            product_name = st.session_state.selected_product
            if not product_name:
                # Try to get a product from product_details.json
                products = load_product_details()
                if products:
                    product_name = products[0].get("Product_Name", "")

            if not product_name:
                st.error("No product selected. Please select a product in the 'Competitor Search' tab.")
            else:
                # Load our product details
                our_product = load_product_details(product_name)

                if not our_product:
                    st.error(f"Could not load details for product: {product_name}")
                else:
                    # Display product information
                    st.subheader("Your Product")
                    st.write(f"**Product:** {our_product.get('Product_Name')}")
                    st.write(f"**Company:** {our_product.get('Company_Name')}")
                    st.write(f"**Type:** {our_product.get('Type_of_Product')}")

                    # Create comparison dataframe
                    df = create_comparison_dataframe(
                        analysis_data=st.session_state.competitor_data,
                        our_product=our_product
                    )

                    if df.empty:
                        st.warning("No competitor products found in the analysis. Please analyze competitors first.")
                    else:
                        # Display the comparison table
                        st.subheader(f"Comparison: {our_product.get('Product_Name')} vs. Competitors")
                        st.markdown("*Scroll horizontally to see all features and specifications*")

                        # Configure column formatting for URLs to be clickable
                        st.dataframe(
                            df,
                            use_container_width=True,
                            height=400,
                            column_config={
                                "Product URL": st.column_config.LinkColumn(
                                    "Product URL",
                                    help="Direct link to the product page",
                                    width="medium",
                                    validate="^https?://.*"
                                )
                            }
                        )

                        # Add download button for the comparison
                        csv = df.to_csv(index=False)
                        st.download_button(
                            label="Download Comparison as CSV",
                            data=csv,
                            file_name=f"{our_product.get('Product_Name')}_comparison.csv",
                            mime="text/csv",
                            key="download_competitor_comparison"
                        )

                        # Generate and display summary
                        with st.spinner("Generating comparison summary..."):
                            summary = summarize_comparison(
                                df=df,
                                our_product_name=our_product.get('Product_Name')
                            )

                            st.subheader("Comparison Summary")
                            st.markdown(summary)
