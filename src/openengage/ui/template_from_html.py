#!/usr/bin/env python
# coding: utf-8

"""
HTML Template Generator for OpenEngage
Generates personalized email templates from HTML templates using AI agents.
"""

from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field
from typing import List, Dict
import os
import pandas as pd
from bs4 import BeautifulSoup
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Use environment variable for OpenAI API key
llm = 'openai/gpt-4o-mini'

# Pydantic models for structured outputs
class HTMLSection(BaseModel):
    section_id: str = Field(description="Unique identifier for the section (e.g., section_1, section_2)")
    section_type: str = Field(description="Type of section (header, main_content, cta, footer, etc.)")
    current_text: str = Field(description="Current text content in the section")
    html_element: str = Field(description="HTML element type (h1, p, div, etc.)")
    text_color: str = Field(description="Text color from CSS styling (e.g., #333, white, etc.)")
    background_color: str = Field(description="Background color from CSS styling (e.g., #f8f9fa, transparent, etc.)")
    context: str = Field(description="What this section is meant to convey")

class HTMLAnalysis(BaseModel):
    sections: List[HTMLSection] = Field(description="List of all content sections found in HTML")
    total_sections: int = Field(description="Total number of content sections identified")

class SectionContent(BaseModel):
    section_id: str = Field(description="ID matching the section from HTML analysis")
    new_content: str = Field(description="Generated content for this section")
    content_type: str = Field(description="Type of content (headline, body, cta, etc.)")

class GeneratedContent(BaseModel):
    section_contents: List[SectionContent] = Field(description="Generated content for each section")
    product_focus: str = Field(description="Main product focus of the generated content")
    product_link: str = Field(description="Link of the product")

class FinalHTML(BaseModel):
    updated_html: str = Field(description="Complete HTML with updated content")
    sections_updated: List[str] = Field(description="List of section IDs that were updated")
    changes_summary: str = Field(description="Summary of content changes made")

# Define the agents
html_analyzer = Agent(
    role="HTML Section Analyzer",
    goal="Identify and catalog all content sections in HTML email templates",
    backstory="""You are an expert HTML analyst specializing in email templates. You can
    parse HTML structure, identify content sections, and understand the purpose of each
    section within the email layout. You focus on text content areas that need to be
    replaced with new marketing copy.""",
    llm=llm,
    verbose=True,
    allow_delegation=False
)

content_generator = Agent(
    role="Email Content Creator",
    goal="Generate targeted email content for each identified section based on product details",
    backstory="""You are a professional email copywriter with expertise in creating
    compelling, conversion-focused content. You adapt your writing style to match different
    section types (headlines, body text, CTAs) and ensure consistency across all sections
    while highlighting product benefits effectively.""",
    llm=llm,
    verbose=True,
    allow_delegation=False
)

html_assembler = Agent(
    role="HTML Content Replacer",
    goal="Replace existing content in HTML with newly generated content while preserving structure",
    backstory="""You are a technical specialist in HTML email development. You excel at
    precisely replacing text content in HTML templates while maintaining all formatting,
    styling, and structural elements. You ensure the final HTML is clean and functional. 
    Remember you should also replace the any embedded link""",
    llm=llm,
    verbose=True,
    allow_delegation=False
)

# Define the tasks
analyze_html_task = Task(
    description="""
    Analyze the provided HTML email template and identify all content sections that need text replacement.

    HTML Template: {html_string}

    Your analysis should:
    1. Parse the HTML and identify all text content areas (headings, paragraphs, buttons, etc.)
    2. Assign unique section IDs (section_1, section_2, etc.) to each content area
    3. Classify each section type (header, main_content, cta, footer, etc.)
    4. Extract the current text content from each section
    5. Identify the HTML element type (h1, p, div, span, etc.)
    6. Extract text color and background color from CSS styling for each section
    7. Determine the purpose/context of each section

    IMPORTANT: Pay special attention to color styling:
    - Extract text color from 'color' CSS property or inline styles
    - Extract background color from 'background-color' or 'background' CSS property
    - Note if colors are inherited from parent elements
    - Record colors in any format found (hex, rgb, named colors, etc.)

    Focus only on sections containing text content that should be replaced with new marketing copy.
    Ignore structural elements, styling, and non-content areas.
    """,
    agent=html_analyzer,
    expected_output="Complete analysis of all content sections with section IDs, color information, and details",
    output_pydantic=HTMLAnalysis
)

generate_content_task = Task(
    description="""
    Generate new email content for each section identified in the HTML analysis.

    HTML Analysis: Use the section analysis from the previous task
    Product Details: {product_details}

    For each section in the analysis:
    1. Create compelling content that matches the section type and context
    2. Tailor content length and style to the section purpose:
       - Headers: Short, attention-grabbing headlines
       - Main content: Detailed product benefits and features
       - CTAs: Action-oriented, persuasive text
       - Footer: Brief, professional closing
    3. Ensure all content focuses on the provided product details
    4. Maintain consistent tone and messaging across all sections
    5. Make content email-marketing optimized for engagement and conversion
    6. Identify the product link and return it along with the content.
    7. Also generate the content for the table section or <tr>, <td>.

    Generate exactly one piece of content for each section ID from the HTML analysis.
    """,
    agent=content_generator,
    expected_output="New content generated for each section, matching the structure from HTML analysis",
    output_pydantic=GeneratedContent
)

assemble_html_task = Task(
    description="""
    Replace the existing text content in the original HTML with the newly generated content.

    Original HTML: {html_string}
    HTML Section Analysis: from the first task
    Generated Content: from the previous task

    Your process:
    1. Take the original HTML template
    2. For each section ID in the generated content, locate the corresponding text in the original HTML
    3. Replace the old text with the new generated content, ensure none of the old text or title is still present in the HTML.
    4. Preserve all HTML structure, tags, attributes, styling, and formatting
    5. Ensure no HTML elements are broken or modified
    6. Maintain the exact same HTML structure with only text content updated
    7. Replace the old link with the product link
    8. Also replace the text within table or <tr>, <td> with the relevant information.
    
    CRITICAL: Preserve all color styling:
    - Keep all existing text colors (color CSS property) exactly as they were
    - Keep all existing background colors (background-color, background CSS properties) exactly as they were
    - Maintain all inline styles and CSS classes that control colors
    - Do not modify any color-related styling or attributes
    - Ensure the visual appearance remains identical except for text content

    Return the complete updated HTML with all text content replaced but structure and colors intact.
    """,
    agent=html_assembler,
    expected_output="Complete HTML template with updated content, preserving all original structure, styling, and colors",
    context=[analyze_html_task, generate_content_task],
    output_pydantic=FinalHTML
)

# Create the crew
email_template_crew = Crew(
    agents=[html_analyzer, content_generator, html_assembler],
    tasks=[analyze_html_task, generate_content_task, assemble_html_task],
    process=Process.sequential,
    verbose=True
)

# Sample product details for testing (if needed)
sample_product_details = """
Product: iPhone 16 Pro
Features:
- A18 Pro chip with next-gen performance
- 6.3" Super Retina XDR display with ProMotion
- Advanced quad-camera system with 5x optical zoom
- Titanium body for enhanced durability
- USB-C support and faster charging
- iOS 18 with AI-enhanced features
Price: $999
Target audience: Power users, mobile creators, and Apple enthusiasts
Key benefits: Professional-grade photography, smooth performance, premium design
Product link: https://www.apple.com/iphone-16-pro
"""

def run_email_generation(html_template, product_info):
    """
    Generate personalized email content by replacing HTML template text with product-specific content.

    Args:
        html_template (str): HTML email template
        product_info (str): Product details and information

    Returns:
        CrewOutput: Final result with updated HTML
    """
    result = email_template_crew.kickoff(inputs={
        'html_string': html_template,
        'product_details': product_info
    })
    return result

# Execute the crew (for testing purposes)
if __name__ == "__main__":
    # This section is for testing only
    # In production, use the run_email_generation function directly
    print("HTML Template Generator module loaded successfully!")
    print("Use run_email_generation(html_template, product_info) to generate templates.")
