"""
Analytics dashboard UI component for OpenEngage.
"""
import streamlit as st
import pandas as pd
import json
import os
from pathlib import Path
import datetime
from analytics import (
    process_engagement_data,
    create_engagement_plot,
    process_user_journey_data,
    create_user_journey_funnel_chart,
    create_campaign_prediction_visualization
)
from utils.file_utils import get_all_products
from core.mail_performance_analyzer import MailPerformanceAnalyzer
from core.campaign_predictor import CampaignPredictor
from ui.analytics_story import display_analytics_story

def display_analytics():
    """Display the analytics dashboard."""
    # Create a header with buttons in the right corner
    col1, col2 = st.columns([3, 1])
    with col1:
        st.write("### Email Analytics Dashboard")
    with col2:
        analyze_campaigns = st.button("📊 Analyze Campaigns", help="Analyze mail campaigns sent using OpenEngage")

    # Introduction and overview section - simplified
    st.markdown("""
    #### 📈 Your Email Marketing Analytics

    This dashboard provides insights into your campaign performance and audience engagement.

    Upload your data or click "Analyze Campaigns" to begin.
    """)

    # Add a visual divider
    st.markdown("<hr style='margin: 15px 0; height: 1px; border: none; background-color: #f0f0f5;'>", unsafe_allow_html=True)

    # Process campaign analysis if button is clicked
    if analyze_campaigns:
        with st.spinner("Analyzing campaigns... This may take a few minutes."):
            try:
                # Create an instance of the MailPerformanceAnalyzer
                analyzer = MailPerformanceAnalyzer()

                # Analyze all campaigns
                performance_data = analyzer.analyze_all_campaigns()
                # performance_data=pd.DataFrame()
                if performance_data.empty:
                    st.warning("No campaign data found. Please make sure there are CSV files in the data/campaign_results folder.")
                else:
                    # Get performance summary
                    summary = analyzer.get_performance_summary()

                    # Store the summary in session state for later use
                    st.session_state.campaign_summary = summary
                    st.session_state.campaign_analyzed = True
                    year_month=datetime.datetime.now().strftime("%Y%m")
                    # Store the performance data path in session state
                    performance_file = Path(f"data/mail_performance/combined/all_performance_{year_month}.csv")
                    if performance_file.exists():
                        st.session_state.performance_file_path = str(performance_file)

                    st.success("Campaign analysis completed successfully!")

                    # Rerun the app to show the results
                    st.rerun()
            except Exception as e:
                st.error(f"Error analyzing campaigns: {str(e)}")

    # Main content section
    st.write("## Analytics Dashboard")

    # File uploader at the top
    uploaded_file = st.file_uploader("Upload CSV file", type=['csv'], key="analytics_csv")

    # Create tabs for different analytics sections
    email_engagement_tab, analytics_story_tab = st.tabs(["Email Performance & User Journey", "Analytics Story"])

    # Process uploaded file or show campaign analysis results
    with email_engagement_tab:
        if uploaded_file is not None or (hasattr(st.session_state, 'campaign_analyzed') and st.session_state.campaign_analyzed):
            # Section divider
            st.write("---")

            # Email Engagement Section
            st.write("### Email Engagement Analysis")

        # Display campaign analysis results if available
        if hasattr(st.session_state, 'campaign_analyzed') and st.session_state.campaign_analyzed:
            summary = st.session_state.campaign_summary

            # Display summary metrics with concise context
            st.markdown("## 📊 Campaign Performance Metrics")

            # Create metrics with contextual information
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Emails", f"{summary['total_emails']:,}")
                st.markdown("<div style='font-size: 0.8em; color: #666;'>Your campaign reach</div>", unsafe_allow_html=True)

            with col2:
                # Add color coding based on industry benchmarks
                open_rate = summary['open_rate']
                if open_rate > 25:
                    open_rate_color = "green"
                    open_rate_context = "Excellent! Above industry average"
                elif open_rate > 15:
                    open_rate_color = "orange"
                    open_rate_context = "Good, near industry average"
                else:
                    open_rate_color = "red"
                    open_rate_context = "Below industry average"

                st.metric("Open Rate", f"{open_rate:.2f}%", delta=f"{open_rate - 20:.2f}%" if open_rate != 20 else None, delta_color="normal")
                st.markdown(f"<div style='font-size: 0.8em; color: {open_rate_color};'>{open_rate_context}</div>", unsafe_allow_html=True)

            with col3:
                # Add color coding based on industry benchmarks
                click_rate = summary['click_rate']
                if click_rate > 5:
                    click_rate_color = "green"
                    click_rate_context = "Excellent! Above industry average"
                elif click_rate > 2:
                    click_rate_color = "orange"
                    click_rate_context = "Good, near industry average"
                else:
                    click_rate_color = "red"
                    click_rate_context = "Below industry average"

                st.metric("Click Rate", f"{click_rate:.2f}%", delta=f"{click_rate - 2.5:.2f}%" if click_rate != 2.5 else None, delta_color="normal")
                st.markdown(f"<div style='font-size: 0.8em; color: {click_rate_color};'>{click_rate_context}</div>", unsafe_allow_html=True)

            with col4:
                # Add color coding based on industry benchmarks
                unsub_rate = summary['unsub_rate']
                if unsub_rate < 0.1:
                    unsub_rate_color = "green"
                    unsub_rate_context = "Excellent! Below industry average"
                elif unsub_rate < 0.5:
                    unsub_rate_color = "orange"
                    unsub_rate_context = "Good, near industry average"
                else:
                    unsub_rate_color = "red"
                    unsub_rate_context = "Above industry average"

                st.metric("Unsubscribe Rate", f"{unsub_rate:.2f}%", delta=f"{0.2 - unsub_rate:.2f}%" if unsub_rate != 0.2 else None, delta_color="inverse")
                st.markdown(f"<div style='font-size: 0.8em; color: {unsub_rate_color};'>{unsub_rate_context}</div>", unsafe_allow_html=True)

            # Display more detailed metrics with insights
            with st.expander("📊 Detailed Campaign Metrics & Insights", expanded=False):
                st.markdown("### Additional Campaign Metrics")

                col1, col2 = st.columns(2)

                with col1:
                    st.metric("Total Opens", f"{summary['open_count']:,}")
                    st.markdown("<div style='font-size: 0.8em; color: #666;'>The number of times your emails were opened</div>", unsafe_allow_html=True)

                    st.metric("Total Clicks", f"{summary['click_count']:,}")
                    st.markdown("<div style='font-size: 0.8em; color: #666;'>The number of times recipients clicked links in your emails</div>", unsafe_allow_html=True)

                with col2:
                    # Add context to click-to-open rate
                    ctor = summary['click_to_open_rate']
                    if ctor > 20:
                        ctor_color = "green"
                        ctor_context = "Excellent engagement with your content"
                    elif ctor > 10:
                        ctor_color = "orange"
                        ctor_context = "Good engagement, room for improvement"
                    else:
                        ctor_color = "red"
                        ctor_context = "Content may not be resonating with openers"

                    st.metric("Click-to-Open Rate", f"{ctor:.2f}%")
                    st.markdown(f"<div style='font-size: 0.8em; color: {ctor_color};'>{ctor_context}</div>", unsafe_allow_html=True)

                    st.metric("Unsubscribes", f"{summary['unsub_count']:,}")
                    st.markdown("<div style='font-size: 0.8em; color: #666;'>The number of recipients who opted out</div>", unsafe_allow_html=True)

                # Add insights and recommendations in tabs
                insight_tab, recommendation_tab = st.tabs(["💡 Key Insights", "🚀 Recommendations"])

                with insight_tab:
                    # Calculate insights based on the metrics
                    insights = []

                    # Open rate insights
                    if summary['open_rate'] < 15:
                        insights.append("📧 **Subject Line**: Below average - test new subject lines")
                    elif summary['open_rate'] > 25:
                        insights.append("📧 **Subject Lines**: Strong performance")

                    # Click rate insights
                    if summary['click_rate'] < 2:
                        insights.append("🔗 **CTAs**: Below average - improve prominence")
                    elif summary['click_rate'] > 5:
                        insights.append("🔗 **Content**: Highly engaging")

                    # Click-to-open insights
                    if summary['click_to_open_rate'] < 10:
                        insights.append("📱 **Content Alignment**: Needs improvement")
                    elif summary['click_to_open_rate'] > 20:
                        insights.append("📱 **Content Alignment**: Strong")

                    # Unsubscribe insights
                    if summary['unsub_rate'] > 0.5:
                        insights.append("⚠️ **Audience**: Possible fatigue")
                    elif summary['unsub_rate'] < 0.1:
                        insights.append("✅ **Audience**: Strong retention")

                    # Display insights
                    if insights:
                        for insight in insights:
                            st.markdown(insight)
                    else:
                        st.markdown("No specific insights available.")

                with recommendation_tab:
                    # Generate recommendations based on metrics
                    recommendations = []

                    if summary['open_rate'] < 15:
                        recommendations.append("1. **Test subject lines** with A/B testing")
                        recommendations.append("2. **Optimize send times**")

                    if summary['click_rate'] < 2:
                        recommendations.append("3. **Make CTAs more prominent**")
                        recommendations.append("4. **Focus on primary CTA**")

                    if summary['click_to_open_rate'] < 10:
                        recommendations.append("5. **Align content with subject lines**")

                    if summary['unsub_rate'] > 0.5:
                        recommendations.append("6. **Review email frequency**")
                        recommendations.append("7. **Segment your audience**")

                    # Add general recommendations if specific ones are few
                    if len(recommendations) < 3:
                        recommendations.append("• **Continue monitoring performance**")
                        recommendations.append("• **Test new content formats**")

                    # Display recommendations
                    for recommendation in recommendations:
                        st.markdown(recommendation)

            st.write("---")

            # Display data table if available
            if hasattr(st.session_state, 'performance_file_path'):
                performance_file = st.session_state.performance_file_path
                if os.path.exists(performance_file):
                    df = pd.read_csv(performance_file)
                    st.dataframe(df)

                    # Download button for the data
                    csv = df.to_csv(index=False)
                    st.download_button(
                        label="Download Performance Data",
                        data=csv,
                        file_name="mail_performance_all.csv",
                        mime="text/csv",
                        key="download_performance_data"
                    )

            # Display visualizations if visualization data is available
            if summary.get('visualization_data'):
                st.write("### Campaign Performance Visualizations")

                # Convert visualization data to DataFrame
                viz_df = pd.DataFrame(summary['visualization_data'])

                # Convert date column to datetime
                viz_df['date'] = pd.to_datetime(viz_df['date'])

                # Store in session state for use with the visualization tools
                st.session_state.analytics_data = viz_df

                # Metric selector expander
                with st.expander("📈 Visualization Options", expanded=True):
                    # Metric type and time unit selectors
                    col1, col2 = st.columns([2, 2])
                    with col1:
                        metric_type = st.selectbox(
                            "Select Metric",
                            options=['Rates', 'Sent', 'Opens', 'Clicks'],
                            format_func=lambda x: f"Email {x}" if x != 'Rates' else 'Open Rate vs Click Rate',
                            key='campaign_metric_type'
                        )
                    with col2:
                        time_unit = st.selectbox(
                            "Select Time Unit",
                            options=['Daily', 'Weekly', 'Monthly'],
                            key='campaign_time_unit'
                        )

                # Date filter
                col1, col2 = st.columns(2)
                with col1:
                    start_date = st.date_input("Start Date", min(viz_df['date']), key='campaign_start_date')
                with col2:
                    end_date = st.date_input("End Date", max(viz_df['date']), key='campaign_end_date')

                # Filter data and create plot
                filtered_df = viz_df[(viz_df['date'] >= pd.Timestamp(start_date)) & (viz_df['date'] <= pd.Timestamp(end_date))]
                fig = create_engagement_plot(
                    filtered_df,
                    time_unit='M' if time_unit == 'Monthly' else 'W' if time_unit == 'Weekly' else 'D',
                    metric_type=metric_type.lower()
                )

                # Display plot
                st.altair_chart(fig, use_container_width=True)

                # Display metrics table in an expander
                with st.expander("📊 Campaign Engagement Metrics", expanded=False):
                    st.dataframe(
                        filtered_df[['date', 'total_sent', 'total_opened', 'total_clicked', 'open_rate', 'click_rate']].style.format({
                            'open_rate': '{:.1f}%',
                            'click_rate': '{:.1f}%'
                        }),
                        hide_index=True
                    )

    # Process uploaded file for email engagement
    if uploaded_file is not None:
        try:
            # Read CSV
            df = pd.read_csv(uploaded_file)
            df = process_engagement_data(df)
            st.session_state.analytics_data = df

            # Metric selector expander
            with st.expander("📈 Visualization Options", expanded=True):
                # Metric type and time unit selectors in the second row
                col1, col2 = st.columns([2, 2])
                with col1:
                    metric_type = st.selectbox(
                        "Select Metric",
                        options=['Rates', 'Sent', 'Opens', 'Clicks'],
                        format_func=lambda x: f"Email {x}" if x != 'Rates' else 'Open Rate vs Click Rate',
                        key='metric_type'
                    )
                with col2:
                    time_unit = st.selectbox(
                        "Select Time Unit",
                        options=['Daily', 'Weekly', 'Monthly'],
                        key='time_unit'
                    )

            # Date filter in the third row
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input("Start Date", min(df['date']))
            with col2:
                end_date = st.date_input("End Date", max(df['date']))

            # Filter data and create plot
            filtered_df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
            fig = create_engagement_plot(
                filtered_df,
                time_unit='M' if time_unit == 'Monthly' else 'W' if time_unit == 'Weekly' else 'D',
                metric_type=metric_type.lower()
            )

            # Display plot
            st.altair_chart(fig, use_container_width=True)

            # Display metrics table in an expander
            with st.expander("📊 Engagement Metrics", expanded=False):
                st.dataframe(
                    filtered_df[['date', 'total_sent', 'total_opened', 'total_clicked', 'open_rate', 'click_rate']].style.format({
                        'open_rate': '{:.1f}%',
                        'click_rate': '{:.1f}%'
                    }),
                    hide_index=True
                )

        except Exception as e:
            st.error(f"Error processing file: {str(e)}")
    else:
        st.info("Please upload a CSV file to view email analytics.")

    # User Journey section remains in the first tab

    # User Journey Section with narrative - still in the first tab
    with email_engagement_tab:
        st.write("---")
        st.markdown("## 🛤️ Your Audience's Journey")

        st.markdown("""
        ### User Journey Analysis

        Visualize how your audience moves through different stages of engagement with your product.
        """)

        # Load journey data from user_journey.json
        journey_data = {}
        try:
            with open('data/user_journey.json', 'r') as f:
                journey_data = json.load(f)
        except Exception as e:
            st.error(f"Error loading user journey data: {str(e)}")

        # Try to load campaign performance data for funnel integration
        campaign_data = None
        if hasattr(st.session_state, 'performance_file_path'):
            try:
                performance_file = st.session_state.performance_file_path
                if os.path.exists(performance_file):
                    campaign_data = pd.read_csv(performance_file)
                    st.success("Campaign performance data loaded for funnel integration")
            except Exception as e:
                st.warning(f"Could not load campaign data for funnel: {str(e)}")

        # Check if we have campaign data
        has_campaign_data = campaign_data is not None

        # Process uploaded file for journey analysis if available
        # This takes precedence over the campaign data visualization
        if uploaded_file is not None:
            try:
                # Read CSV
                journey_df = pd.read_csv(uploaded_file)

                # Check if required columns exist
                if 'user_stage' not in journey_df.columns:
                    st.error("The CSV file must contain a 'user_stage' column.")
                elif 'product' not in journey_df.columns:
                    st.warning("The CSV file does not contain a 'product' column. Product filtering will not be applied.")
                    # Continue with processing but show the warning
                else:
                    # Check if organization products visibility is enabled
                    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

                    # Get organization URL from session state if available
                    org_url = None
                    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
                        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

                    # Get unique products from the dataframe
                    df_products = journey_df['product'].unique().tolist()

                    # Filter products by organization if enabled
                    if org_filter_enabled and org_url:
                        # Get all products for this organization
                        org_products = get_all_products(organization_url=org_url, filter_by_org=True)
                        org_product_names = [p.get('Product_Name', '') for p in org_products]

                        # Filter dataframe products to only include those from the organization
                        df_products = [p for p in df_products if p in org_product_names]

                        if not df_products:
                            st.warning(f"No products found for your organization ({org_url}) in the data. Showing all products instead.")
                            df_products = journey_df['product'].unique().tolist()

                    if not df_products:
                        st.error("No products found in the data.")
                        return

                    # Now we have products from the dataframe, let the user select one
                    selected_product = st.selectbox(
                        "Select Product",
                        options=df_products,
                        key="journey_product_filter"
                    )

                    # Get journey stages for the selected product
                    journey_stages = []

                    # First check if the product exists in user_journey.json
                    if selected_product in journey_data:
                        journey_stages = [stage['current_stage'] for stage in journey_data[selected_product]]
                        st.success(f"Using journey stages for '{selected_product}' from user_journey.json")
                    elif 'default' in journey_data:
                        # If no journey stages are defined for this product, use the default journey
                        st.warning(f"No journey stages defined for '{selected_product}' in user_journey.json. Using default journey stages.")
                        journey_stages = [stage['current_stage'] for stage in journey_data['default']]
                    else:
                        # If no default journey is defined, extract stages from the data
                        st.warning(f"No journey stages defined for '{selected_product}' and no default journey in user_journey.json. Using stages from the data.")
                        # Filter data for the selected product
                        product_data = journey_df[journey_df['product'] == selected_product]
                        # Get unique stages for this product
                        journey_stages = product_data['user_stage'].unique().tolist()

                    journey_df=journey_df[journey_df['product'] == selected_product]
                    # Process the data with product filter
                    stage_counts = process_user_journey_data(journey_df, product_name=selected_product, journey_stages=journey_stages)

                    try:
                        # Count emails, opens, clicks by stage if possible
                        if 'user_stage' in journey_df.columns and 'user_email' in journey_df.columns:
                            # Merge journey data with campaign data
                            merged_data = journey_df.copy()

                            # Add campaign metrics to stage_counts
                            for i, stage in enumerate(journey_stages):
                                stage_users = merged_data[merged_data['user_stage'] == stage]['user_email'].unique()
                                stage_data = merged_data[merged_data['user_stage'] == stage]

                                # Add metrics to stage_counts
                                if i < len(stage_counts):
                                    stage_counts.loc[i, 'emails_sent'] = len(stage_users)
                                    stage_counts.loc[i, 'emails_opened'] = stage_data['Open_Time'].notna().sum()
                                    stage_counts.loc[i, 'emails_clicked'] = stage_data['Click_Time'].notna().sum()

                                    # Calculate rates
                                    if len(stage_users) > 0:
                                        stage_counts.loc[i, 'open_rate'] = (stage_data['Open_Time'].notna().sum() / len(stage_users)) * 100
                                        stage_counts.loc[i, 'click_rate'] = (stage_data['Click_Time'].notna().sum() / len(stage_users)) * 100
                    except Exception as e:
                        st.warning(f"Could not enhance funnel with campaign metrics: {str(e)}")

                    # Create and display funnel chart
                    fig = create_user_journey_funnel_chart(
                        stage_counts,
                        journey_stages,
                        product_name=selected_product
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    # Display data table
                    with st.expander("📊 User Journey Data", expanded=False):
                        st.dataframe(
                            stage_counts.style.format({
                                'percentage': '{:.1f}%',
                                'open_rate': '{:.1f}%' if 'open_rate' in stage_counts.columns else None,
                                'click_rate': '{:.1f}%' if 'click_rate' in stage_counts.columns else None
                            }),
                            hide_index=True
                        )

                        # Download button for processed data
                        csv = stage_counts.to_csv(index=False)
                        st.download_button(
                            label="Download Journey Data",
                            data=csv,
                            file_name="user_journey_analytics.csv",
                            mime="text/csv",
                            key="download_journey_analytics"
                        )

            except Exception as e:
                st.error(f"Error processing file: {str(e)}")
        # If we have campaign data and no file is uploaded, show a journey visualization based on campaign data
        elif has_campaign_data:
            st.info("Showing user journey based on campaign data. Upload a User Journey CSV for more detailed analysis.")

            try:
                # Read CSV
                journey_df = campaign_data.copy()

                # Check if required columns exist
                if 'user_stage' not in journey_df.columns:
                    st.error("The CSV file must contain a 'user_stage' column.")
                elif 'Matched_Product' not in journey_df.columns:
                    st.warning("The CSV file does not contain a 'Matched_Product' column. Product filtering will not be applied.")
                    # Continue with processing but show the warning
                else:
                    # Check if organization products visibility is enabled
                    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

                    # Get organization URL from session state if available
                    org_url = None
                    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
                        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

                    # Get unique products from the dataframe
                    df_products = journey_df['Matched_Product'].unique().tolist()

                    # Filter products by organization if enabled
                    if org_filter_enabled and org_url:
                        # Get all products for this organization
                        org_products = get_all_products(organization_url=org_url, filter_by_org=True)
                        org_product_names = [p.get('Product_Name', '') for p in org_products]

                        # Filter dataframe products to only include those from the organization
                        df_products = [p for p in df_products if p in org_product_names]

                        if not df_products:
                            st.warning(f"No products found for your organization ({org_url}) in the data. Showing all products instead.")
                            df_products = journey_df['product'].unique().tolist()

                    if not df_products:
                        st.error("No products found in the data.")
                        return

                    # Now we have products from the dataframe, let the user select one
                    selected_product = st.selectbox(
                        "Select Product",
                        options=df_products,
                        key="journey_product_filter"
                    )

                    # Get journey stages for the selected product
                    journey_stages = []

                    # First check if the product exists in user_journey.json
                    if selected_product in journey_data:
                        journey_stages = [stage['current_stage'] for stage in journey_data[selected_product]]
                        st.success(f"Using journey stages for '{selected_product}' from user_journey.json")
                    elif 'default' in journey_data:
                        # If no journey stages are defined for this product, use the default journey
                        st.warning(f"No journey stages defined for '{selected_product}' in user_journey.json. Using default journey stages.")
                        journey_stages = [stage['current_stage'] for stage in journey_data['default']]
                    else:
                        # If no default journey is defined, extract stages from the data
                        st.warning(f"No journey stages defined for '{selected_product}' and no default journey in user_journey.json. Using stages from the data.")
                        # Filter data for the selected product
                        product_data = journey_df[journey_df['Matched_Product'] == selected_product]
                        # Get unique stages for this product
                        journey_stages = product_data['user_stage'].unique().tolist()

                    journey_df=journey_df[journey_df['Matched_Product'] == selected_product]
                    # Process the data with product filter
                    stage_counts = process_user_journey_data(journey_df, product_name=selected_product, journey_stages=journey_stages)

                    try:
                        # Count emails, opens, clicks by stage if possible
                        if 'user_stage' in journey_df.columns and 'user_email' in journey_df.columns:
                            # Merge journey data with campaign data
                            merged_data = journey_df.copy()

                            # Add campaign metrics to stage_counts
                            for i, stage in enumerate(journey_stages):
                                stage_users = merged_data[merged_data['user_stage'] == stage]['user_email'].unique()
                                stage_data = merged_data[merged_data['user_stage'] == stage]

                                # Add metrics to stage_counts
                                if i < len(stage_counts):
                                    stage_counts.loc[i, 'emails_sent'] = len(stage_users)
                                    stage_counts.loc[i, 'emails_opened'] = stage_data['Open_Time'].notna().sum()
                                    stage_counts.loc[i, 'emails_clicked'] = stage_data['Click_Time'].notna().sum()

                                    # Calculate rates
                                    if len(stage_users) > 0:
                                        stage_counts.loc[i, 'open_rate'] = (stage_data['Open_Time'].notna().sum() / len(stage_users)) * 100
                                        stage_counts.loc[i, 'click_rate'] = (stage_data['Click_Time'].notna().sum() / len(stage_users)) * 100
                    except Exception as e:
                        st.warning(f"Could not enhance funnel with campaign metrics: {str(e)}")

                    # Create and display funnel chart
                    fig = create_user_journey_funnel_chart(
                        stage_counts,
                        journey_stages,
                        product_name=selected_product
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    # Display data table
                    with st.expander("📊 User Journey Data", expanded=False):
                        st.dataframe(
                            stage_counts.style.format({
                                'percentage': '{:.1f}%',
                                'open_rate': '{:.1f}%' if 'open_rate' in stage_counts.columns else None,
                                'click_rate': '{:.1f}%' if 'click_rate' in stage_counts.columns else None
                            }),
                            hide_index=True
                        )

                        # Download button for processed data
                        csv = stage_counts.to_csv(index=False)
                        st.download_button(
                            label="Download Journey Data",
                            data=csv,
                            file_name="user_journey_analytics.csv",
                            mime="text/csv",
                            key="download_journey_analytics"
                        )

            except Exception as e:
                st.error(f"Error processing file: {str(e)}")

        else:
            # Show this message if we have neither campaign data nor an uploaded file
            st.info("Please upload a CSV file with user journey data or click 'Analyze Campaigns' to visualize the funnel chart.")

    # Add a comprehensive summary section at the end of the first tab
    with email_engagement_tab:
        st.write("---")
        st.markdown("## 📋 Your Email Marketing Dashboard Summary")

        # Check if we have any data to summarize
        has_campaign_data = hasattr(st.session_state, 'campaign_analyzed') and st.session_state.campaign_analyzed
        has_prediction_data = 'campaign_prediction' in st.session_state and st.session_state.campaign_prediction
        has_journey_data = 'journey_data' in locals() and journey_data

        if has_campaign_data or has_prediction_data or has_journey_data:
            st.markdown("""
            ### Key Takeaways
            """)

            # Create columns for the summary cards - removed Future Outlook since it's in its own tab now
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("""
                ##### 📊 Campaign Performance
                """)
                if has_campaign_data:
                    summary = st.session_state.campaign_summary
                    # Determine overall performance rating
                    avg_open_rate = summary['open_rate']
                    avg_click_rate = summary['click_rate']

                    if avg_open_rate > 25 and avg_click_rate > 5:
                        performance = "Excellent"
                        color = "green"
                    elif avg_open_rate > 15 and avg_click_rate > 2:
                        performance = "Good"
                        color = "orange"
                    else:
                        performance = "Needs improvement"
                        color = "red"

                    st.markdown(f"""
                    **Overall Performance**: <span style='color:{color};'>{performance}</span>

                    **Key Metrics**:
                    - Open Rate: {avg_open_rate:.1f}%
                    - Click Rate: {avg_click_rate:.1f}%
                    - Click-to-Open: {summary['click_to_open_rate']:.1f}%
                    """, unsafe_allow_html=True)
                else:
                    st.markdown("""
                    *No campaign data analyzed yet. Click "Analyze Campaigns" to see your performance metrics.*
                    """)

            with col2:
                st.markdown("""
                ##### 🛤️ Audience Journey
                """)
                if has_journey_data:
                    st.markdown("""
                    **Journey Insights**:
                    - Most users are in the awareness and consideration stages
                    - Highest engagement is in the consideration stage
                    - Opportunity to improve conversion from consideration to decision

                    **Recommended Focus**:
                    Create targeted content for each journey stage to move users forward in the funnel.
                    """)
                else:
                    st.markdown("""
                    *No journey data analyzed yet. Upload journey data or analyze campaigns to see your audience journey.*
                    """)

            # Add next steps section - simplified
            with st.expander("🚀 Recommended Next Steps", expanded=False):
                st.markdown("""
                1. **Review content strategy** for each journey stage
                2. **Optimize send times** based on engagement data
                3. **Segment audience** for more relevant content
                4. **A/B test** subject lines and CTAs
                5. **Monitor trends** over time
                """)
        else:
            st.info("To see a comprehensive summary of your email marketing performance, please analyze your campaigns or upload data using the options above.")

    # Analytics Story Tab
    with analytics_story_tab:
        display_analytics_story()

