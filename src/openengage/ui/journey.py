"""
Journey builder UI component for OpenEngage.

This module provides two email generation modes:
1. Template Based: Uses predefined templates with personalization
2. Fully Personalized: Creates completely custom emails using specialized AI agent
   with the fine-tuned model "ft:gpt-4o-mini-2024-07-18:analytics-vidhya:personalizedmailgeneratorendtoend:BfREaoS1"
"""
import json
import os
import pandas as pd
import streamlit as st
import asyncio
import subprocess
import sys
from datetime import datetime
from streamlit_plotly_events import plotly_events
from core.journey_builder import (
    create_journey_tree_plotly,
    create_user_journey_flow,
    handle_node_click,
    find_similar_product
)
from core.email_generator import generate_tree_emails
from utils.file_utils import get_all_products, load_feature_toggles
from pathlib import Path

def load_user_data():
    """Load user data from organization-specific CSV or fallback to default"""
    try:
        # Get organization-specific CSV path
        file_path = get_organization_csv_path()
        if not file_path:
            st.warning("No user data CSV found. Please generate one using the 'Generate User CSV' button.")
            return None, {}

        df = pd.read_csv(file_path)
        
        # Create a dictionary of email to (first_name, user_behaviour, user_stage) mapping
        user_data = {}
        
        for _, row in df.iterrows():
            if pd.notna(row['user_email']) and pd.notna(row['first_name']) and pd.notna(row['user_behaviour']):
                # Get the user stage
                user_stage = row['user_stage'] if pd.notna(row['user_stage']) else None
                user_data[row['user_email']] = (row['first_name'], row['user_behaviour'], user_stage)
        
        return df, user_data
    except Exception as e:
        st.error(f"Error loading user data: {e}")
        return None, {}

def generate_organization_csv():
    """Generate CSV for current organization using new_data.py script"""
    try:
        # Get current organization URL from session state
        
        org_url = getattr(st.session_state, 'organization_url', None)

        # Run the new_data.py script with organization URL as argument
        # Get the correct path to new_data.py (it's in the repository root)
        current_dir = os.path.dirname(os.path.abspath(__file__))  # src/openengage/ui
        repo_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # go up 3 levels
        script_path = os.path.join(repo_root, "new_data.py")

        if org_url:
            result = subprocess.run([sys.executable, script_path, org_url],
                                  capture_output=True, text=True, cwd=os.path.dirname(script_path))
        else:
            result = subprocess.run([sys.executable, script_path],
                                  capture_output=True, text=True, cwd=os.path.dirname(script_path))

        if result.returncode == 0:
            st.success("✅ Organization CSV generated successfully!")
        else:
            st.error("❌ Error generating CSV:")
            if result.stderr:
                st.code(result.stderr)

    except Exception as e:
        st.error(f"❌ Error running script: {str(e)}")

def get_organization_csv_path():
    """Get the CSV path for current organization"""
    try:
        # Get organization name from current user session state
        org_name = "Analytics Vidhya"  # default

        # First try to get from current user in session state
        if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
            org_data = st.session_state.current_user.get('organization', {})
            if org_data and org_data.get('name'):
                org_name = org_data['name']
        else:
            # Fallback to organization_url if available
            org_url = getattr(st.session_state, 'organization_url', None)
            if org_url:
                # Try to find matching user in users.json
                try:
                    with open('data/users.json', 'r') as f:
                        users = json.load(f)

                    for user in users:
                        if user.get('organization', {}).get('url') == org_url:
                            org_name = user['organization']['name']
                            break
                except (FileNotFoundError, json.JSONDecodeError):
                    pass

        safe_org_name = org_name.replace(' ', '_').replace('/', '_')
        org_csv_path = f'Sample Data For Mass Generation/{safe_org_name}_processed_user_data.csv'

        # Check if organization-specific CSV exists
        if os.path.exists(org_csv_path):
            return org_csv_path
        else:
            # Fallback to default CSV
            default_path = 'Sample Data For Mass Generation/processed_user_data.csv'
            if os.path.exists(default_path):
                return default_path
            return None
    except:
        return 'Sample Data For Mass Generation/processed_user_data.csv' if os.path.exists('Sample Data For Mass Generation/processed_user_data.csv') else None

def display_journey_builder():
    """Display the journey builder interface"""
    st.write("## 🛠️ Journey Builder")
    st.write("Build personalized email journeys for your users.")

    # Load feature toggles
    feature_toggles = load_feature_toggles()
    hide_csv_generation = feature_toggles.get('hide_behaviour_generation', False)

    # CSV Generation button (if not hidden)
    if not hide_csv_generation:
        col1, col2 = st.columns([3, 1])
        with col2:
            if st.button("📊 Generate User Behaviour", help="Generate user behaviour data for current organization"):
                generate_organization_csv()

    # Add Demo Blog button in a container with custom styling

    # Add Demo Blog button in a container with custom styling
    demo_blog_container = st.container()
    '''with demo_blog_container:
        st.markdown("""
        <div style="display: flex; justify-content: flex-end; margin-bottom: 20px;">
            <a href="http://localhost:8000/demo_blog/index.html" target="_blank" style="text-decoration: none;">
                <button style="background-color: #FF6B6B; color: white; border: none; border-radius: 4px; padding: 8px 16px; cursor: pointer; font-weight: bold;">
                    📝 Open Demo Blog
                </button>
            </a>
        </div>
        """, unsafe_allow_html=True)
    '''
    # Define stage progression map

    # Create a map of current stage to goal stage
    stage_map = {stage['current_stage']: stage['goal_stage'] for stage in st.session_state.user_journey}

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load products with optional filtering
    all_products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)
    companies = set()

    # Extract company names
    for product in all_products:
        if product:
            companies.add(product.get('Company_Name', ''))

    print(f"Loaded {len(all_products)} products")

    if not all_products:
        if org_filter_enabled and org_url:
            st.error(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
        else:
            st.error("Product details file not found or empty. Please add products first.")
        return

    if not companies:
        st.warning("No companies found in product details. Please add products first.")
        return

    # Initialize journey state in session if not exists
    if 'journey_generated' not in st.session_state:
        st.session_state.journey_generated = False

    # Only show input form if journey hasn't been generated
    if not st.session_state.journey_generated:
        # Load user data from CSV
        user_df, user_data_dict = load_user_data()

        # Silently load user data without showing debug info

        # Add generation mode selection
        st.markdown("### 🎯 Generation Mode")
        generation_mode = st.selectbox(
            "Select Email Generation Mode",
            ["Template Based", "Fully Personalized / End to End"],
            index=0,
            help="Template Based: Uses predefined templates with personalization. Fully Personalized: Creates completely custom emails without templates."
        )

        # Store generation mode in session state
        st.session_state.generation_mode = "template" if generation_mode == "Template Based" else "personalized"

        # Show mode description
        if generation_mode == "Template Based":
            st.info("📝 **Template Based Mode**: Uses existing email templates with personalization based on user behavior and product features.")
        else:
            st.info("🤖 **Fully Personalized Mode**: Creates completely custom emails using AI without any templates.")

        st.markdown("---")

        # User inputs
        col1, col2 = st.columns(2)

        with col1:
            # Get current user's organization name from session state
            if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
                org_data = st.session_state.current_user.get('organization', {})
                org_name = org_data.get('name', 'Unknown Organization')
                org_url = org_data.get('url', '')
            else:
                org_name = 'Unknown Organization'
                org_url = ''

            st.write("**Organization**")
            st.info(f"🏢 {org_name}")
            if org_url:
                st.caption(f"URL: {org_url}")

            # Set selected_company for compatibility with existing code
            selected_company = org_name
            
            # User selection dropdown
            if user_df is not None and not user_df.empty:
                # Initialize session state variables if they don't exist
                if 'selected_user_email' not in st.session_state:
                    st.session_state.selected_user_email = None
                if 'manual_entry' not in st.session_state:
                    st.session_state.manual_entry = True
                
                # Create a list of options: manual entry + all user emails
                user_options = ["Manual Entry"] + user_df['user_email'].tolist()
                
                selected_user_option = st.selectbox(
                    "Select User",
                    user_options,
                    index=0,
                    key="user_selector"
                )
                
                # Check if user selection has changed
                previous_selection = st.session_state.get('selected_user_option', 'Manual Entry')
                if selected_user_option != previous_selection:
                    # Store the current selection
                    st.session_state.selected_user_option = selected_user_option
                    
                    if selected_user_option == "Manual Entry":
                        # Clear auto-filled values for manual entry
                        st.session_state.auto_filled_name = ""
                        st.session_state.auto_filled_behavior = ""
                        if 'auto_filled_stage' in st.session_state:
                            del st.session_state.auto_filled_stage
                    else:
                        # Get user data for the selected user
                        if selected_user_option in user_data_dict:
                            name_value = user_data_dict[selected_user_option][0]
                            behavior_value = user_data_dict[selected_user_option][1]
                            stage_value = user_data_dict[selected_user_option][2]  # Get user stage
                            
                            # Update session state
                            st.session_state.auto_filled_name = name_value
                            st.session_state.auto_filled_behavior = behavior_value
                            
                            # Update stage if available
                            if stage_value is not None:
                                st.session_state.auto_filled_stage = stage_value
                    
                    # Force a rerun to update the form
                    st.rerun()
            
            # User name input field (always use the session state value)
            user_name = st.text_input(
                "User's First Name",
                value=st.session_state.get('auto_filled_name', ''),
                key="user_name_input"
            )
            # Always update the session state with the current value
            st.session_state.auto_filled_name = user_name

        with col2:
            # Get stages from user journey
            stages = [stage['current_stage'] for stage in st.session_state.user_journey]
            
            # Create a mapping between CSV stages and journey stages
            stage_mapping = {
                'New Visitor': 'New Visitor',
                'Product Purchased': 'Product Purchased',
                'First Time Visitor': 'New Visitor',  # Map similar stages
                'Lead Generated': 'Lead Generated',
                'Returning Visitor': 'Returning Visitor'
            }
            
            # Determine the default index for the stage dropdown
            default_stage_index = 0
            auto_filled_stage = st.session_state.get('auto_filled_stage', None)
            
            # Map the user's stage from CSV to a journey stage
            mapped_stage = None
            if auto_filled_stage in stage_mapping:
                mapped_stage = stage_mapping[auto_filled_stage]
            
            # Find the appropriate stage index based on mapping
            if mapped_stage and mapped_stage in stages:
                # Use the mapped stage if it exists in journey stages
                default_stage_index = stages.index(mapped_stage)
            elif auto_filled_stage in stages:
                # Use the original stage if it exists in journey stages
                default_stage_index = stages.index(auto_filled_stage)
            else:
                # Try to find a fuzzy match for the stage name
                for i, journey_stage in enumerate(stages):
                    if auto_filled_stage and journey_stage and (
                        auto_filled_stage.lower() in journey_stage.lower() or 
                        journey_stage.lower() in auto_filled_stage.lower()
                    ):
                        default_stage_index = i
                        break
            
            user_stage = st.selectbox(
                "User's Current Stage",
                stages,
                index=default_stage_index,
                key="user_stage_input"
            )
            
            # Update the auto-filled stage value if it was changed
            st.session_state.auto_filled_stage = user_stage

            # User behavior input field (always use the session state value)
            user_behavior = st.text_area(
                "User's Activity/Behavior",
                value=st.session_state.get('auto_filled_behavior', ''),
                placeholder="Describe the user's recent activity, interests, or behavior...",
                key="user_behavior_input"
            )
            # Always update the session state with the current value
            st.session_state.auto_filled_behavior = user_behavior

        st.session_state.journey_user_behavior = user_behavior
        st.session_state.journey_user_stage = user_stage
        st.session_state.journey_user_name = user_name
        st.session_state.journey_company = selected_company

        if st.button("Generate Personalized Journey", type="primary"):
            if not user_name or not user_behavior:
                st.error("Please fill in all fields.")
                return

            # Create a container for AI process visualization with a prominent header
            st.markdown("## 🧠 AI Decision-Making Process")
            st.markdown("### Watch the AI analyze behavior and create a personalized journey")
            
            # Add a divider for better visibility
            st.markdown("---")
            
            # Create collapsable sections for each AI process (defaulting to expanded state)
            
            # 1. User data analysis
            with st.expander("📃 **Analyzing User Data**", expanded=False):
                st.markdown("Collecting and processing user behavior data...")
                
                # Get organization_url for the selected company
                selected_company_products = [p for p in all_products if (p.get('organization_url') == org_url or p.get('Company_URL') == org_url)]
                
                if not selected_company_products:
                    st.error(f"No products found for {st.session_state.journey_company}")
                    return
                
                # Get the organization_url from the first product
                org_url = selected_company_products[0].get('organization_url') or selected_company_products[0].get('Company_URL')
                
                if not org_url:
                    st.error(f"No organization URL found for {st.session_state.journey_company}")
                    return
                    
                # Get all products with matching organization_url
                company_products = [p for p in all_products if (p.get('organization_url') == org_url or p.get('Company_URL') == org_url)]
                
                st.caption(f"Found {len(company_products)} products for {st.session_state.journey_company} using organization URL: {org_url}")
                
                if not company_products:
                    st.error(f"No products found for organization URL: {org_url}")
                    return
                
                # Small delay to make the process visible
                import time
                time.sleep(0.3)
                st.success("✓ User data analyzed successfully!")
            
            # 2. Product matching
            with st.expander("🎁 **Deciding the optimal product**", expanded=False):
                st.markdown("Using semantic analysis to match user behavior with the most relevant product...")
                time.sleep(0.5)
                
                # Find most similar product
                matched_product, similarity = find_similar_product(user_behavior, company_products)

                # Display product match results
                st.success("✓ Product matched successfully!")
                st.markdown(f"**Based on the user's activity, we recommend:** {matched_product.get('Product_Name')}")
                st.markdown(f"**Similarity Score:** {similarity:.2f}")
                
                # Display product features that might have influenced the match
                if 'Product_Features' in matched_product and matched_product['Product_Features']:
                    st.write("**Key product features relevant to user behavior:**")
                    for i, feature in enumerate(matched_product['Product_Features'][:3]):
                        st.markdown(f"- {feature}")
            
            # Store the matched product in session state
            st.session_state.matched_product = matched_product
            
            # 3. Stage determination
            with st.expander("💼 **Deciding the next stages in the user journey as per current stage**", expanded=False):
                st.markdown(f"Analyzing user's current stage: **{user_stage}**")
                time.sleep(0.4)
                
                # Find starting stage index
                start_idx = stages.index(user_stage)
                remaining_stages = stages[start_idx:]
                
                # Show determined stages
                st.success("✓ User journey stages determined")
                st.markdown(f"**Current stage:** {user_stage}")
                if start_idx + 1 < len(stages):
                    st.markdown(f"**Next target stage:** {stages[start_idx + 1]}")
                if start_idx + 2 < len(stages):
                    st.markdown(f"**Future stage:** {stages[start_idx + 2]}")
            
            # 4. Email template/generation selection
            generation_mode = st.session_state.get('generation_mode', 'template')
            if generation_mode == 'template':
                with st.expander("📄 **Selecting email template for mail generation**", expanded=False):
                    st.markdown("Choosing optimal email templates based on user stage and product...")
                    time.sleep(0.4)

                    # Show template selection result
                    st.success("✓ Email templates selected")
                    st.markdown("**Selected template categories:**")
                    st.markdown("- Product introduction template")
                    st.markdown("- Engagement follow-up template")
                    st.markdown("- Conversion template")
                    time.sleep(0.3)
            else:
                with st.expander("🤖 **Preparing AI for fully personalized email generation**", expanded=False):
                    st.markdown("Initializing specialized AI agent for end-to-end email generation...")
                    time.sleep(0.4)

                    # Show AI preparation result
                    st.success("✓ AI agent ready for personalized generation")
                    st.markdown("**AI capabilities activated:**")
                    st.markdown("- HOOK-TRUST-PITCH-MOTIVATION framework")
                    st.markdown("- Behavior-driven content creation")
                    st.markdown("- Template-free personalization")
                    time.sleep(0.3)
                
            # 5. Email generation
            generation_mode = st.session_state.get('generation_mode', 'template')
            if generation_mode == 'template':
                with st.expander("📧 **Generating personalised mail for stages**", expanded=False):
                    st.markdown("Creating tailored email content for each stage using templates...")
                    time.sleep(0.5)

                    # Save journey state
                    st.session_state.journey_generated = True
                    st.session_state.journey_stages = stages
                    st.session_state.current_stage_idx = start_idx
                    st.session_state.tree_start_idx = start_idx

                    # Show progress bar for email generation
                    progress_bar = st.progress(0)
                    st.session_state.progress_bar = progress_bar

                    # Generate emails with progress updates
                    generate_tree_emails(user_stage, 0, 0, matched_product, progress_bar, start_idx, max_level=2, generation_mode=generation_mode)
                    progress_bar.progress(100)

                    st.success("✓ Template-based emails generated for all journey paths")
                    st.markdown("**Email personalization strategies applied:**")
                    st.markdown("- Dynamic product feature highlighting based on user behavior")
                    st.markdown("- Stage-appropriate messaging and CTAs")
                    st.markdown("- Personalized subject lines and preview text")
            else:
                with st.expander("🤖 **Generating fully personalized emails using AI**", expanded=False):
                    st.markdown("Creating completely custom email content using specialized AI agent...")
                    time.sleep(0.5)

                    # Save journey state
                    st.session_state.journey_generated = True
                    st.session_state.journey_stages = stages
                    st.session_state.current_stage_idx = start_idx
                    st.session_state.tree_start_idx = start_idx

                    # Show progress bar for email generation
                    progress_bar = st.progress(0)
                    st.session_state.progress_bar = progress_bar

                    # Generate emails with progress updates
                    generate_tree_emails(user_stage, 0, 0, matched_product, progress_bar, start_idx, generation_mode=generation_mode)
                    progress_bar.progress(100)

                    st.success("✓ Fully personalized emails generated for all journey paths")
                    st.markdown("**AI personalization strategies applied:**")
                    st.markdown("- HOOK-TRUST-PITCH-MOTIVATION framework implementation")
                    st.markdown("- Behavior-driven content creation without templates")
                    st.markdown("- Completely custom messaging for each user scenario")
            
            # 6. Journey tree building
            with st.expander("🌳 **Building and plotting the journey tree for the user**", expanded=False):
                st.markdown("Creating visual representation of the user's journey...")
                time.sleep(0.4)
                
                st.success("✓ Journey tree created")
                st.markdown("**Tree structure:**")
                st.markdown("- Root node: Current user stage")
                st.markdown("- Left branches: Email opened/clicked but goal not achieved")
                st.markdown("- Right branches: Goal achieved, advancing to next stage")
            
            # 7. Send time determination
            with st.expander("🕒 **Deciding the send time**", expanded=False):
                st.markdown("Analyzing optimal timing for email delivery...")
                time.sleep(0.3)
                
                # Determine optimal send time (this is a placeholder - in real implementation would be more sophisticated)
                from datetime import datetime, timedelta
                optimal_time = datetime.now() + timedelta(days=1)
                optimal_time = optimal_time.replace(hour=10, minute=0, second=0)
                
                st.success("✓ Optimal send time determined")
                st.markdown(f"**Recommended send time:** {optimal_time.strftime('%A, %d %B %Y at %I:%M %p')}")
                st.markdown("**Factors considered:**")
                st.markdown("- User's past engagement patterns")
                st.markdown("- Industry benchmarks for open rates")
                st.markdown("- Time zone optimization")
            
            # 8. Brand guidelines
            with st.expander("🎨 **Modifying the generated email html as per brand guidelines**", expanded=False):
                st.markdown("Applying company branding to email templates...")
                time.sleep(0.4)
                
                st.success("✓ Brand guidelines applied")
                st.markdown("**Branding elements applied:**")
                st.markdown("- Corporate colors and typography")
                st.markdown("- Logo placement and sizing")
                st.markdown("- Footer with compliant unsubscribe options")
                st.markdown("- Legal disclaimers and privacy policy links")
            
            # 9-11. Simulation capabilities
            with st.expander("💻 **Journey simulation capabilities ready**", expanded=False):
                st.markdown("The system is prepared to simulate user interactions:")
                st.markdown("- **Simulating open/click events** - Test email engagement")
                st.markdown("- **Capturing stage change events** - Monitor journey progression")
                st.markdown("- **Adding new activity data to behaviors** - Update user profiles")
                
                st.success("✓ Journey builder ready for interaction")
                st.markdown("Use the controls below to simulate user responses and see the journey evolve.")
            
            # Summary of AI work done
            st.success("🤖 **AI Process Complete:** User analyzed, optimal product matched, and personalized journey created!")
            st.write("Click on any node in the journey tree to view the personalized email content.")
            
            # Display the journey visualization elements
            st.write("### 🎯 Matched Product")
            st.write(f"Based on the user's activity, we recommend: **{matched_product.get('Product_Name')}**")
            st.write(f"Similarity Score: {similarity:.2f}")

            # Generate personalized emails for each stage
            st.write("### 📧 Personalized Email Journey")

            # Display the journey tree visualization
            st.write("### 🌳 Journey Tree with Emails")

            # Display the journey tree with email content side by side
            fig, node_ids = create_journey_tree_plotly(stages, start_idx, st.session_state.get("traversed_node", 1))

            # Create columns for tree and email
            tree_col, email_col = st.columns([2, 1])

            with tree_col:
                # Display interactive tree and capture clicks
                clicked_data = plotly_events(fig, click_event=True, override_height=600)

                if clicked_data and isinstance(clicked_data, list) and len(clicked_data) > 0:
                    clicked_item = clicked_data[0]
                    node_number = clicked_item.get("pointNumber", 0)
                    st.session_state["selected_node"] = node_number + 1
                    st.session_state["traversed_node"] = node_number + 1

                    # Add tree controls below the tree
                display_tree_controls(tree_col, start_idx, stages, all_products)

            with email_col:
                # Check if we have a selected node
                if "selected_node" in st.session_state:
                    handle_node_click(f"node_{st.session_state['selected_node']}")

            # Show success message
            st.success("Journey tree generated successfully! Click a node to view its email content.")

            # Store journey data in session state
            st.session_state.journey_generated = True
            st.session_state.journey_stages = stages
            st.session_state.current_stage_idx = start_idx
    else:
        # Display the existing journey tree
        fig, node_ids = create_journey_tree_plotly(st.session_state.journey_stages, st.session_state.tree_start_idx, st.session_state.get("traversed_node", 1))

        # Create columns for tree and email
        tree_col, email_col = st.columns([2, 1])

        with tree_col:
            # Display interactive tree and capture clicks
            clicked_data = plotly_events(fig, click_event=True, override_height=600)

            if clicked_data and isinstance(clicked_data, list) and len(clicked_data) > 0:
                clicked_item = clicked_data[0]
                node_number = clicked_item.get("pointNumber", 0)
                st.session_state["selected_node"] = node_number + 1
                st.session_state["traversed_node"] = node_number + 1

            # Add tree controls below the tree
            display_tree_controls(tree_col, st.session_state.current_stage_idx, st.session_state.journey_stages, all_products)

        with email_col:
            # Check if we have a selected node
            if "selected_node" in st.session_state:
                handle_node_click(f"node_{st.session_state['selected_node']}")

        # Add button to generate new journey
        if st.button("Generate New Journey"):
            st.session_state.journey_generated = False
            st.rerun()

def display_tree_controls(tree_col, current_stage_idx, stages=None, all_products=None):
    """Display tree traversal controls for the current stage"""
    st.write("### Journey Controls")

    # Get the current node number based on stage
    current_node = st.session_state.get("traversed_node", 1)

    # Get stages from session state if not provided
    if stages is None and "journey_stages" in st.session_state:
        stages = st.session_state.journey_stages

    # Initialize journey steps if not exists
    if "journey_steps" not in st.session_state:
        st.session_state.journey_steps = []
        # Add initial stage
        if stages:
            st.session_state.journey_steps.append({
                'type': 'stage',
                'action': stages[current_stage_idx],
                'timestamp': datetime.now().isoformat()
            })

    # Get current stage based on current_stage_idx
    current_stage = stages[current_stage_idx] if stages else None

    # Create three columns for buttons
    left_col, middle_col, right_col = st.columns(3)

    with tree_col:
        # Display current stage
        st.write(f"**Current Stage:** {current_stage}")

    with left_col:
        # Email Open button - Allow at any stage except the last one
        if st.button("📧 Open Email", key="btn_open", disabled=current_stage_idx >= len(stages) - 1):
            st.session_state.journey_steps.append({
                'type': 'email',
                'action': 'Opened Email',
                'timestamp': datetime.now().isoformat()
            })
            st.rerun()

    with middle_col:
        # Email Click button - Allow at any stage except the last one
        if st.button("🖱️ Click Link", key="btn_click", disabled=current_stage_idx >= len(stages) - 1):
            st.session_state.journey_steps.append({
                'type': 'email',
                'action': 'Clicked Link',
                'timestamp': datetime.now().isoformat()
            })
            st.rerun()

    with right_col:
        # Next Stage button - Always leads to right child
        next_stage_name = None
        if stages:
            next_stage_name = stages[current_stage_idx + 1] if current_stage_idx + 1 < len(stages) else None

        if next_stage_name:
            if st.button(f"🎯 {next_stage_name}", key="btn_next"):
                st.session_state["current_stage_idx"] = current_stage_idx + 1
                st.session_state.journey_steps.append({
                    'type': 'stage',
                    'action': next_stage_name,
                    'timestamp': datetime.now().isoformat()
                })
                st.rerun()


    # Display user journey flow with refresh button
    journey_header_col, refresh_btn_col = st.columns([5, 1])
    with journey_header_col:
        st.write("### User Journey Flow")
    with refresh_btn_col:
        if st.button("🔄 Refresh Journey", key="btn_refresh"):
            # Initialize or reset the refresh journey state
            st.session_state.refresh_journey = True
            st.session_state.show_behavior_input = False
            st.rerun()
    new_behavior = None
    # Show behavior change dialog if refresh button was clicked
    if st.session_state.get('refresh_journey', False):
        behavior_changed = st.radio(
            "Has the user's behavior changed?",
            ["Yes", "No"],
            key="behavior_changed"
        )

        if behavior_changed == "Yes":
            # Show previous behavior
            st.write("**Previous User Behavior:**")
            st.info(st.session_state.get('journey_user_behavior', ''))

            # Get new behavior
            new_behavior = st.text_area(
                "Additional User Behavior",
                placeholder="Describe the user's new activity, interests, or behavior...",
                key="new_user_behavior"
            )

        if st.button("Update Journey", key="btn_update_journey"):
            # Update the user behavior by combining old and new
            if new_behavior:
                combined_behavior = f"{st.session_state.get('journey_user_behavior', '')}\n\nNew Behavior:\n{new_behavior}"
                st.session_state.journey_user_behavior = combined_behavior
            else:
                combined_behavior = st.session_state.get('journey_user_behavior', '')

            # Reset journey from current stage
            current_stage = stages[current_stage_idx] if stages else None
            if current_stage:
                # Store only the steps up to current stage
                st.session_state.journey_steps = [
                    step for step in st.session_state.journey_steps
                    if step.get('type') == 'stage' and
                    stages.index(step.get('action', '')) <= current_stage_idx
                ]

                with st.spinner("Analyzing user behavior and generating personalized journey..."):
                    # Generate personalized emails for each stage
                    st.write("### 📧 Personalized Email Journey")

                    # Find starting stage index
                    start_idx = stages.index(current_stage)
                    remaining_stages = stages[start_idx:]

                    # Save journey state
                    st.session_state.journey_generated = True
                    st.session_state.journey_stages = stages
                    st.session_state.current_stage_idx = start_idx
                    st.session_state.tree_start_idx = start_idx

                    # Generate emails
                    st.write("### 🌳 Journey Tree with Emails")
                    st.write("Generating personalized emails for each node...")

                    progress_bar = st.progress(0)
                    st.session_state.progress_bar = progress_bar
                    generation_mode = st.session_state.get('generation_mode', 'template')
                    generate_tree_emails(current_stage, 0, 0, st.session_state.matched_product, progress_bar, start_idx, max_level=2, generation_mode=generation_mode)
                    progress_bar.progress(100)

                    # Store journey data in session state
                    st.session_state.journey_generated = True
                    st.session_state.refresh_journey = False
                    st.session_state.journey_stages = stages
                    st.session_state.current_stage_idx = start_idx
                    st.rerun()

    # Display journey visualization
    journey_fig = create_user_journey_flow(st.session_state.journey_steps)
    if journey_fig:
        st.plotly_chart(journey_fig, use_container_width=True)
