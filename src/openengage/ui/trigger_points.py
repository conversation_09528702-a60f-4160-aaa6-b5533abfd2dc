"""
Trigger points UI component for OpenEngage.
"""
import streamlit as st
from datetime import datetime
import json
import os
import pandas as pd

from core.trigger_points import (
    save_trigger_action,
    get_trigger_actions,
    delete_trigger_action,
    segment_users_by_trigger,
    fire_trigger
)

def _convert_old_trigger_format(trigger):
    """Convert old trigger format to new format with conditions"""
    if "conditions" not in trigger:
        # Create conditions list from old format
        conditions = []
        if "condition_type" in trigger and "trigger_details" in trigger:
            conditions.append({
                "condition_type": trigger["condition_type"],
                "trigger_details": trigger["trigger_details"]
            })
        
        # Create new format trigger
        new_trigger = {
            "id": trigger["id"],
            "action_type": trigger["action_type"],
            "conditions": conditions,
            "combination_type": "AND",
            "status": trigger.get("status", "Active")
        }
        return new_trigger
    return trigger

def load_trigger_points():
    """Load trigger points from JSON file"""
    file_path = "data/trigger_points.json"
    
    # Create data directory if it doesn't exist
    if not os.path.exists("data"):
        os.makedirs("data")
    
    # Create empty JSON file if it doesn't exist
    if not os.path.exists(file_path):
        with open(file_path, "w") as f:
            json.dump([], f)
        return []
    
    try:
        with open(file_path, "r") as f:
            content = f.read()
            if not content.strip():  # If file is empty
                return []
            triggers = json.loads(content)
            # Convert old format triggers to new format
            return [_convert_old_trigger_format(t) for t in triggers]
    except json.JSONDecodeError:
        # If JSON is corrupted, backup the file and create new empty one
        if os.path.exists(file_path):
            backup_path = f"{file_path}.backup"
            os.rename(file_path, backup_path)
        with open(file_path, "w") as f:
            json.dump([], f)
        return []
    except Exception as e:
        st.error(f"Error loading trigger points: {str(e)}")
        return []

def save_trigger_points(trigger_points):
    """Save trigger points to JSON file"""
    file_path = "data/trigger_points.json"
    try:
        with open(file_path, "w") as f:
            json.dump(trigger_points, f, indent=4)
    except Exception as e:
        st.error(f"Error saving trigger points: {str(e)}")

def delete_trigger_point(trigger_id):
    """Delete a trigger point by ID"""
    trigger_points = load_trigger_points()
    trigger_points = [t for t in trigger_points if t["id"] != trigger_id]
    save_trigger_points(trigger_points)

def get_product_list():
    """Get list of available products"""
    return ["Certified AI/ML BlackBelt Plus Program", 
            "GenAI Pinnacle Plus Program", 
            "Agentic AI Pioneer Program"]

def display_trigger_points():
    st.title("Trigger Points and Rules")
    st.write("Define your trigger points and the actions to be performed.")

    # Initialize session state
    if "editing_trigger" not in st.session_state:
        st.session_state.editing_trigger = None
    if "conditions" not in st.session_state:
        st.session_state.conditions = []
    if "current_condition" not in st.session_state:
        st.session_state.current_condition = {}

    # Load existing trigger points
    trigger_points = load_trigger_points()

    # Step 1: Select Action Type
    action_type = st.selectbox(
        "What should happen when this trigger is fired?",
        ["Send Campaign", "Update User Behaviour"]
    )

    # Step 2: Configure conditions
    st.subheader("Configure Trigger Conditions")
    
    # Display existing conditions
    if st.session_state.conditions:
        st.write("Current Conditions:")
        for i, condition in enumerate(st.session_state.conditions):
            with st.expander(f"Condition {i+1}"):
                st.write(f"**Type:** {condition['condition_type']}")
                for key, value in condition['trigger_details'].items():
                    st.write(f"- {key}: {value}")

    # Configure new condition
    condition_type = st.selectbox(
        "Select Input Condition Type:",
        [
            "Activity in product funnel",
            "User done something on website",
            "User responded the previous campaign",
            "User demographic",
            "Is inactive"
        ]
    )

    # Step 3: Additional details based on condition type
    trigger_details = {}
    
    if condition_type == "Activity in product funnel":
        activity_type = st.selectbox(
            "Select Activity Type:",
            ["Is Buyer", "Is Lead", "Is Pagevisitor"]
        )
        selected_product = st.selectbox(
            "Select Product:",
            get_product_list()
        )
        trigger_details.update({
            "activity_type": activity_type,
            "product": selected_product
        })

    elif condition_type == "User done something on website":
        action = st.selectbox(
            "Select Action Type:",
            ["Pageview", "Click"]
        )
        time_duration = st.text_input("Time Duration (e.g., last 7 days):")
        url_contains = st.text_input("URL Contains:")
        trigger_details.update({
            "action": action,
            "time_duration": time_duration,
            "url_contains": url_contains
        })

    elif condition_type == "User responded the previous campaign":
        response_type = st.selectbox(
            "Select Response Type:",
            ["Opened", "Clicked"]
        )
        campaign_id = st.text_input("Campaign ID:")
        trigger_details.update({
            "response_type": response_type,
            "campaign_id": campaign_id
        })

    elif condition_type == "User demographic":
        attribute = st.selectbox(
            "Select Attribute:",
            ["Signup Date", "Age is between", "Country"]
        )
        if attribute == "Country":
            filter_value = st.text_input("Enter Country:")
            trigger_details.update({
                "attribute": attribute,
                "filter_value": filter_value
            })
        elif attribute == "Age is between":
            col1, col2 = st.columns(2)
            with col1:
                min_age = st.number_input("Minimum Age", min_value=0, max_value=120)
            with col2:
                max_age = st.number_input("Maximum Age", min_value=0, max_value=120, value=100)
            trigger_details.update({
                "attribute": attribute,
                "min_age": min_age,
                "max_age": max_age
            })
        else:  # Signup Date
            filter_value = st.date_input(f"Select {attribute}:")
            trigger_details.update({
                "attribute": attribute,
                "filter_value": str(filter_value)
            })

    elif condition_type == "Is inactive":
        trigger_details["is_inactive"] = True

    # Add Condition button
    if st.button("Add Condition"):
        new_condition = {
            "condition_type": condition_type,
            "trigger_details": trigger_details
        }
        st.session_state.conditions.append(new_condition)
        st.success("Condition added! You can add more conditions or save the trigger.")
        st.rerun()

    # Save Trigger button
    if st.button("Save Trigger"):
        if st.session_state.conditions or condition_type:
            conditions = st.session_state.conditions.copy()
            # Add current condition if it exists
            if trigger_details:
                conditions.append({
                    "condition_type": condition_type,
                    "trigger_details": trigger_details
                })
            
            new_trigger = {
                "id": st.session_state.editing_trigger["id"] if st.session_state.editing_trigger else datetime.now().strftime("%Y%m%d%H%M%S"),
                "action_type": action_type,
                "conditions": conditions,
                "combination_type": "AND",
                "status": "Active"
            }

            if st.session_state.editing_trigger:
                trigger_points = [t if t["id"] != new_trigger["id"] else new_trigger for t in trigger_points]
                st.session_state.editing_trigger = None
            else:
                trigger_points.append(new_trigger)

            save_trigger_points(trigger_points)
            st.session_state.conditions = []  # Clear conditions after saving
            st.success("Trigger Point saved successfully!")
            st.rerun()

    # Display saved triggers
    st.write("### Saved Triggers")
    
    if not trigger_points:
        st.info("No trigger points saved yet.")
    else:
        for trigger in trigger_points:
            # Ensure trigger is in new format
            trigger = _convert_old_trigger_format(trigger)
            
            with st.expander(f"Trigger: {trigger['action_type']} ({len(trigger['conditions'])} conditions)"):
                st.write(f"**Action Type:** {trigger['action_type']}")
                st.write("**Conditions:**")
                for i, condition in enumerate(trigger['conditions'], 1):
                    st.write(f"\nCondition {i}:")
                    st.write(f"- Type: {condition['condition_type']}")
                    st.write("- Details:")
                    for key, value in condition['trigger_details'].items():
                        st.write(f"  * {key}: {value}")
                    if i < len(trigger['conditions']):
                        st.write("**AND**")
                
                # Action buttons
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    if st.button("🎯 Segment", key=f"segment_{trigger['id']}"):
                        users = segment_users_by_trigger(trigger['id'])
                        st.write(f"Segmented Users: {len(users)}")
                        st.dataframe(pd.DataFrame(users))
                with col2:
                    if st.button("🚀 Fire", key=f"fire_{trigger['id']}"):
                        result = fire_trigger(trigger['id'])
                        st.success(f"Trigger fired successfully! Affected users: {result['affected_users']}")
                with col3:
                    if st.button("🗑️ Delete", key=f"delete_{trigger['id']}"):
                        delete_trigger_point(trigger['id'])
                        st.success("Trigger point deleted successfully!")
                        st.rerun()
                with col4:
                    if st.button("✏️ Edit", key=f"edit_{trigger['id']}"):
                        st.session_state.editing_trigger = trigger
                        st.session_state.conditions = trigger['conditions']
                        st.rerun()
