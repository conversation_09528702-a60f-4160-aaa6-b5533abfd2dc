"""
Chatbot component for OpenEngage.
"""
import streamlit as st
from openai import OpenAI
import os
from dotenv import load_dotenv
import pandas as pd

def init_chatbot_session():
    """Initialize the chatbot session state variables."""
    if "chatbot_visible" not in st.session_state:
        st.session_state.chatbot_visible = False

def toggle_chatbot():
    """Toggle the visibility of the chatbot."""
    st.session_state.chatbot_visible = not st.session_state.chatbot_visible

def get_analytics_context(df):
    """
    Extract relevant analytics context from the dataframe to provide to the chatbot.

    Args:
        df: DataFrame containing campaign analytics data

    Returns:
        str: A summary of the analytics data
    """
    context = "Campaign Analytics Summary:\n"

    # Add basic stats
    if df is not None and not df.empty:
        # Check for different column naming patterns
        # For sent emails
        sent_count = 0
        if 'total_sent' in df.columns:
            sent_count = df['total_sent'].sum()
        elif 'user_email' in df.columns:
            sent_count = len(df['user_email'])

        # For opened emails
        opened_count = 0
        if 'total_opened' in df.columns:
            opened_count = df['total_opened'].sum()
        elif 'Open_Time' in df.columns:
            opened_count = df['Open_Time'].notna().sum()
        elif 'Last_Open_Time' in df.columns:
            opened_count = df['Last_Open_Time'].notna().sum()

        # For clicked emails
        clicked_count = 0
        if 'total_clicked' in df.columns:
            clicked_count = df['total_clicked'].sum()
        elif 'Click_Time' in df.columns:
            clicked_count = df['Click_Time'].notna().sum()
        elif 'Last_Click_Time' in df.columns:
            clicked_count = df['Last_Click_Time'].notna().sum()

        # Calculate rates
        open_rate = (opened_count / sent_count * 100) if sent_count > 0 else 0
        click_rate = (clicked_count / sent_count * 100) if sent_count > 0 else 0
        click_to_open_rate = (clicked_count / opened_count * 100) if opened_count > 0 else 0

        context += f"- Total emails sent: {sent_count}\n"
        context += f"- Total emails opened: {opened_count}\n"
        context += f"- Total emails clicked: {clicked_count}\n"
        context += f"- Overall open rate: {open_rate:.2f}%\n"
        context += f"- Overall click rate: {click_rate:.2f}%\n"
        context += f"- Click-to-open rate: {click_to_open_rate:.2f}%\n"

        # Add product information if available
        product_column = None
        if 'product' in df.columns:
            product_column = 'product'
        elif 'offering' in df.columns:
            product_column = 'offering'
        elif 'Matched_Product' in df.columns:
            product_column = 'Matched_Product'
        elif 'last_product_sent' in df.columns:
            product_column = 'last_product_sent'
        elif 'Last_Product_Sent' in df.columns:
            product_column = 'Last_Product_Sent'
        elif 'template-id' in df.columns:
            product_column = 'template-id'
        elif 'email-template' in df.columns:
            product_column = 'email-template'

        if product_column and not df[product_column].empty:
            products = df[product_column].dropna().unique()
            if len(products) > 0:
                context += f"- Products/Templates in data: {', '.join(products)}\n"

                # Add product-specific metrics
                context += "\nProduct-specific metrics:\n"
                for product in products:
                    product_df = df[df[product_column] == product]

                    # For sent emails
                    product_sent = 0
                    if 'total_sent' in product_df.columns:
                        product_sent = product_df['total_sent'].sum()
                    elif 'user_email' in product_df.columns:
                        product_sent = len(product_df['user_email'])

                    # For opened emails
                    product_opened = 0
                    if 'total_opened' in product_df.columns:
                        product_opened = product_df['total_opened'].sum()
                    elif 'Open_Time' in product_df.columns:
                        product_opened = product_df['Open_Time'].notna().sum()
                    elif 'Last_Open_Time' in product_df.columns:
                        product_opened = product_df['Last_Open_Time'].notna().sum()

                    # For clicked emails
                    product_clicked = 0
                    if 'total_clicked' in product_df.columns:
                        product_clicked = product_df['total_clicked'].sum()
                    elif 'Click_Time' in product_df.columns:
                        product_clicked = product_df['Click_Time'].notna().sum()
                    elif 'Last_Click_Time' in product_df.columns:
                        product_clicked = product_df['Last_Click_Time'].notna().sum()

                    product_open_rate = (product_opened / product_sent * 100) if product_sent > 0 else 0
                    product_click_rate = (product_clicked / product_sent * 100) if product_sent > 0 else 0

                    context += f"\n{product}:\n"
                    context += f"  - Emails sent: {product_sent}\n"
                    context += f"  - Emails opened: {product_opened}\n"
                    context += f"  - Open rate: {product_open_rate:.2f}%\n"
                    context += f"  - Click rate: {product_click_rate:.2f}%\n"

        # Add date range information
        date_column = None
        if 'date' in df.columns:
            date_column = 'date'
        elif 'Send_Time' in df.columns:
            date_column = 'Send_Time'
        elif 'Last_Send_Time' in df.columns:
            date_column = 'Last_Send_Time'

        if date_column:
            # Convert to datetime if not already
            df[date_column] = pd.to_datetime(df[date_column], errors='coerce')

            # Filter out NaT values
            valid_dates = df[df[date_column].notna()]

            if not valid_dates.empty:
                min_date = valid_dates[date_column].min().strftime('%Y-%m-%d')
                max_date = valid_dates[date_column].max().strftime('%Y-%m-%d')
                context += f"\nDate range: {min_date} to {max_date}\n"

                # Add time-based trends
                context += "\nTime-based trends:\n"

                # Create a copy to avoid modifying the original dataframe
                trend_df = valid_dates.copy()

                # Group by month
                trend_df['month'] = trend_df[date_column].dt.to_period('M')

                # Prepare aggregation dictionary based on available columns
                agg_dict = {}

                # For sent emails
                if 'total_sent' in trend_df.columns:
                    agg_dict['total_sent'] = 'sum'
                else:
                    # Count emails as a proxy for sent
                    trend_df['email_count'] = 1
                    agg_dict['email_count'] = 'sum'

                # For opened emails
                if 'total_opened' in trend_df.columns:
                    agg_dict['total_opened'] = 'sum'
                elif 'Open_Time' in trend_df.columns:
                    agg_dict['Open_Time'] = lambda x: x.notna().sum()
                elif 'Last_Open_Time' in trend_df.columns:
                    agg_dict['Last_Open_Time'] = lambda x: x.notna().sum()

                # For clicked emails
                if 'total_clicked' in trend_df.columns:
                    agg_dict['total_clicked'] = 'sum'
                elif 'Click_Time' in trend_df.columns:
                    agg_dict['Click_Time'] = lambda x: x.notna().sum()
                elif 'Last_Click_Time' in trend_df.columns:
                    agg_dict['Last_Click_Time'] = lambda x: x.notna().sum()

                # Group by month with the appropriate aggregation
                if agg_dict:
                    monthly_data = trend_df.groupby('month').agg(agg_dict).reset_index()

                    # Determine column names for calculations
                    sent_col = 'total_sent' if 'total_sent' in monthly_data.columns else 'email_count'

                    open_col = None
                    if 'total_opened' in monthly_data.columns:
                        open_col = 'total_opened'
                    elif 'Open_Time' in monthly_data.columns:
                        open_col = 'Open_Time'
                    elif 'Last_Open_Time' in monthly_data.columns:
                        open_col = 'Last_Open_Time'

                    click_col = None
                    if 'total_clicked' in monthly_data.columns:
                        click_col = 'total_clicked'
                    elif 'Click_Time' in monthly_data.columns:
                        click_col = 'Click_Time'
                    elif 'Last_Click_Time' in monthly_data.columns:
                        click_col = 'Last_Click_Time'

                    # Calculate monthly rates if possible
                    if open_col:
                        monthly_data['open_rate'] = (monthly_data[open_col] / monthly_data[sent_col] * 100).round(2)
                    else:
                        monthly_data['open_rate'] = 0

                    if click_col:
                        monthly_data['click_rate'] = (monthly_data[click_col] / monthly_data[sent_col] * 100).round(2)
                    else:
                        monthly_data['click_rate'] = 0

                    # Add monthly trends to context
                    context += "Monthly performance:\n"
                    for _, row in monthly_data.iterrows():
                        month = row['month'].strftime('%Y-%m')
                        context += f"  - {month}: Sent: {row[sent_col]}, Open rate: {row['open_rate']}%, Click rate: {row['click_rate']}%\n"

        # Add subject line performance if available
        if 'Subject' in df.columns:
            context += "\nTop performing subject lines (by open rate):\n"

            # Determine which columns to use for aggregation
            sent_col = None
            if 'total_sent' in df.columns:
                sent_col = 'total_sent'
            else:
                # Use count of emails as a proxy for sent
                df['email_count'] = 1
                sent_col = 'email_count'

            open_col = None
            if 'total_opened' in df.columns:
                open_col = 'total_opened'
            elif 'Open_Time' in df.columns:
                open_col = 'Open_Time'
                # Convert to boolean for aggregation
                df['opened'] = df['Open_Time'].notna()
                open_col = 'opened'
            elif 'Last_Open_Time' in df.columns:
                # Convert to boolean for aggregation
                df['opened'] = df['Last_Open_Time'].notna()
                open_col = 'opened'

            if open_col:
                # Group by subject and calculate open rates
                agg_dict = {sent_col: 'sum'}

                if open_col in ['opened']:
                    agg_dict[open_col] = 'sum'
                else:
                    agg_dict[open_col] = 'sum'

                subject_performance = df.groupby('Subject').agg(agg_dict).reset_index()

                # Calculate open rate
                subject_performance['open_rate'] = (subject_performance[open_col] / subject_performance[sent_col] * 100).round(2)

                # Sort by open rate and get top 3
                top_subjects = subject_performance.sort_values('open_rate', ascending=False).head(3)

                # Add to context
                for i, (_, row) in enumerate(top_subjects.iterrows(), 1):
                    context += f"  {i}. \"{row['Subject']}\" - Open rate: {row['open_rate']}%\n"
            else:
                context += "  (Open rate data not available for subject line analysis)\n"

    return context

def process_question(question, df):
    """
    Process a user question about campaign analytics.

    Args:
        question: The user's question
        df: DataFrame containing campaign analytics data

    Returns:
        str: The chatbot's response
    """
    try:
        # Load environment variables
        load_dotenv()

        # Initialize OpenAI client
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return "Error: OpenAI API key not found. Please set the OPENAI_API_KEY environment variable."

        client = OpenAI(api_key=api_key)

        # Get analytics context
        context = get_analytics_context(df)

        # Prepare system message with enhanced instructions
        system_message = f"""You are an analytics assistant that helps users understand their campaign performance data.
Use the following context to answer questions:

{context}

Guidelines for answering:
1. Be concise and direct in your answers.
2. When discussing metrics, include both the raw numbers and the interpretation (e.g., "The open rate is 25%, which is above industry average").
3. If asked about trends, mention whether metrics are improving or declining over time.
4. If asked about best performing elements, explain why they might be performing well.
5. If asked for recommendations, base them on the data patterns you observe.
6. If you don't know the answer, say so and suggest what data might help answer the question.
7. If the user asks for a comparison between products or time periods, provide the relevant metrics side by side.
8. End your response with "Do you have any more questions?" to encourage further interaction.

Remember that you're helping the user understand their marketing campaign performance to make better decisions."""

        # Prepare messages for the API call - no conversation history, just the current question
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": question}
        ]

        # Call the API
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            max_tokens=800,
            temperature=0.7
        )

        # Extract the response
        answer = response.choices[0].message.content

        # Check if we need to calculate additional data based on the question
        if "compare" in question.lower() or "comparison" in question.lower() or "versus" in question.lower() or " vs " in question.lower():
            # The user is asking for a comparison, let's try to enhance the answer with specific data
            try:
                # Extract product names or time periods from the question
                # This is a simplified approach - in a real implementation, you'd use NLP to extract entities
                product_column = None
                if 'product' in df.columns:
                    product_column = 'product'
                elif 'offering' in df.columns:
                    product_column = 'offering'
                elif 'Matched_Product' in df.columns:
                    product_column = 'Matched_Product'

                if product_column and any(product.lower() in question.lower() for product in df[product_column].unique()):
                    # The user is asking about specific products
                    # We've already included product comparisons in the context, so no need to add more here
                    pass
            except:
                # If any error occurs during the enhancement, just return the original answer
                pass

        return answer

    except Exception as e:
        return f"Error processing your question: {str(e)}"

def render_chatbot(df=None):
    """
    Render the chatbot interface.

    Args:
        df: DataFrame containing campaign analytics data
    """
    # Initialize session state
    init_chatbot_session()

    # Add chatbot toggle button
    st.markdown("""
    <style>
    .chat-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background-color: #8D06FE;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .chat-container:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }

    .chat-window {
        position: fixed;
        bottom: 90px;
        right: 20px;
        width: 350px;
        height: 500px;
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .chat-header {
        background-color: #8D06FE;
        color: white;
        padding: 10px 15px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-close {
        cursor: pointer;
        font-size: 18px;
    }

    .chat-messages {
        flex-grow: 1;
        padding: 15px;
        overflow-y: auto;
    }

    .chat-input {
        padding: 10px;
        border-top: 1px solid #eee;
    }
    </style>
    """, unsafe_allow_html=True)

    # Add chat toggle button
    st.markdown(f"""
    <div class="chat-container" id="chat-toggle-button">
        💬
    </div>

    <script>
    // Function to toggle the chatbot
    function toggleChatbot() {{
        const event = new Event('streamlit:toggleChatbot');
        window.dispatchEvent(event);
    }}

    // Add click event listener to the chat button
    document.getElementById('chat-toggle-button').addEventListener('click', function() {{
        toggleChatbot();
    }});

    // Listen for the custom event
    window.addEventListener('streamlit:toggleChatbot', function() {{
        // Use Streamlit's event mechanism to trigger a Python callback
        const button = document.querySelector('button[data-testid="baseButton-primary"]');
        if (button) {{
            button.click();
        }}
    }});
    </script>
    """, unsafe_allow_html=True)

    # Handle chat toggle event with a hidden button that JavaScript can click
    # Using a container to hide the button from view but keep it accessible to JavaScript
    with st.container():
        if st.button("Toggle Chat", key="toggle_chat_button", type="primary"):
            toggle_chatbot()

    # Add CSS to hide the button but keep it functional
    st.markdown("""
    <style>
    [data-testid="baseButton-primary"] {
        visibility: hidden;
        position: absolute;
        width: 0;
        height: 0;
        overflow: hidden;
    }
    </style>
    """, unsafe_allow_html=True)

    # Render chat window if visible
    if st.session_state.chatbot_visible:
        # Create a custom container for the chat window
        chat_window = st.container()

        with chat_window:
            # Create a chat window with custom styling
            st.markdown("""
            <style>
            .chat-window-container {
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                margin-bottom: 10px;
                max-height: 400px;
                overflow-y: auto;
            }

            .chat-header {
                background-color: #8D06FE;
                color: white;
                padding: 10px 15px;
                font-weight: bold;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            </style>

            <div class="chat-window-container">
                <div class="chat-header">
                    <span>Analytics Assistant</span>
                    <span id="chat-close-button" style="cursor: pointer;">✕</span>
                </div>
            """, unsafe_allow_html=True)

            # Display welcome message
            st.markdown("""
            Hi there! I'm your Analytics Assistant. Ask me anything about your campaign data,
            and I'll help you understand the performance metrics.
            """)

            # Create the input field and send button
            col1, col2 = st.columns([5, 1])

            with col1:
                user_question = st.text_input(
                    "Ask a question about your campaign analytics:",
                    key="chatbot_input",
                    placeholder="e.g., What's my overall open rate?"
                )

            with col2:
                send_button = st.button("Send", key="chatbot_send", use_container_width=True)

            # Add example questions to help users get started
            with st.expander("Example questions you can ask"):
                st.markdown("""
                - What's my overall open rate and click rate?
                - How is my email performance trending over time?
                - Which product has the best engagement?
                - What are my top performing subject lines?
                - Compare the performance of different products
                - What recommendations do you have to improve my open rates?
                - How does my click-to-open rate compare to industry standards?
                """)

                # Add a quick-select feature for example questions
                example_questions = [
                    "What's my overall open rate and click rate?",
                    "How is my email performance trending over time?",
                    "Which product has the best engagement?",
                    "What are my top performing subject lines?"
                ]

                for i, example in enumerate(example_questions):
                    if st.button(f"Ask: {example}", key=f"example_question_{i}"):
                        # Process the question and display the response
                        response = process_question(example, df)

                        # Display the question and response
                        st.markdown(f"**Your question:** {example}")
                        st.markdown(f"**Response:** {response}")

            # Add JavaScript to close the chat window
            st.markdown("""
            <script>
            document.getElementById('chat-close-button').addEventListener('click', function() {
                toggleChatbot();
            });
            </script>
            """, unsafe_allow_html=True)

            # Process user input
            if send_button and user_question:
                # Process the question
                response = process_question(user_question, df)

                # Display the question and response
                st.markdown(f"**Your question:** {user_question}")
                st.markdown(f"**Response:** {response}")

                # No need to clear the input or force a rerun
                # The input will be cleared automatically when the user submits a new question
