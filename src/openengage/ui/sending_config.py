"""
Sending Channel Configuration UI component for OpenEngage.
"""
import os
import json
import re
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
from dotenv import load_dotenv, set_key, find_dotenv
from typing import Dict, Any, List, Optional

# Import core modules
try:
    from core.email_sender import create_email_sender
    from core.whatsapp_sender import create_whatsapp_sender
    from core.whatsapp_template_manager import WhatsAppTemplateManager
except ImportError:
    # Handle relative imports when running as a module
    from openengage.core.email_sender import create_email_sender
    try:
        from openengage.core.whatsapp_sender import create_whatsapp_sender
        from openengage.core.whatsapp_template_manager import WhatsAppTemplateManager
    except ImportError:
        # These modules might not be available yet
        pass

def update_env_var(content: str, key: str, value: str) -> str:
    """Update an environment variable in the .env file content without adding quotes."""
    # Create the new line with the key-value pair
    new_line = f"{key}={value}"

    # Check if the key already exists in the content
    pattern = re.compile(f"^{key}=.*$", re.MULTILINE)
    if pattern.search(content):
        # Replace the existing line
        return pattern.sub(new_line, content)
    else:
        # Add a new line
        return content + "\n" + new_line

def save_sender_details(channel, esp, sender_name, sender_email, reply_to_email, active=False):
    """Save sender details to senderDetails.json file."""
    # Create the sender details object
    sender_details = {
        "channel": channel,
        "esp": esp,
        "sender_name": sender_name,
        "sender_email": sender_email,
        "reply_to_email": reply_to_email,
        "active": active
    }

    # Initialize the file contents
    if os.path.exists('data/senderDetails.json'):
        try:
            with open('data/senderDetails.json', 'r') as f:
                all_details = json.load(f)
        except json.JSONDecodeError:
            all_details = []
    else:
        # Create the directory if it doesn't exist
        os.makedirs('data', exist_ok=True)
        all_details = []

    # Check if this configuration already exists
    found = False
    for i, details in enumerate(all_details):
        if details.get("channel") == channel and details.get("esp") == esp:
            # Update the existing entry
            all_details[i] = sender_details
            found = True
            break

    # If not found, add it
    if not found:
        all_details.append(sender_details)

    # Save back to the file
    with open('data/senderDetails.json', 'w') as f:
        json.dump(all_details, f, indent=2)

    return True

def get_sender_details():
    """Get all sender details from senderDetails.json file."""
    if os.path.exists('data/senderDetails.json'):
        try:
            with open('data/senderDetails.json', 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            return []
    return []

def update_env_var(env_content, var_name, var_value):
    """Update an environment variable in the .env file content"""
    # Check if variable exists
    if f"{var_name}=" in env_content:
        # Replace existing variable
        lines = env_content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith(f"{var_name}="):
                lines[i] = f"{var_name}={var_value}"
                break
        return '\n'.join(lines)
    else:
        # Add new variable
        return f"{env_content}\n{var_name}={var_value}"

def get_esp_api_keys():
    """Get ESP API keys from .env file."""
    load_dotenv()
    return {
        "sparkpost": os.getenv("SPARKPOST_API_KEY", ""),
        "mailmodo": os.getenv("MAILMODO_API_KEY", ""),
        "amazon_ses": os.getenv("AMAZON_SES_API_KEY", ""),
        "twilio": os.getenv("TWILIO_API_KEY", ""),
        "gupshup": os.getenv("GUPSHUP_API_KEY", ""),
        "messagebird": os.getenv("MESSAGEBIRD_API_KEY", "")
    }

def save_campaign_settings(time_settings_active, time_settings_non_active, active_frequency, non_active_frequency):
    """Save campaign settings to campaignSettings.json file."""
    # Create the campaign settings object
    settings = {
        "time_settings_active": time_settings_active,
        "time_settings_non_active": time_settings_non_active,
        "active_audience_frequency": active_frequency,
        "non_active_audience_frequency": non_active_frequency,
        "updated_at": datetime.now().isoformat()
    }

    # Create the directory if it doesn't exist
    os.makedirs('data', exist_ok=True)

    # Save to file
    with open('data/campaignSettings.json', 'w') as f:
        json.dump(settings, f, indent=2)

    return True

def get_campaign_settings():
    """Get campaign settings from campaignSettings.json file."""
    default_settings = {
        "time_settings_active": {"mode": "best_time", "custom_times": []},
        "time_settings_non_active": {"mode": "custom", "custom_times": []},
        "active_audience_frequency": "moderate",
        "non_active_audience_frequency": "moderate"
    }

    if os.path.exists('data/campaignSettings.json'):
        try:
            with open('data/campaignSettings.json', 'r') as f:
                settings = json.load(f)

                # Handle backward compatibility - if old format is detected, convert it
                if "time_settings" in settings and "time_settings_active" not in settings:
                    settings["time_settings_active"] = settings["time_settings"]
                    settings["time_settings_non_active"] = {
                        "mode": "custom",
                        "custom_times": settings["time_settings"].get("custom_times", [])
                    }

                # Ensure all required keys are present
                for key in default_settings:
                    if key not in settings:
                        settings[key] = default_settings[key]

                return settings
        except json.JSONDecodeError:
            return default_settings
    return default_settings

def display_sending_configuration():
    """Display the sending channel configuration interface"""
    # Load environment variables
    load_dotenv()

    st.write("## 📨 Sending Channel Configuration")

    # Create tabs
    tab1, tab2 = st.tabs(["Channel Setup", "Campaign Settings"])

    # First tab - existing functionality
    with tab1:
        display_channel_setup()

    # Second tab - new campaign settings
    with tab2:
        display_campaign_settings()

def display_channel_setup():
    """Display the channel setup interface (original functionality)"""
    # Load environment variables
    load_dotenv()

    # Initialize session state
    if "config_step" not in st.session_state:
        st.session_state.config_step = 0  # Start with initial selection step

    if "selected_channel" not in st.session_state:
        st.session_state.selected_channel = None

    if "selected_esp" not in st.session_state:
        st.session_state.selected_esp = None

    if "api_key_saved" not in st.session_state:
        st.session_state.api_key_saved = False

    if "sender_details_saved" not in st.session_state:
        st.session_state.sender_details_saved = False

    if "configuration_mode" not in st.session_state:
        st.session_state.configuration_mode = "setup"  # Options: setup, test, activate

    # Step 0: Choose to configure new channel or manage existing ones
    if st.session_state.config_step == 0:
        st.write("### Choose an Action")

        # Get existing configurations
        existing_configs = get_sender_details()

        col1, col2 = st.columns(2)

        with col1:
            if st.button("➕ Configure New Channel", key="new_channel", type="primary"):
                st.session_state.config_step = 1
                st.rerun()

        with col2:
            if existing_configs and st.button("⚙️ Manage Existing Channels", key="manage_channels", type="primary"):
                st.session_state.config_step = "manage"
                st.rerun()

        # Show summary of existing configurations
        if existing_configs:
            st.write("### Current Channel Configurations")

            # Group by channel type
            channel_groups = {}
            for config in existing_configs:
                channel = config.get("channel")
                if channel not in channel_groups:
                    channel_groups[channel] = []
                channel_groups[channel].append(config)

            # Display each channel type
            for channel, configs in channel_groups.items():
                with st.expander(f"{channel} Channels ({len(configs)})"):
                    for idx, config in enumerate(configs):
                        active_status = "✅ Active" if config.get("active") else "⚪ Inactive"

                        # Create columns for the channel info and test button
                        info_col, button_col = st.columns([3, 1])

                        with info_col:
                            st.write(f"**{idx+1}. {config.get('esp')}** - {active_status}")
                            st.write(f"Sender: {config.get('sender_name')} <{config.get('sender_email')}>")

                        # Add Test Message button for WhatsApp channels
                        if channel == "WhatsApp":
                            with button_col:
                                if st.button("Test Messages", key=f"test_whatsapp_{idx}"):
                                    # Set up session state for the test section
                                    st.session_state.selected_channel = config.get("channel")
                                    st.session_state.selected_esp = config.get("esp")
                                    st.session_state.configuration_mode = "test"
                                    st.session_state.show_test_section = True
                                    st.rerun()

                        st.write("---")

    # Manage existing configurations
    elif st.session_state.config_step == "manage":
        st.write("### Manage Existing Channel Configurations")

        # Get configurations
        existing_configs = get_sender_details()

        if not existing_configs:
            st.warning("No channel configurations found")
            if st.button("Back", key="manage_back_empty"):
                st.session_state.config_step = 0
                st.rerun()
            return

        # Group by channel type for selection
        channel_options = list(set(config.get("channel") for config in existing_configs))
        selected_channel_type = st.selectbox(
            "Select Channel Type",
            channel_options,
            key="manage_channel_type"
        )

        # Filter configs by selected channel type
        filtered_configs = [c for c in existing_configs if c.get("channel") == selected_channel_type]

        # Create a selection option for each config
        config_options = [f"{c.get('esp')} - {c.get('sender_name')} <{c.get('sender_email')}>" for c in filtered_configs]
        selected_config_idx = st.selectbox(
            "Select Configuration to Manage",
            range(len(config_options)),
            format_func=lambda i: config_options[i],
            key="manage_config_select"
        )

        selected_config = filtered_configs[selected_config_idx]

        # Show selected config details
        st.write("### Configuration Details")
        st.write(f"**Channel:** {selected_config.get('channel')}")
        st.write(f"**Service Provider:** {selected_config.get('esp')}")
        st.write(f"**Sender Name:** {selected_config.get('sender_name')}")
        st.write(f"**From Email:** {selected_config.get('sender_email')}")
        st.write(f"**Reply-to Email:** {selected_config.get('reply_to_email')}")

        # Current status
        is_active = selected_config.get('active', False)
        st.write(f"**Status:** {'✅ Active' if is_active else '⚪ Inactive'}")

        # Action buttons
        st.write("### Actions")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📝 Edit Sender Details", key="edit_sender_details"):
                # Set up session state for editing
                st.session_state.selected_channel = selected_config.get("channel")
                st.session_state.selected_esp = selected_config.get("esp")
                st.session_state.config_step = 4  # Go to sender details step
                st.rerun()

        with col2:
            if st.button("🔑 Update API Key", key="update_api_key"):
                # Set up session state for editing API key
                st.session_state.selected_channel = selected_config.get("channel")
                st.session_state.selected_esp = selected_config.get("esp")
                st.session_state.config_step = 3  # Go to API key step
                st.rerun()

        with col3:
            # Toggle active status
            action_text = "🔴 Deactivate" if is_active else "🟢 Activate"
            if st.button(action_text, key="toggle_active"):
                # Update active status
                active_updated = False
                for idx, config in enumerate(existing_configs):
                    if (config.get("channel") == selected_config.get("channel") and
                        config.get("esp") == selected_config.get("esp")):

                        # If activating, first deactivate all others of this channel type
                        if not is_active:
                            for i, c in enumerate(existing_configs):
                                if c.get("channel") == selected_config.get("channel"):
                                    existing_configs[i]["active"] = False

                        # Now toggle the selected one
                        existing_configs[idx]["active"] = not is_active
                        active_updated = True
                        break

                if active_updated:
                    # Save updated configurations
                    with open('data/senderDetails.json', 'w') as f:
                        json.dump(existing_configs, f, indent=2)

                    if is_active:
                        st.success(f"Channel deactivated successfully!")
                    else:
                        st.success(f"Channel activated successfully!")

                    # Refresh the page
                    st.rerun()

        # Back button
        if st.button("Back to Main Menu", key="manage_back"):
            st.session_state.config_step = 0
            st.rerun()

    # Step 1: Select Channel Category
    elif st.session_state.config_step == 1:
        st.write("### Step 1: Select Channel Category")
        channel_options = ["Email", "WhatsApp", "SMS"]
        selected_channel = st.selectbox(
            "Select the channel category",
            channel_options,
            index=channel_options.index(st.session_state.selected_channel) if st.session_state.selected_channel else 0
        )

        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("Back", key="step1_back"):
                st.session_state.config_step = 0
                st.rerun()

        with col2:
            if st.button("Continue", key="step1_continue"):
                st.session_state.selected_channel = selected_channel
                st.session_state.config_step = 2
                st.rerun()

    # Step 2: Select ESP
    elif st.session_state.config_step == 2:
        st.write(f"### Step 2: Select {st.session_state.selected_channel} Service Provider")

        esp_options = []
        if st.session_state.selected_channel == "Email":
            esp_options = ["SparkPost", "Mailmodo", "Amazon SES"]
        elif st.session_state.selected_channel == "WhatsApp":
            esp_options = ["Twilio","Gupshup", "MessageBird"]
        elif st.session_state.selected_channel == "SMS":
            esp_options = ["Twilio","Gupshup", "MessageBird"]

        selected_esp = st.selectbox(
            f"Select your {st.session_state.selected_channel} service provider",
            esp_options,
            index=esp_options.index(st.session_state.selected_esp) if st.session_state.selected_esp else 0
        )

        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("Back", key="step2_back"):
                st.session_state.config_step = 1
                st.rerun()

        with col2:
            if st.button("Continue", key="step2_continue"):
                st.session_state.selected_esp = selected_esp
                st.session_state.config_step = 3
                st.rerun()

    # Step 3: Enter API Key
    elif st.session_state.config_step == 3:
        st.write(f"### Step 3: Enter {st.session_state.selected_esp} API Key")

        # Get the current API key from .env, if it exists
        api_keys = get_esp_api_keys()
        api_key_env_name = ""
        current_api_key = None  # Initialize with None

        if st.session_state.selected_esp == "SparkPost":
            api_key_env_name = "SPARKPOST_API_KEY"
            current_api_key = api_keys.get("sparkpost", "")
        elif st.session_state.selected_esp == "Mailmodo":
            api_key_env_name = "MAILMODO_API_KEY"
            current_api_key = api_keys.get("mailmodo", "")
        elif st.session_state.selected_esp == "Amazon SES":
            api_key_env_name = "AMAZON_SES_API_KEY"
            current_api_key = api_keys.get("amazon_ses", "")
        elif st.session_state.selected_esp == "Twilio":
            api_key_env_name = "TWILIO_API_KEY"
            current_api_key = api_keys.get("twilio", "")
        elif st.session_state.selected_esp == "Gupshup":
            api_key_env_name = "GUPSHUP_API_KEY"
            current_api_key = api_keys.get("gupshup", "")
        elif st.session_state.selected_esp == "MessageBird":
            api_key_env_name = "MESSAGEBIRD_API_KEY"
            current_api_key = api_keys.get("messagebird", "")

        # Display current API key status without showing the key
        if current_api_key:
            st.success(f"✅ {st.session_state.selected_esp} API Key is configured")

            # Option to update without showing the existing key
            st.info("Enter a new API key below to update the existing configuration")

        # API Key input - always start with empty field for security
        api_key = st.text_input(
            f"Enter your {st.session_state.selected_esp} API Key",
            type="password",
            value="",
            key="api_key_input",
            help=f"Enter your {st.session_state.selected_esp} API Key. Leave blank to keep existing key."
        )

        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("Back", key="step3_back"):
                if st.session_state.config_step == "manage":
                    st.session_state.config_step = "manage"
                else:
                    st.session_state.config_step = 2
                st.rerun()

        with col2:
            if st.button("Save API Key", key="step3_save", type="primary"):
                if api_key:
                    if len(api_key) >= 10:  # Basic validation
                        # Save the API key to session state and .env file
                        dotenv_path = find_dotenv()
                        if not dotenv_path:
                            dotenv_path = os.path.join(os.getcwd(), '.env')

                        # Read the current .env file
                        with open(dotenv_path, 'r') as f:
                            env_content = f.read()

                        # Update the API key without quotes
                        env_content = update_env_var(env_content, api_key_env_name, api_key)

                        # Write back to the file
                        with open(dotenv_path, 'w') as f:
                            f.write(env_content)

                        st.success("API Key saved successfully!")
                        st.session_state.api_key_saved = True
                        st.session_state.config_step = 4
                        st.rerun()
                    else:
                        st.error(f"Please enter a valid {st.session_state.selected_esp} API Key.")
                elif current_api_key:
                    # User chose to keep the existing key
                    st.success("Keeping existing API Key!")
                    st.session_state.api_key_saved = True
                    st.session_state.config_step = 4
                    st.rerun()
                else:
                    st.error(f"Please enter a valid {st.session_state.selected_esp} API Key.")

    # Step 4: Configure Sender Details
    elif st.session_state.config_step == 4:
        st.write("### Step 4: Configure Sender Details")

        # Get existing sender details for this channel/ESP
        sender_details = get_sender_details()
        current_details = {}

        for details in sender_details:
            if (details.get("channel") == st.session_state.selected_channel and
                details.get("esp") == st.session_state.selected_esp):
                current_details = details
                break

        # Different fields based on channel type
        if st.session_state.selected_channel == "Email":
            # Email-specific fields

            # Sender Name
            sender_name = st.text_input(
                "Sender Name",
                value=current_details.get("sender_name", "OpenEngage Team"),
                help="The name that will appear as the sender"
            )

            # Sender Email (From)
            sender_email = st.text_input(
                "From Email Address",
                value=current_details.get("sender_email", "<EMAIL>"),
                help="The email address that will appear as the sender"
            )

            # Reply-to Email
            reply_to_email = st.text_input(
                "Reply-to Email Address",
                value=current_details.get("reply_to_email", "<EMAIL>"),
                help="The email address that recipients will reply to"
            )

            col1, col2 = st.columns([1, 1])
            with col1:
                if st.button("Back", key="step4_back"):
                    st.session_state.config_step = 3
                    st.rerun()

            with col2:
                if st.button("Save Sender Details", key="step4_save", type="primary"):
                    # Validate email addresses
                    email_pattern = r'^[\w\.-]+@[\w\.-]+\.[a-zA-Z]{2,}$'

                    if not re.match(email_pattern, sender_email):
                        st.error("Please enter a valid sender email address.")
                    elif not re.match(email_pattern, reply_to_email):
                        st.error("Please enter a valid reply-to email address.")
                    else:
                        # Save sender details to JSON file
                        save_sender_details(
                            st.session_state.selected_channel,
                            st.session_state.selected_esp,
                            sender_name,
                            sender_email,
                            reply_to_email
                        )

                        st.success("Sender details saved successfully!")
                        st.session_state.sender_details_saved = True
                        st.session_state.config_step = 5
                        st.rerun()

        elif st.session_state.selected_channel == "WhatsApp":
            # WhatsApp-specific fields

            # Business Name
            sender_name = st.text_input(
                "Business Name",
                value=current_details.get("sender_name", "Analytics Vidhya"),
                help="The business name that will appear in WhatsApp messages"
            )

            # Phone Number
            phone_number = st.text_input(
                "WhatsApp Phone Number",
                value=current_details.get("sender_email", "************"),  # Using sender_email field to store phone number
                help="The phone number with country code (e.g., ************)"
            )

            # Show information about WhatsApp verification
            st.info("Note: Your WhatsApp business account and phone number must be verified with Gupshup before sending messages.")

            col1, col2 = st.columns([1, 1])
            with col1:
                if st.button("Back", key="step4_back"):
                    st.session_state.config_step = 3
                    st.rerun()

            with col2:
                if st.button("Save Sender Details", key="step4_save", type="primary"):
                    # Validate phone number (basic validation)
                    phone_pattern = r'^\d{10,15}$'

                    # Remove any non-digit characters for validation
                    clean_phone = ''.join(filter(str.isdigit, phone_number))

                    if not re.match(phone_pattern, clean_phone):
                        st.error("Please enter a valid phone number with country code (10-15 digits).")
                    else:
                        # Save sender details to JSON file
                        # Note: We're using sender_email field to store phone number
                        # and reply_to_email is not used for WhatsApp
                        save_sender_details(
                            st.session_state.selected_channel,
                            st.session_state.selected_esp,
                            sender_name,
                            phone_number,  # Store phone number in sender_email field
                            ""  # Empty reply_to_email for WhatsApp
                        )

                        # Also update environment variables
                        dotenv_path = find_dotenv()
                        if dotenv_path:
                            with open(dotenv_path, 'r') as f:
                                env_content = f.read()

                            # Update sender name and phone number
                            env_content = update_env_var(env_content, "WHATSAPP_SENDER_NAME", sender_name)
                            env_content = update_env_var(env_content, "WHATSAPP_PHONE_NUMBER", phone_number)

                            # Write back to the file
                            with open(dotenv_path, 'w') as f:
                                f.write(env_content)

                        st.success("WhatsApp sender details saved successfully!")
                        st.session_state.sender_details_saved = True
                        st.session_state.config_step = 5
                        st.rerun()

        else:
            # Generic fields for other channels

            # Sender Name
            sender_name = st.text_input(
                "Sender Name",
                value=current_details.get("sender_name", "OpenEngage Team"),
                help="The name that will appear as the sender"
            )

            # Sender ID
            sender_id = st.text_input(
                "Sender ID",
                value=current_details.get("sender_email", ""),
                help="The ID that will be used to send messages"
            )

            col1, col2 = st.columns([1, 1])
            with col1:
                if st.button("Back", key="step4_back"):
                    st.session_state.config_step = 3
                    st.rerun()

            with col2:
                if st.button("Save Sender Details", key="step4_save", type="primary"):
                    if not sender_id:
                        st.error("Please enter a valid sender ID.")
                    else:
                        # Save sender details to JSON file
                        save_sender_details(
                            st.session_state.selected_channel,
                            st.session_state.selected_esp,
                            sender_name,
                            sender_id,
                            ""
                        )

                        st.success("Sender details saved successfully!")
                        st.session_state.sender_details_saved = True
                        st.session_state.config_step = 5
                        st.rerun()

    # Step 5: Choose Action
    elif st.session_state.config_step == 5:
        st.write("### Channel Configuration Complete")
        st.success(f"✅ Your {st.session_state.selected_channel} channel using {st.session_state.selected_esp} has been configured!")

        st.write("### Choose Action")
        st.write("What would you like to do with this configuration?")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("Template Configuration", key="test_config", type="primary"):
                st.session_state.configuration_mode = "test"
                st.rerun()

        with col2:
            if st.button("🚀 Activate Sending Channel", key="activate_channel", type="primary"):
                # Activate this channel in the JSON file
                sender_details = get_sender_details()

                # First deactivate all channels of this type
                for i, details in enumerate(sender_details):
                    if details.get("channel") == st.session_state.selected_channel:
                        sender_details[i]["active"] = False

                # Now activate the selected one
                for i, details in enumerate(sender_details):
                    if (details.get("channel") == st.session_state.selected_channel and
                        details.get("esp") == st.session_state.selected_esp):
                        sender_details[i]["active"] = True
                        break

                # Save the updated details
                with open('data/senderDetails.json', 'w') as f:
                    json.dump(sender_details, f, indent=2)

                st.session_state.configuration_mode = "activate"
                st.rerun()

    # Test Configuration Mode
    if st.session_state.configuration_mode == "test":
        if st.session_state.selected_channel == "Email":
            # Show required CSV format
            with st.expander("📝 Required CSV Format"):
                st.write("""
                Your CSV file must have these exact columns:
                - **user_email**: Email address of the recipient
                - **Subject**: Email subject line
                - **Mail_Content**: HTML content of the email
                - **Preheader** (optional): Text that appears in the email preview

                Sample CSV Format:
                ```csv
                user_email,Subject,Mail_Content,Preheader
                <EMAIL>,Your Special Offer,<p>Hello John...</p>,Check out our latest offer
                <EMAIL>,Product Update,<p>Hello Jane...</p>,New features available
                ```
                """)

            # File uploader
            uploaded_file = st.file_uploader("Choose a CSV file", type='csv', key="send_mail_csv")

        elif st.session_state.selected_channel == "WhatsApp":
            # No need to initialize template manager here anymore
            # We'll use the functions from gupshup_whatsapp_sender.py instead

            # Create tabs for template management and testing
            template_tab, test_tab = st.tabs(["Template Management", "Test Message"])

            with template_tab:
                # Instructions for creating templates on Gupshup
                st.write("""
                #### Step 1: Create Templates on Gupshup Console

                1. **Log in to your Gupshup account** at [console.gupshup.io](https://console.gupshup.io/)
                2. **Navigate to WhatsApp > Templates**
                3. **Click "Create Template"**
                4. **Fill in the template details:**
                   - **Category**: Choose appropriate category
                   - **Template Name**: Give your template a unique name
                   - **Language**: Select the language.
                   - **Buttons**: Optional call-to-action buttons.
                   - **Body**: Copy the below body text to use in your template creation.
                """)

                st.info("""
                ```
                Hi {{1}},

                {{2}}

                Best Regards,
                Analytics Vidhya
                ```
                """)

                st.write("#### After Creating Templates")
                st.write("1. Wait for WhatsApp to approve your templates")
                st.write("2. Once approved, proceed to template mapping by clicking the below button.")

                if st.button("Proceed", key="proceed_to_mapping"):
                    # Redirect to WhatsApp Template Addition page
                    st.session_state.show_whatsapp_template_addition = True
                    st.session_state.reset_other_views('show_whatsapp_template_addition')
                    st.rerun()

                # Initialize session state for template mapping visibility
                if 'show_template_mapping' not in st.session_state:
                    st.session_state.show_template_mapping = False

                # Load products from product_details.json (outside the conditional block)
                products = []
                try:
                    with open('data/product_details.json', 'r') as f:
                        products = json.load(f)
                except Exception as e:
                    st.error(f"Could not load product details: {str(e)}")

                # Only show template mapping if the user has clicked "Proceed"
                if st.session_state.show_template_mapping:
                    # Map templates to products directly
                    st.write("#### Map Template IDs to Products")

                    if not products:
                        st.warning("No products found in product_details.json. Please check the file.")
                    else:
                        # Extract product names for dropdown
                        product_names = [product.get('Product_Name', '') for product in products]
                        selected_product_index = st.selectbox(
                            "Select Product",
                            range(len(product_names)),
                            format_func=lambda i: product_names[i],
                            key="map_product"
                        )

                        # Get selected product details
                        selected_product = product_names[selected_product_index]

                        # Import the gupshup_whatsapp_sender module for template management
                        try:
                            # Try direct import first
                            from core.gupshup_whatsapp_sender import get_template_details
                            from core.whatsapp_template_manager import WhatsAppTemplateManager
                            template_manager = WhatsAppTemplateManager()
                        except ImportError:
                            # Try package import
                            from openengage.core.gupshup_whatsapp_sender import get_template_details
                            from openengage.core.whatsapp_template_manager import WhatsAppTemplateManager
                            template_manager = WhatsAppTemplateManager()

                        # Get current template ID for this product
                        current_template_id = template_manager.get_product_template(selected_product)

                        # Template ID input
                        template_id = st.text_input(
                            "Template ID from Gupshup",
                            value=current_template_id if current_template_id else "",
                            help="Enter the template ID provided by Gupshup after template approval",
                            key="template_id_input"
                        )

                        # Button type selection
                        button_type = st.selectbox(
                            "Buttons (Optional)",
                            ["None", "Add Quick Reply", "Visit Website", "Call Phone Number", "Copy Offer Code"],
                            key="button_type"
                        )

                        # Button text inputs based on selection
                        button_text_1 = ""
                        button_text_2 = ""
                        button_url = ""
                        button_phone = ""
                        button_code = ""

                        if button_type == "Add Quick Reply":
                            button_text_1 = st.text_input("Quick Reply Button 1 Text", key="quick_reply_1")
                            button_text_2 = st.text_input("Quick Reply Button 2 Text (Optional)", key="quick_reply_2")
                        elif button_type == "Visit Website":
                            button_text_1 = st.text_input("Website Button Text", key="website_text")
                            button_url = st.text_input("Website URL", key="website_url")
                        elif button_type == "Call Phone Number":
                            button_text_1 = st.text_input("Call Button Text", key="call_text")
                            button_phone = st.text_input("Phone Number", key="call_phone")
                        elif button_type == "Copy Offer Code":
                            button_text_1 = st.text_input("Copy Code Button Text", key="code_text")
                            button_code = st.text_input("Offer Code", key="offer_code")

                        # Show current template if exists
                        if current_template_id:
                            st.info(f"Current template ID for {selected_product}: **{current_template_id}**")

                        # Map button
                        button_text = "Add Template Mapping"
                        if st.button(button_text, key="map_button"):
                            if not template_id:
                                st.error("Please enter a Template ID")
                            else:
                                # Create button data
                                button_data = {
                                    "type": button_type,
                                    "text_1": button_text_1,
                                    "text_2": button_text_2,
                                    "url": button_url,
                                    "phone": button_phone,
                                    "code": button_code
                                }

                                # Create a template entry with button data
                                success = template_manager.add_template(
                                    template_id=template_id,
                                    template_name=f"{selected_product} Template",
                                    description=f"Template for {selected_product}",
                                    button_data=button_data
                                    # Using default values for variable_count and example_text
                                )

                                if success:
                                    # Map the template to the product (allowing multiple templates per product)
                                    if template_manager.add_product_template_mapping(selected_product, template_id):
                                        st.success(f"Template ID mapped to product '{selected_product}' successfully!")
                                    else:
                                        st.error("Failed to map template to product")
                                else:
                                    st.error("Failed to save template")

                # Add a link to the WhatsApp Template Addition module
                st.info("You can manage your WhatsApp templates in the WhatsApp Template Addition module.")

                # Add a button to go to WhatsApp Template Addition
                if st.button("Go to WhatsApp Template Addition", key="goto_template_addition"):
                    st.session_state.show_whatsapp_template_addition = True
                    st.session_state.reset_other_views('show_whatsapp_template_addition')
                    st.rerun()

            with test_tab:
                # Initialize session state for test section visibility
                if 'show_test_section' not in st.session_state:
                    st.session_state.show_test_section = False

                st.write("### Test WhatsApp Message")

                # Add Test Messages button
                if not st.session_state.show_test_section:
                    if st.button("Test Messages", key="test_messages_button"):
                        st.session_state.show_test_section = True
                        st.rerun()

                # Only show test section if the user has clicked "Test Messages"
                if st.session_state.show_test_section:
                    # We're using the products variable loaded earlier
                    if not products:
                        st.warning("No products found in product_details.json. Please check the file.")
                    else:
                        # Import the gupshup_whatsapp_sender module
                        try:
                            # Try direct import first
                            from core.gupshup_whatsapp_sender import get_unmapped_products, get_product_templates, get_template_details, send_test_whatsapp_message
                        except ImportError:
                            # Try package import
                            from openengage.core.gupshup_whatsapp_sender import get_unmapped_products, get_product_templates, get_template_details, send_test_whatsapp_message

                        # Check for unmapped products
                        product_names = [product.get('Product_Name', '') for product in products]
                        unmapped_products = get_unmapped_products(product_names)

                        if unmapped_products:
                            unmapped_list = ", ".join(unmapped_products)
                            st.warning(f"The following products are not mapped to any template: {unmapped_list}")
                            st.info("You can map templates in the **WhatsApp Template Addition** module.")

                            # Add a button to go to WhatsApp Template Addition
                            if st.button("Go to WhatsApp Template Addition", key="goto_templates_from_test"):
                                st.session_state.show_whatsapp_template_addition = True
                                st.session_state.reset_other_views('show_whatsapp_template_addition')
                                st.rerun()
                        # Extract product names for dropdown
                        product_names = [product.get('Product_Name', '') for product in products]
                        selected_product_index = st.selectbox(
                            "Select Product",
                            range(len(product_names)),
                            format_func=lambda i: product_names[i],
                            key="test_product"
                        )

                        # Get selected product details
                        selected_product = product_names[selected_product_index]

                        # Get template IDs for this product
                        template_ids = get_product_templates(selected_product)

                        if not template_ids:
                            st.warning(f"No templates mapped for product '{selected_product}'. Please map templates first.")
                        else:
                            # Create template options for dropdown
                            template_options = []
                            for tid in template_ids:
                                template = get_template_details(template_id=tid)
                                if template:
                                    # Get button info if available
                                    button_info = ""
                                    if "button_data" in template and template["button_data"]["type"] != "None":
                                        button_info = f" ({template['button_data']['type']})"

                                    template_options.append((tid, f"{template.get('template_name', 'Unknown')} - {tid}{button_info}"))

                            # Template selection dropdown
                            selected_template_index = st.selectbox(
                                "Select Template",
                                range(len(template_options)),
                                format_func=lambda i: template_options[i][1],
                                key="test_template_select"
                            )

                            # Get selected template
                            selected_template_id = template_options[selected_template_index][0]
                            template = get_template_details(template_id=selected_template_id)

                            if not template:
                                st.error(f"Template with ID '{selected_template_id}' not found. Please check your template mappings.")
                            else:
                                st.success(f"Using template: {template.get('template_name')} (ID: {selected_template_id})")

                                # Variable inputs - simplified to just {{1}} and {{2}}
                                st.write("#### Enter Variable Values")
                                variable_values = []

                                # First variable is always first name
                                first_name = st.text_input("First Name ({{1}})",
                                                        value="John",
                                                        help="Recipient's first name")
                                variable_values.append(first_name)

                                # Second variable is personalized content
                                personalized_content = st.text_area("Personalized Content ({{2}})",
                                                                value="We have an exciting update about our product that we think you'll love!",
                                                                help="Main message content")
                                variable_values.append(personalized_content)

                                # Recipient phone number
                                recipient_phone = st.text_input("Recipient Phone Number",
                                                              value="91",
                                                              help="Enter the phone number with country code, e.g., 919876543210")

                                # Send test message button
                                if st.button("Send Test Message", key="send_test", type="primary"):
                                    if not recipient_phone or recipient_phone == "91":
                                        st.error("Please enter a valid recipient phone number")
                                    else:
                                        # Send test message using the gupshup_whatsapp_sender module
                                        with st.spinner("Sending test message..."):
                                            try:
                                                result = send_test_whatsapp_message(
                                                    recipient_phone=recipient_phone,
                                                    selected_template_id=selected_template_id,
                                                    variable_values=variable_values,
                                                    selected_esp=st.session_state.selected_esp
                                                )

                                                if result.get("total_accepted", 0) > 0:
                                                    st.success("✅ Test message sent successfully!")

                                                    # Show message details
                                                    message_data = result.get("message_data")
                                                    if message_data is not None and not message_data.empty:
                                                        st.write("#### Message Details")
                                                        st.write(f"**Status:** {message_data.iloc[0]['Status']}")
                                                        st.write(f"**Message ID:** {message_data.iloc[0]['Message_ID']}")
                                                        st.write(f"**Sent at:** {message_data.iloc[0]['Time_Send']}")
                                                else:
                                                    error_msg = "Unknown error"
                                                    if result.get('errors') and len(result.get('errors', [])) > 0:
                                                        error_msg = result.get('errors')[0]

                                                    st.error(f"Failed to send test message: {error_msg}")

                                                    # Show troubleshooting tips
                                                    st.write("#### Troubleshooting Tips")
                                                    st.write("1. Make sure your API key is correct")
                                                    st.write("2. Verify that the template ID exists in Gupshup")
                                                    st.write("3. Check that the phone number is in the correct format (with country code)")
                                                    st.write("4. Ensure the template variables match what's expected by Gupshup")
                                            except Exception as e:
                                                st.error(f"Error sending test message: {str(e)}")

                                                # Show troubleshooting tips
                                                st.write("#### Troubleshooting Tips")
                                                st.write("1. Make sure your API key is correct")
                                                st.write("2. Verify that the template ID exists in Gupshup")
                                                st.write("3. Check that the phone number is in the correct format (with country code)")
                                                st.write("4. Ensure the template variables match what's expected by Gupshup")

        # Process uploaded file for Email channel
        if st.session_state.selected_channel == "Email" and uploaded_file is not None:
                try:
                    # Read CSV
                    df = pd.read_csv(uploaded_file)

                    # Validate required columns
                    required_columns = ['user_email', 'Subject', 'Mail_Content']
                    missing_columns = [col for col in required_columns if col not in df.columns]

                    if missing_columns:
                        st.error(f"Missing required columns: {', '.join(missing_columns)}")
                    else:
                        # Show preview
                        st.write("### Preview")
                        st.write(f"Total emails to send: {len(df)}")
                        st.dataframe(df[['user_email', 'Subject']].head(5))

                        # Send button
                        if st.button("Send Test Emails Now", type="primary"):
                            with st.spinner("Sending test emails..."):
                                try:
                                    # Import the email sender
                                    try:
                                        # Try direct import first
                                        from core.email_sender import create_email_sender
                                    except ImportError:
                                        # Try package import
                                        from openengage.core.email_sender import create_email_sender

                                    # Get API keys
                                    api_keys = get_esp_api_keys()

                                    # Get sender details
                                    sender_details = get_sender_details()
                                    current_details = {}

                                    for details in sender_details:
                                        if (details.get("channel") == st.session_state.selected_channel and
                                            details.get("esp") == st.session_state.selected_esp):
                                            current_details = details
                                            break

                                    # Configure sender based on channel type
                                    if st.session_state.selected_channel == "Email":
                                        # Configure email sender
                                        if st.session_state.selected_esp.lower() == "sparkpost":
                                            api_key = api_keys.get("sparkpost", "")
                                            sender = create_email_sender("sparkpost", api_key)
                                        elif st.session_state.selected_esp.lower() == "mailmodo":
                                            api_key = api_keys.get("mailmodo", "")
                                            sender = create_email_sender("mailmodo", api_key)
                                        elif st.session_state.selected_esp.lower() == "amazon_ses":
                                            api_key = api_keys.get("amazon_ses", "")
                                            sender = create_email_sender("amazon_ses", api_key)

                                        # Set sender details
                                        if current_details:
                                            sender.set_sender_details(
                                                current_details.get("sender_name", ""),
                                                current_details.get("sender_email", ""),
                                                current_details.get("reply_to_email", "")
                                            )
                                    elif st.session_state.selected_channel == "WhatsApp":
                                        # Import WhatsApp sender
                                        from core.whatsapp_sender import create_whatsapp_sender

                                        # Configure WhatsApp sender
                                        if st.session_state.selected_esp.lower() == "gupshup":
                                            api_key = api_keys.get("gupshup", "")
                                            sender = create_whatsapp_sender("gupshup", api_key)
                                        elif st.session_state.selected_esp.lower() == "twilio":
                                            api_key = api_keys.get("twilio", "")
                                            sender = create_whatsapp_sender("twilio", api_key)
                                        elif st.session_state.selected_esp.lower() == "messagebird":
                                            api_key = api_keys.get("messagebird", "")
                                            sender = create_whatsapp_sender("messagebird", api_key)

                                        # Set sender details for WhatsApp
                                        if current_details:
                                            sender.set_sender_details(
                                                current_details.get("sender_name", ""),
                                                current_details.get("sender_email", "")  # Using email field for phone number
                                            )

                                    # Send the emails
                                    results = sender.send_emails(df)

                                    if results.get("errors"):
                                        st.error(f"Encountered {len(results['errors'])} errors while sending emails")
                                        for error in results["errors"]:
                                            st.error(error)
                                    else:
                                        st.success(f"Successfully sent {results['total_accepted']} test emails!")

                                        # Save results to CSV
                                        csv = results["email_data"].to_csv(index=False)
                                        st.download_button(
                                            label="Download Results CSV",
                                            data=csv,
                                            file_name="email_send_results.csv",
                                            mime="text/csv",
                                            key="download_email_results"
                                        )
                                except Exception as e:
                                    st.error(f"Error sending emails: {str(e)}")

                except Exception as e:
                    st.error(f"Error reading CSV file: {str(e)}")

        # Back button removed as requested

    # Activate Channel Mode
    elif st.session_state.configuration_mode == "activate":
        st.write(f"## 🚀 {st.session_state.selected_channel} Channel Activated")

        # Show active channel details
        sender_details = get_sender_details()
        active_channel = None

        for details in sender_details:
            if details.get("channel") == st.session_state.selected_channel and details.get("active"):
                active_channel = details
                break

        if active_channel:
            st.success(f"✅ Your {st.session_state.selected_channel} channel using {active_channel.get('esp')} is now active")

            # Display the active configuration details
            st.write("### Active Configuration Details")
            st.write(f"**Channel:** {active_channel.get('channel')}")
            st.write(f"**Service Provider:** {active_channel.get('esp')}")
            st.write(f"**Sender Name:** {active_channel.get('sender_name')}")
            st.write(f"**From Email:** {active_channel.get('sender_email')}")
            st.write(f"**Reply-to Email:** {active_channel.get('reply_to_email')}")

            st.info("This configuration will be used for automated campaign sending")

        # Back button
        if st.button("Back to Configuration", key="back_from_activate"):
            st.session_state.configuration_mode = "setup"
            st.rerun()

def display_campaign_settings():
    """Display the campaign settings configuration interface"""
    st.write("### ⚙️ Campaign Settings Configuration")

    # Get existing settings
    current_settings = get_campaign_settings()

    # Initialize time settings from current_settings or with defaults
    if "time_settings_active" in current_settings:
        time_settings_active = current_settings["time_settings_active"]
    else:
        # For backward compatibility
        time_settings_active = current_settings.get("time_settings", {"mode": "best_time", "custom_times": []})

    if "time_settings_non_active" in current_settings:
        time_settings_non_active = current_settings["time_settings_non_active"]
    else:
        # For backward compatibility
        time_settings_non_active = current_settings.get("time_settings", {"mode": "custom", "custom_times": []})

    # Unified Time Configuration
    st.write("#### ⏰ Sending Time Configuration")

    # Initialize custom times if not already in session state
    if "custom_times" not in st.session_state:
        # Use non-active times as the base since they're always custom
        st.session_state.custom_times = time_settings_non_active.get("custom_times", [])
        # If no times are set yet, initialize with some standard times
        if not st.session_state.custom_times:
            st.session_state.custom_times = ["09:00", "18:00"]

    # Use Best Time option (applies only to active users)
    use_best_time_active = st.checkbox(
        "Use Best Time for active users",
        value=time_settings_active.get("mode", "best_time") == "best_time",
        key="use_best_time_active",
        help="The system will analyze activity patterns of active users and send campaigns at optimal times."
    )

    # If using best time for active users, active time settings will be different from custom
    if use_best_time_active:
        time_settings_active = {"mode": "best_time", "custom_times": []}
    else:
        # If not using best time, active users will use the same custom times
        time_settings_active = {"mode": "custom", "custom_times": st.session_state.custom_times}

    # Non-active users always use custom times
    time_settings_non_active = {"mode": "custom", "custom_times": st.session_state.custom_times}

    # Show custom times settings
    st.write("**Custom Times (UTC):**")
    st.caption("These times will be used for non-active users, and for active users if 'Use Best Time' is disabled.")

    # Display existing custom times
    for i, time_str in enumerate(st.session_state.custom_times):
        cols = st.columns([3, 1])
        with cols[0]:
            st.text(f"Time {i+1}: {time_str} UTC")
        with cols[1]:
            if st.button("Remove", key=f"remove_time_{i}"):
                st.session_state.custom_times.pop(i)
                st.rerun()

    # Add new time option
    cols = st.columns([3, 1])
    with cols[0]:
        new_time = st.time_input("Add custom time (UTC)", datetime.strptime("09:00", "%H:%M"), key="new_time")
    with cols[1]:
        if st.button("Add", key="add_time"):
            time_str = new_time.strftime("%H:%M")
            if time_str not in st.session_state.custom_times:
                st.session_state.custom_times.append(time_str)
                st.rerun()

    if not st.session_state.custom_times:
        st.warning("⚠️ Please add at least one custom time")

    st.divider()

    # Frequency for Active Audience
    st.write("#### 👤 Frequency for Active Audience")
    active_frequency = st.radio(
        "Select email frequency for active users:",
        options=["Aggressive (3 emails/week)", "Moderate (2 emails/week)", "Defensive (1 email/week)"],
        index=0 if current_settings["active_audience_frequency"] == "aggressive" else
              1 if current_settings["active_audience_frequency"] == "moderate" else 2,
        key="active_frequency"
    )

    # Convert display text to value for storage
    active_frequency_value = active_frequency.lower().split(" ")[0]

    st.divider()

    # Frequency for Non-Active Audience
    st.write("#### 👥 Frequency for Non-Active Audience")
    non_active_frequency = st.radio(
        "Select email frequency for non-active users:",
        options=["Aggressive (3 emails/week)", "Moderate (2 emails/week)", "Defensive (1 email/week)"],
        index=0 if current_settings["non_active_audience_frequency"] == "aggressive" else
              1 if current_settings["non_active_audience_frequency"] == "moderate" else 2,
        key="non_active_frequency"
    )

    # Convert display text to value for storage
    non_active_frequency_value = non_active_frequency.lower().split(" ")[0]

    # Save settings button
    if st.button("Save Campaign Settings", type="primary"):
        # Validate settings
        validation_error = False
        error_message = ""

        # Custom times are required
        if not st.session_state.custom_times:
            validation_error = True
            error_message += "⚠️ Please add at least one custom time"

        if validation_error:
            st.error(error_message)
        else:
            # Save settings
            if save_campaign_settings(time_settings_active, time_settings_non_active, active_frequency_value, non_active_frequency_value):
                st.success("✅ Campaign settings saved successfully!")

                # Show summary of saved settings
                st.write("#### Summary of Saved Settings")

                # Custom times summary
                st.write("**Custom Times (UTC):**")
                if st.session_state.custom_times:
                    st.write(f"{', '.join([t + ' UTC' for t in st.session_state.custom_times])}")
                else:
                    st.write("None specified")

                # Active users time mode summary
                st.write("**Active Users Mode:**")
                if use_best_time_active:
                    st.write("Best Time (Optimized based on user activity)")
                else:
                    st.write("Custom Times (as specified above)")

                # Frequency settings summary
                st.write(f"**Active Audience Frequency:** {active_frequency}")
                st.write(f"**Non-Active Audience Frequency:** {non_active_frequency}")
            else:
                st.error("Failed to save campaign settings")

# Legacy functions kept for compatibility
def display_send_mail_tab():
    """Legacy function - Display the Send Mail tab content"""
    st.write("Please use the new Sending Channel Configuration flow.")

def display_schedule_mail_tab():
    """Legacy function - Display the Schedule Mail tab content"""
    st.write("Please use the new Sending Channel Configuration flow.")
