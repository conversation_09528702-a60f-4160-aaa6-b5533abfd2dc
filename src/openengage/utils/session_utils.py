"""
Utility functions for managing session state in OpenEngage.
"""
import os
import streamlit as st
import uuid
from crew_manager import OpenEngageCrew

def initialize_session_state():
    """Initialize session state variables"""
    if "messages" not in st.session_state:
        st.session_state.messages = []

    if "crew" not in st.session_state:
        # Initialize the crew
        st.session_state.crew = OpenEngageCrew()

        # Ensure the crew is ready to handle messages by initializing a default task
        from crewai import Task
        from datetime import datetime

        # Get current date for context
        current_date = datetime.now().strftime("%A, %B %d, %Y")

        # Create a default welcome task
        welcome_task = Task(
            description=f"Prepare a friendly welcome message for a new user. Today's date is {current_date}.",
            agent=st.session_state.crew.master_agent,
            expected_output="A friendly welcome message",
            context=[{
                "description": f"You are helping users with OpenEngage. Today's date is {current_date}. Provide a friendly welcome message without mentioning URLs unless specifically asked.",
                "expected_output": "A friendly welcome message"
            }]
        )

        # Initialize the crew with this task
        st.session_state.crew.crew.tasks = [welcome_task]

    if "show_editor" not in st.session_state:
        st.session_state.show_editor = False

    if "product_data" not in st.session_state:
        st.session_state.product_data = None

    if "selected_channels" not in st.session_state:
        st.session_state.selected_channels = {
            "Email": True,
            "WhatsApp": False,
        }

    if "node_selected" not in st.session_state:
        st.session_state.node_selected = None

    if "node_clicked" not in st.session_state:
        st.session_state.node_clicked = False

    if "show_channel_selection" not in st.session_state:
        st.session_state.show_channel_selection = False

    if "show_communication_settings" not in st.session_state:
        st.session_state.show_communication_settings = False

    if "show_user_journey" not in st.session_state:
        st.session_state.show_user_journey = False

    if "show_stage_selection" not in st.session_state:
        st.session_state.show_stage_selection = False

    if "show_template_verification" not in st.session_state:
        st.session_state.show_template_verification = False

    if "show_journey_builder" not in st.session_state:
        st.session_state.show_journey_builder = False

    if "show_templates_generator" not in st.session_state:
        st.session_state.show_templates_generator = False

    if "show_mass_generator" not in st.session_state:
        st.session_state.show_mass_generator = False

    if "current_email_content" not in st.session_state:
        st.session_state.current_email_content = {"subject": "", "body": ""}

    if "current_product_data" not in st.session_state:
        st.session_state.current_product_data = {}

    if "available_stages" not in st.session_state:
        st.session_state.available_stages = []

    if "communication_settings" not in st.session_state:
        from utils.file_utils import load_communication_settings
        # Try to load communication settings for the current organization if available
        if "organization_url" in st.session_state and st.session_state.organization_url:
            st.session_state.communication_settings = load_communication_settings(organization_url=st.session_state.organization_url)
        else:
            st.session_state.communication_settings = load_communication_settings()

    if "user_journey" not in st.session_state:
        from utils.file_utils import load_user_journey
        st.session_state.user_journey = load_user_journey()

    if "current_product_data" not in st.session_state:
        from utils.file_utils import load_product_details
        st.session_state.current_product_data = load_product_details()

    if "session_id" not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())

    if "selected_node" not in st.session_state:
        st.session_state.selected_node = None

    if "journey_generated" not in st.session_state:
        st.session_state.journey_generated = False

    if "highlighted_node" not in st.session_state:
        st.session_state.highlighted_node = 1

    if "traversed_node" not in st.session_state:
        st.session_state.traversed_node = 1

    if "current_stage_idx" not in st.session_state:
        st.session_state.current_stage_idx = 0

    if "tree_start_idx" not in st.session_state:
        st.session_state.tree_start_idx = 0

    if "show_analytics" not in st.session_state:
        st.session_state.show_analytics = False

    if "show_trigger_points" not in st.session_state:
        st.session_state.show_trigger_points = False

    if "analytics_data" not in st.session_state:
        st.session_state.analytics_data = None

    if "analytics_time_unit" not in st.session_state:
        st.session_state.analytics_time_unit = 'D'

    if "show_collaterals_section" not in st.session_state:
        st.session_state.show_collaterals_section = False

    if "organization_data" not in st.session_state:
        st.session_state.organization_data = {}

    if "show_org_editor" not in st.session_state:
        # Start with organization setup by default
        st.session_state.show_org_editor = True

    if 'journey_company' not in st.session_state:
        st.session_state.journey_company = None

    if 'journey_user_name' not in st.session_state:
        st.session_state.journey_user_name = ""

    if 'journey_user_stage' not in st.session_state:
        st.session_state.journey_user_stage = None

    if 'journey_user_behavior' not in st.session_state:
        st.session_state.journey_user_behavior = ""

    if 'journey_generated' not in st.session_state:
        st.session_state.journey_generated = False

    if 'matched_product' not in st.session_state:
        st.session_state.matched_product = None

    if "show_login_signup" not in st.session_state:
        st.session_state.show_login_signup = True

    if "is_logged_in" not in st.session_state:
        st.session_state.is_logged_in = False

    if "current_user" not in st.session_state:
        st.session_state.current_user = None

    # Store organization name from current user for easy access
    if "organization_name" not in st.session_state:
        if "current_user" in st.session_state and st.session_state.current_user:
            user_org = st.session_state.current_user.get('organization', {})
            st.session_state.organization_name = user_org.get('name', 'OpenEngage Team')
        else:
            st.session_state.organization_name = 'OpenEngage Team'

    if "show_dashboard" not in st.session_state:
        st.session_state.show_dashboard = False

    # Competitive analysis session state variables
    if "show_competitive_analysis" not in st.session_state:
        st.session_state.show_competitive_analysis = False

    if "show_sending_config" not in st.session_state:
        st.session_state.show_sending_config = False

    if "competitor_urls" not in st.session_state:
        st.session_state.competitor_urls = []

    if "competitor_data" not in st.session_state:
        st.session_state.competitor_data = {}

    if "analysis_complete" not in st.session_state:
        st.session_state.analysis_complete = False

    if "existing_products" not in st.session_state:
        st.session_state.existing_products = []

def check_api_key():
    """Check if OpenAI API key is configured"""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        api_key = st.secrets.get("OPENAI_API_KEY")

    if not api_key:
        st.error("OpenAI API key not found. Please set it in your .env file or Streamlit secrets.")
        st.stop()
    else:
        os.environ["OPENAI_API_KEY"] = api_key
