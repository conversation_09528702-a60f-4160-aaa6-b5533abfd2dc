"""
Utility to update existing brand guidelines with archetype scores.

This script analyzes all organizations in the brand_guidelines.json file
and adds archetype scores for any organization that doesn't have them.
"""

import json
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent.parent.parent.parent
sys.path.append(str(project_root))

from src.openengage.agents.brand_analyzer import WebsiteBrandAnalyzerTool
from src.openengage.utils.file_utils import save_brand_guidelines

def load_brand_guidelines():
    """Load brand guidelines from the JSON file."""
    try:
        file_path = os.path.join(project_root, "data", "brand_guidelines.json")
        if os.path.exists(file_path):
            with open(file_path, "r") as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Error loading brand guidelines: {e}")
        return {}

def analyze_archetypes_for_url(url):
    """Analyze brand archetypes for a given URL."""
    try:
        print(f"Analyzing archetypes for {url}...")
        brand_analyzer = WebsiteBrandAnalyzerTool()
        brand_guidelines = brand_analyzer._run(url)
        archetype_scores = brand_guidelines.get("archetype_scores", {})

        if archetype_scores:
            print(f"Successfully analyzed archetypes for {url}: found {len(archetype_scores)} archetypes")
            return archetype_scores
        else:
            print(f"No archetype scores found in response for {url}")

            # Generate mock archetype scores based on brand personality
            print(f"Generating mock archetype scores for {url}")
            brand_personality = brand_guidelines.get("brand_personality", "")
            brand_reasoning = brand_guidelines.get("brand_personality_reasoning", "")

            # Generate mock archetype scores
            mock_archetype_scores = {}
            archetypes = [
                "Creator", "Sage", "Caregiver", "Innocent", "Jester", "Magician",
                "Ruler", "Hero", "Everyman", "Rebel", "Explorer", "Lover"
            ]

            # Set the primary archetype to a high score
            primary_score = 9

            # Find the primary archetype from brand_personality
            primary_archetype = None
            for archetype in archetypes:
                if archetype.lower() in brand_personality.lower():
                    primary_archetype = archetype
                    break

            # If no match found, use the first word of brand_personality
            if not primary_archetype and brand_personality:
                words = brand_personality.split()
                if words:
                    for archetype in archetypes:
                        if archetype.lower() in words[0].lower():
                            primary_archetype = archetype
                            break

            # If still no match, default to the first archetype
            if not primary_archetype:
                primary_archetype = archetypes[0]

            print(f"Primary archetype identified as {primary_archetype} for {url}")

            # Generate scores for all archetypes
            import random
            for archetype in archetypes:
                if archetype == primary_archetype:
                    score = primary_score
                    reasoning = brand_reasoning or f"This is the primary brand archetype that best represents the organization's values and communication style."
                else:
                    # Generate a random score between 1 and 8
                    score = random.randint(1, 8)
                    reasoning = f"This archetype has some alignment with the brand's values but is not the primary archetype."

                mock_archetype_scores[archetype] = {
                    "score": score,
                    "reasoning": reasoning
                }

            print(f"Generated {len(mock_archetype_scores)} mock archetype scores for {url}")
            return mock_archetype_scores
    except Exception as e:
        import traceback
        print(f"Error analyzing archetypes for {url}: {e}")
        print(traceback.format_exc())
        return {}

def update_all_organizations_with_archetypes():
    """Update all organizations in brand_guidelines.json with archetype scores."""
    # Load existing brand guidelines
    brand_guidelines_data = load_brand_guidelines()

    # Count organizations
    total_orgs = len(brand_guidelines_data)
    print(f"Found {total_orgs} organizations in brand_guidelines.json")

    # Track organizations that need updating
    orgs_to_update = []
    for url in brand_guidelines_data:
        if "archetype_scores" not in brand_guidelines_data[url] or not brand_guidelines_data[url]["archetype_scores"]:
            orgs_to_update.append(url)

    print(f"Found {len(orgs_to_update)} organizations without archetype scores")

    # Update each organization
    for i, url in enumerate(orgs_to_update):
        print(f"Processing organization {i+1}/{len(orgs_to_update)}: {url}")

        # Get existing brand guidelines
        org_guidelines = brand_guidelines_data[url]

        # Analyze archetypes
        archetype_scores = analyze_archetypes_for_url(url)

        if archetype_scores:
            # Update brand guidelines with archetype scores
            org_guidelines["archetype_scores"] = archetype_scores

            # Save updated brand guidelines
            save_brand_guidelines(org_guidelines, url)

            print(f"✅ Successfully updated {url} with archetype scores")
        else:
            print(f"❌ Failed to get archetype scores for {url}")

    print(f"Completed updating {len(orgs_to_update)} organizations with archetype scores")

if __name__ == "__main__":
    update_all_organizations_with_archetypes()
