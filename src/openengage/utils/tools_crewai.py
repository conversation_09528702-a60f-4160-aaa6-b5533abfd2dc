import os
import json
import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool, ScrapeWebsiteTool
from openai import OpenAI

# Load environment variables
load_dotenv()

# Set up logging
log_file = 'data/competitor_crew.log'
os.makedirs('data', exist_ok=True)

# Configure logging to write to both file and console
logger = logging.getLogger('CompetitiveAnalysisCrew')
logger.setLevel(logging.DEBUG)  # Set to DEBUG for more verbose output

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)

# Create console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# Add both handlers to logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

class CompetitiveAnalysisCrew:
    """Crew for competitive analysis"""

    def __init__(self):
        # Initialize tools
        logger.info("Initializing CompetitiveAnalysisCrew")
        self.search_tool = SerperDevTool(api_key=os.getenv("SERPER_API_KEY"), n_results=3)
        self.scrape_tool = ScrapeWebsiteTool()

        # Initialize OpenAI client
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Create agents
        self.competitor_finder = self._create_competitor_finder()
        self.company_analyzer = self._create_company_analyzer()
        logger.info("CompetitiveAnalysisCrew initialized successfully")

    def _log_agent_action(self, data):
        """Log agent actions with detailed information"""
        logger.debug(f"Agent action: {data}")
        try:
            agent_name = data.get('agent_name', 'Unknown Agent')
            action = data.get('action', {})
            action_type = action.get('type', 'Unknown Action')
            action_input = action.get('input', 'No input')

            print(f"\n===== AGENT ACTION: {agent_name} =====\n")
            print(f"Action type: {action_type}")
            print(f"Input: {action_input[:500]}..." if len(str(action_input)) > 500 else f"Input: {action_input}")
            print("\n")
        except Exception as e:
            logger.error(f"Error logging agent action: {e}")

    def _log_tool_start(self, data):
        """Log tool start with detailed information"""
        logger.debug(f"Tool started: {data}")
        try:
            tool_name = data.get('tool_name', 'Unknown Tool')
            tool_input = data.get('input', 'No input')

            print(f"\n----- TOOL STARTED: {tool_name} -----\n")
            print(f"Input: {tool_input[:300]}..." if len(str(tool_input)) > 300 else f"Input: {tool_input}")
        except Exception as e:
            logger.error(f"Error logging tool start: {e}")

    def _log_tool_end(self, data):
        """Log tool end with detailed information"""
        logger.debug(f"Tool ended: {data}")
        try:
            tool_name = data.get('tool_name', 'Unknown Tool')
            output = data.get('output', 'No output')

            print(f"\n----- TOOL ENDED: {tool_name} -----\n")
            print(f"Output: {output[:300]}..." if len(str(output)) > 300 else f"Output: {output}")
            print("\n")
        except Exception as e:
            logger.error(f"Error logging tool end: {e}")

    def _log_text(self, data):
        """Log text with detailed information"""
        logger.debug(f"Text: {data}")
        try:
            if isinstance(data, str) and data.strip():
                print(f"\n{data}\n")
        except Exception as e:
            logger.error(f"Error logging text: {e}")

    def _create_competitor_finder(self) -> Agent:
        """Create an agent that finds competitors"""
        return Agent(
            role="Competitive Intelligence Researcher",
            goal="Find direct competitors for the given company based on product type",
            backstory="""You are an expert in competitive intelligence with years of
            experience in market research. You have a knack for identifying relevant
            competitors in any industry. You use search tools to find the most
            relevant competitors for any company.

            You focus on finding DIRECT competitors that offer similar products or services
            to the target company. You pay special attention to the product type and ensure
            that the competitors you identify are offering products in the same category.

            For example, if analyzing an 'Online Learning Program', you would find other
            online learning programs that compete directly, not physical schools or unrelated
            educational products.

            You always provide specific product page URLs whenever possible, not just company
            homepages, as this provides more valuable competitive intelligence.""",
            tools=[self.search_tool],
            verbose=True,
            llm_model="gpt-4o-mini-2024-07-18"
        )

    def _create_company_analyzer(self) -> Agent:
        """Create an agent that analyzes companies and extracts product information"""
        return Agent(
            role="Company and Product Analyst",
            goal="Extract detailed information about companies and their products",
            backstory="""You are a skilled business analyst specializing in product
            analysis and competitive intelligence. You have a deep understanding of
            various industries and can quickly identify key product features, pricing
            strategies, and unique selling points. You're meticulous about details and
            always provide comprehensive analysis.""",
            tools=[self.scrape_tool, self.search_tool],
            verbose=True,
            llm_model="gpt-4o-mini-2024-07-18"
        )

    def find_competitors(self, company_urls: List[str], product_type: str = None) -> List[str]:
        """Find competitors for the given company URLs"""
        logger.info(f"Finding competitors for: {company_urls}")
        logger.info(f"Product type: {product_type}")
        print(f"\n===== FINDING COMPETITORS =====\n")
        print(f"Target URLs: {company_urls}")
        print(f"Product type: {product_type if product_type else 'Not specified'}")

        all_competitors = []

        # Process each URL individually
        for url in company_urls:
            print(f"\n----- Processing URL: {url} -----\n")

            # Create a task for finding competitors for this specific URL
            find_competitors_task = Task(
                description=f"""
                Research and identify the top competitors for the following company:
                {url}

                Product type: {product_type if product_type else 'Not specified'}

                IMPORTANT: Focus on finding competitors that offer products of the same type: "{product_type if product_type else 'Not specified'}".
                You must consider the product type as a critical factor in determining relevant competitors.

                When searching for competitors, always include the product type in your search queries.
                For example, if the product type is "Online Learning Program", search for "Online Learning Program competitors" or similar queries.

                For this company:
                1. Visit their website
                2. Understand their business model and products
                3. Search for similar companies in the same industry
                4. Identify at least 3-5 direct competitors that offer similar products or services
                   Direct competitors are those that target the same customer needs and offer alternative solutions
                5. For each competitor, find their main product page URLs (not just the homepage)

                Return a JSON array of competitor website URLs, including specific product page URLs when possible.

                Explain your research process in detail as you work, including:
                - How you identified the company's main products/services
                - What search terms you used to find competitors
                - Why you consider each competitor to be a direct competitor
                - What specific products from each competitor are most similar

                Your detailed explanation will be logged for analysis.
                """,
                agent=self.competitor_finder,
                expected_output="A JSON array of competitor website URLs with explanation of research process"
            )
            print("\nTask description sent to competitor finder agent:")
            print(f"{find_competitors_task.description}\n")

            # Log the task description
            logger.debug(f"Task description for URL {url}: {find_competitors_task.description}")

            # Create a crew with just the competitor finder
            finder_crew = Crew(
                agents=[self.competitor_finder],
                tasks=[find_competitors_task],
                process=Process.sequential,
                verbose=True,
                callbacks={
                    "on_chain_start": lambda x: logger.debug(f"Chain started: {x}"),
                    "on_chain_end": lambda x: logger.debug(f"Chain ended: {x}"),
                    "on_agent_action": lambda x: self._log_agent_action(x),
                    "on_tool_start": lambda x: self._log_tool_start(x),
                    "on_tool_end": lambda x: self._log_tool_end(x),
                    "on_text": lambda x: self._log_text(x)
                }
            )

            # Log the start of the crew execution
            logger.info(f"Starting competitor finder crew for URL: {url}")

            # Run the crew
            logger.debug(f"Kicking off competitor finder crew for URL: {url}...")
            print(f"\n===== STARTING COMPETITOR FINDER CREW FOR {url} =====\n")
            print("The agent will now search for competitors. This may take a few minutes...\n")
            result = finder_crew.kickoff()

            # Log the raw result
            logger.info(f"Competitor finder crew completed for URL: {url}")
            logger.debug(f"Raw result for URL {url}: {result}")

            # print(f"\n===== COMPETITOR FINDER CREW COMPLETED FOR {url} =====\n")
            # print("Raw result (truncated):")
            # print(f"{result[:1000]}..." if len(result) > 1000 else result)
            # print("\n")

            # Extract URLs from the result
            try:
                # Convert CrewOutput to string if needed
                result_str = str(result)

                # Try to parse as JSON
                logger.debug("Attempting to extract JSON array of URLs from result")
                urls_match = re.search(r'\[.*\]', result_str, re.DOTALL)
                if urls_match:
                    urls_json = urls_match.group(0)
                    logger.debug(f"Found JSON array: {urls_json}")
                    new_urls = json.loads(urls_json)
                    logger.info(f"Successfully extracted {len(new_urls)} competitor URLs as JSON for {url}")
                    logger.debug(f"Extracted URLs for {url}: {new_urls}")
                    all_competitors.extend(new_urls)
                else:
                    logger.debug("No JSON array found, falling back to regex extraction")
                    # If no JSON array found, try to extract URLs with regex
                    urls = re.findall(r'https?://(?:www\.)?([a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+)(?:/[^\s]*)?', result_str)
                    extracted_urls = [f"https://{u}" for u in urls]
                    logger.info(f"Extracted {len(extracted_urls)} competitor URLs using regex for {url}")
                    logger.debug(f"Extracted URLs for {url}: {extracted_urls}")
                    all_competitors.extend(extracted_urls)
            except Exception as e:
                error_msg = f"Error extracting competitor URLs for {url}: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"Exception details: {e}", exc_info=True)
                print(error_msg)
                # Fallback: extract any URLs from the text
                logger.debug("Using fallback regex extraction method")
                result_str = str(result)
                urls = re.findall(r'https?://(?:www\.)?([a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+)(?:/[^\s]*)?', result_str)
                extracted_urls = [f"https://{u}" for u in urls]
                logger.info(f"Fallback: Extracted {len(extracted_urls)} competitor URLs using regex for {url}")
                logger.debug(f"Fallback extracted URLs for {url}: {extracted_urls}")
                all_competitors.extend(extracted_urls)

        # Remove duplicates while preserving order
        unique_competitors = []
        for url in all_competitors:
            if url not in unique_competitors:
                unique_competitors.append(url)

        logger.info(f"Total unique competitor URLs found: {len(unique_competitors)}")
        print(f"\n===== FINAL RESULTS =====\n")
        print(f"Found {len(unique_competitors)} unique competitor URLs across all products")
        for i, url in enumerate(unique_competitors):
            print(f"  {i+1}. {url}")
        print("\n")

        return unique_competitors

    def analyze_companies(self, company_urls: List[str]) -> Dict[str, Any]:
        """Analyze companies and extract product information"""
        logger.info(f"Analyzing companies: {company_urls}")

        # Create a task for analyzing companies
        urls_str = ', '.join(company_urls)
        json_structure = '''
{
    "company_url": {
        "company_name": "...",
        "company_description": "...",
        "products": [
            {
                "product_name": "...",
                "product_description": "...",
                "features": ["...", "..."],
                "target_market": "...",
                "pricing": "...",
                "price_value": "...",
                "specifications": {"spec1": "value1", "spec2": "value2"},
                "unique_selling_points": ["...", "..."],
                "availability": "...",
                "ratings": "...",
                "release_date": "...",
                "product_url": "..."
            }
        ]
    }
}
'''
        analyze_companies_task = Task(
            description=f"""Analyze the following companies and extract detailed information about their products:
{urls_str}

For each company:
1. Visit their website
2. Extract the company name and description
3. Identify their main products
4. For each product, extract:
   - Product name
   - Product description
   - Key features
   - Target market
   - Pricing information (if available)
   - Product specifications
   - Unique selling points
   - Availability
   - Customer ratings/reviews (if mentioned)
   - Release date/product age
   - Product URL (the direct link to the product page, this is VERY important to capture accurately)

Return the information in a JSON format with the following structure:
```json
{json_structure}
```

If any field is not found, use reasonable defaults or indicate 'Not available'.""",
            agent=self.company_analyzer,
            expected_output="A JSON object with detailed company and product information"
        )

        # Log the task description
        logger.debug(f"Company analysis task description: {analyze_companies_task.description}")

        # Create a crew with just the company analyzer
        analyzer_crew = Crew(
            agents=[self.company_analyzer],
            tasks=[analyze_companies_task],
            process=Process.sequential,
            verbose=True,
            callbacks={
                "on_chain_start": lambda x: logger.debug(f"Chain started: {x}"),
                "on_chain_end": lambda x: logger.debug(f"Chain ended: {x}"),
                "on_agent_action": lambda x: self._log_agent_action(x),
                "on_tool_start": lambda x: self._log_tool_start(x),
                "on_tool_end": lambda x: self._log_tool_end(x),
                "on_text": lambda x: self._log_text(x)
            }
        )

        # Log the start of the crew execution
        logger.info(f"Starting company analyzer crew for URLs: {company_urls}")

        # Run the crew
        logger.debug("Kicking off company analyzer crew...")
        print("\n===== STARTING COMPANY ANALYZER CREW =====\n")
        print("The agent will now analyze the companies and extract product information. This may take a few minutes...\n")
        result = analyzer_crew.kickoff()

        # Convert CrewOutput to string if needed
        result_str = str(result)

        # Log the raw result (truncated to avoid extremely large logs)
        result_preview = result_str[:500] + "..." if len(result_str) > 500 else result_str
        logger.info(f"Company analyzer crew completed")
        logger.debug(f"Company analyzer crew result preview: {result_preview}")

        print("\n===== COMPANY ANALYZER CREW COMPLETED =====\n")
        print("Raw result (truncated):")
        print(f"{result_str[:1000]}..." if len(result_str) > 1000 else result_str)
        print("\n")

        # Extract JSON from the result
        try:
            # Try to find a JSON object in the text
            logger.debug("Attempting to extract JSON object from result")
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                logger.debug(f"Found JSON object with length {len(json_str)} characters")
                parsed_result = json.loads(json_str)
                logger.info(f"Successfully extracted JSON data for {len(parsed_result)} companies")
                logger.debug(f"Extracted company keys: {list(parsed_result.keys())}")
                return parsed_result
            else:
                error_msg = "Could not extract JSON from result"
                logger.error(error_msg)
                logger.debug(f"Failed to find JSON object in result: {result_str[:200]}...")
                return {"error": error_msg}
        except Exception as e:
            error_msg = f"Error extracting company analysis: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details: {e}", exc_info=True)
            print(error_msg)
            return {"error": error_msg}

    async def analyze_competitors_async(self, company_urls: List[str]) -> Dict[str, Any]:
        """Analyze competitors asynchronously (wrapper for synchronous method)"""
        # This is a wrapper to maintain compatibility with the existing code
        # In a real async implementation, we would use asyncio properly
        return self.analyze_companies(company_urls)
