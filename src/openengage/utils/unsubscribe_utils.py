"""
Utility functions for handling email unsubscribe functionality in OpenEngage.
"""
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

def encrypt_email(email):
    """
    Encrypt an email address using AES encryption for unsubscribe link generation.
    
    Args:
        email: The email address to encrypt
        
    Returns:
        Encrypted email string safe for URL usage
    """
    key = "CQv9UQ/cqt8XNhCiq6JmZQ=="
    key = base64.b64decode(key)
    cipher = AES.new(key, AES.MODE_CBC)  # Create a new AES cipher
    iv = cipher.iv  # Initialization vector
    ciphertext = cipher.encrypt(pad(email.encode(), AES.block_size))  # Encrypt with padding

    # Combine IV and ciphertext and encode as Base64
    encrypted_email = base64.urlsafe_b64encode(iv + ciphertext).decode('utf-8')
    return encrypted_email

def create_unsubscribe_link(email):
    """
    Create a secure unsubscribe link for the given email address.
    
    Args:
        email: The email address to create an unsubscribe link for
        
    Returns:
        Complete unsubscribe URL
    """
    e = encrypt_email(email)
    url = 'https://www.analyticsvidhya.com/one-tap-unsubscribe/?email_hash=' + e
    url = url + "&utm_source=email_openengage"
    return url
