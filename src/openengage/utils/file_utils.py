"""
Utility functions for file operations in OpenEngage.
"""
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import re
import streamlit as st

def load_product_details(product_name=None, organization_url=None, filter_by_org=False):
    """Load product details for a specific product or return the most recent

    Args:
        product_name: Name of the product to load
        organization_url: URL of the organization to filter by
        filter_by_org: Whether to filter products by organization URL
    """
    try:
        with open('data/product_details.json', 'r') as f:
            products_list = json.load(f)
            if not isinstance(products_list, list):
                products_list = [products_list]  # Convert old format to list

            # Filter by organization URL if requested
            if filter_by_org and organization_url:
                # Check both Company_URL and organization URL in product data
                products_list = [p for p in products_list if p.get("Company_URL", "") == organization_url or
                                                            p.get("organization_url", "") == organization_url]

            if product_name:
                # Find details for specific product
                for product in products_list:
                    if product.get("Product_Name") == product_name:
                        return product

            # Return most recent product if no specific product or not found
            return products_list[-1] if products_list else {}
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_product_details(product_data, replace_all=False):
    """Save product details while maintaining history

    Args:
        product_data: A single product dict or a list of product dicts
        replace_all: If True, replace all products with the provided data
    """
    if not product_data:
        return

    # Handle both single product and list of products
    if isinstance(product_data, list):
        products_to_save = product_data
    else:
        products_to_save = [product_data]

    # If we're not replacing all products, load existing ones
    if not replace_all:
        # Load existing products
        existing_products = []
        try:
            with open('data/product_details.json', 'r') as f:
                existing_products = json.load(f)
                if not isinstance(existing_products, list):
                    existing_products = [existing_products]
        except (FileNotFoundError, json.JSONDecodeError):
            existing_products = []

        # Process each product to save
        final_products = existing_products.copy()
        for new_product in products_to_save:
            # Check if product already exists
            found = False
            product_name = new_product.get("Product_Name")

            for i, existing_product in enumerate(final_products):
                if existing_product.get("Product_Name") == product_name:
                    # Update existing product
                    final_products[i] = new_product
                    found = True
                    break

            # If not found, append new product
            if not found:
                final_products.append(new_product)
    else:
        # If replacing all, just use the provided products
        final_products = products_to_save

    # Save updated list
    with open('data/product_details.json', 'w') as f:
        json.dump(final_products, f, indent=4)

def load_user_journey(product_name=None):
    """Load user journey for a specific product or return default journey"""
    try:
        with open('data/user_journey.json', 'r') as f:
            journeys = json.load(f)
            if not isinstance(journeys, dict):
                # Convert old format to dict with default journey
                journeys = {"default": journeys if isinstance(journeys, list) else []}

            if product_name:
                # Return journey for specific product or default
                return journeys.get(product_name, journeys.get("default", get_default_journey()))

            # Return default journey
            return journeys.get("default", get_default_journey())
    except (FileNotFoundError, json.JSONDecodeError):
        return get_default_journey()

def save_user_journey(journey, product_name):
    """Save user journey for a specific product"""
    if not journey or not product_name:
        return

    # Load existing journeys
    journeys = {}
    try:
        with open('data/user_journey.json', 'r') as f:
            journeys = json.load(f)
            if not isinstance(journeys, dict):
                # Convert old format to dict with default journey
                journeys = {"default": journeys if isinstance(journeys, list) else []}
    except (FileNotFoundError, json.JSONDecodeError):
        journeys = {"default": get_default_journey()}

    # Update journey for this product
    journeys[product_name] = journey

    # Save updated journeys
    with open('data/user_journey.json', 'w') as f:
        json.dump(journeys, f, indent=4)

def get_default_journey():
    """Return the default user journey stages based on organization domain class"""
    # Default fallback class
    org_class = "EdTech"

    try:
        # Load organization data
        org_data = load_organization_data()
        if org_data:
            # First try to get the Class attribute (new approach)
            org_class = org_data.get("Class")

            # If Class is not found, try to get it from the org_url data
            if not org_class and hasattr(st.session_state, 'organization_url'):
                org_url = st.session_state.organization_url
                if org_url and org_url in org_data:
                    org_class = org_data[org_url].get("Class")

            # If still not found, fall back to domain-based matching
            if not org_class:
                org_domain = org_data.get("Domain", "")
                # Only proceed with domain matching if we have a domain
                if org_domain:
                    # Common domains and their potential alternative names
                    domain_keywords = {
                        "EdTech": ["education", "learning", "academic", "e-learning", "edtech"],
                        "E-commerce": ["retail", "shop", "store", "ecommerce", "e-commerce", "marketplace", "skincare", "wellness", "beauty"],
                        "Insurance (InsurTech)": ["insurance", "insurtech", "policy", "underwriting"],
                        "Banking": ["bank", "finance", "fintech", "financial", "wealth"],
                        "Quick-Commerce / Food": ["food", "delivery", "restaurant", "grocery", "quick-commerce"],
                        "TravelTech": ["travel", "tourism", "hotel", "flight", "booking", "vacation"]
                    }

                    # Convert org_domain to lowercase for better matching
                    org_domain_lower = org_domain.lower()

                    # Check if any keywords match the organization domain
                    for domain, keywords in domain_keywords.items():
                        if any(keyword in org_domain_lower for keyword in keywords):
                            org_class = domain
                            break
    except Exception as e:
        print(f"Error loading organization data: {str(e)}")

    # Load domain-specific stages from default_domain_stages.json
    try:
        with open('data/default_domain_stages.json', 'r') as f:
            domain_stages = json.load(f)

            # Use the class attribute to find the matching stages
            if org_class and org_class in domain_stages:
                return domain_stages[org_class]

            # Default to EdTech if available in domain_stages
            if "EdTech" in domain_stages:
                return domain_stages["EdTech"]
    except Exception as e:
        print(f"Error loading domain stages: {str(e)}")


    # Fallback to hardcoded default stages if everything else fails
    return [
        {
            "s_no": 1,
            "current_stage": "New Visitor",
            "description": "User is not aware of the product",
            "goal_stage": "Product Page Viewed"
        },
        {
            "s_no": 2,
            "current_stage": "Product Page Viewed",
            "description": "User has viewed product details",
            "goal_stage": "Product Lead Generated"
        },
        {
            "s_no": 3,
            "current_stage": "Product Lead Generated",
            "description": "User has shown interest in the product",
            "goal_stage": "Product Purchased"
        },
        {
            "s_no": 4,
            "current_stage": "Product Purchased",
            "description": "User has completed the purchase",
            "goal_stage": "Thank You"
        }
    ]

def load_communication_settings(sender_name=None, organization_url=None):
    """Load communication settings for a specific sender or organization

    Args:
        sender_name: Name of the sender to load settings for
        organization_url: URL of the organization to load settings for
    """
    try:
        # Try to load from file first
        with open('data/communication_settings.json', 'r') as f:
            settings_list = json.load(f)
            if not isinstance(settings_list, list):
                settings_list = [settings_list]  # Convert old format to list

            # First try to find by organization_url if provided
            if organization_url:
                for settings in settings_list:
                    if settings.get("organization_url") == organization_url:
                        return settings

            # Then try to find by sender_name if provided
            if sender_name:
                for settings in settings_list:
                    if settings.get("sender_name") == sender_name:
                        return settings
    except (FileNotFoundError, json.JSONDecodeError):
        # File doesn't exist or is invalid, continue to default settings
        pass

    # If we get here, we need to create default settings
    # Get organization name from session state if available
    import streamlit as st
    org_name = "OpenEngage Team"

    try:
        # First check if organization_name is in session state (set during login)
        if hasattr(st, 'session_state') and 'organization_name' in st.session_state:
            org_name = st.session_state.organization_name
        # If not, try to get from current_user
        elif hasattr(st, 'session_state') and 'current_user' in st.session_state and st.session_state.current_user:
            user_org = st.session_state.current_user.get('organization', {})
            if user_org:
                org_name = user_org.get('name', org_name)
    except Exception:
        # If anything goes wrong, just use the default
        pass
    
    # Return default settings with the organization name
    return {
        "sender_name": org_name,
        "style": "friendly",
        "length": "100-150 words",
        "utm_source": "email",
        "utm_medium": "Email",  # Using capitalized 'Email' to be consistent with UI
        "utm_campaign": "product_launch",
        "utm_content": "initial",
        "organization_url": organization_url if organization_url else ""
    }

def save_communication_settings(settings, sender_name, organization_url=None):
    """Save communication settings for a specific sender and organization

    Args:
        settings: The communication settings to save
        sender_name: Name of the sender
        organization_url: URL of the organization (optional)
    """
    # Load existing settings file
    settings_list = []
    try:
        with open('data/communication_settings.json', 'r') as f:
            settings_list = json.load(f)
            if not isinstance(settings_list, list):
                settings_list = [settings_list]
    except (FileNotFoundError, json.JSONDecodeError):
        settings_list = []

    # Get current settings
    current_settings = dict(settings)

    # Add organization_url to settings if provided
    if organization_url:
        current_settings["organization_url"] = organization_url

        # Ensure sender_name matches the organization for known organizations
        if "nike" in organization_url.lower() and current_settings.get("sender_name") != "Nike":
            current_settings["sender_name"] = "Nike"
        elif "instagram" in organization_url.lower() and current_settings.get("sender_name") != "Instagram":
            current_settings["sender_name"] = "Instagram"
        elif "snapmint" in organization_url.lower() and current_settings.get("sender_name") != "Snapmint":
            current_settings["sender_name"] = "Snapmint"
        elif "flipkart" in organization_url.lower() and current_settings.get("sender_name") != "Flipkart":
            current_settings["sender_name"] = "Flipkart"
        elif "analyticsvidhya" in organization_url.lower() and current_settings.get("sender_name") != "Analytics Vidhya":
            current_settings["sender_name"] = "Analytics Vidhya"

    # Ensure sender_name is set if not already set
    if not current_settings.get("sender_name"):
        # Try to use the provided sender_name first
        if sender_name:
            current_settings["sender_name"] = sender_name
        else:
            # Try to get from session state if available
            try:
                import streamlit as st
                if hasattr(st, 'session_state') and 'organization_name' in st.session_state:
                    current_settings["sender_name"] = st.session_state.organization_name
                else:
                    current_settings["sender_name"] = "OpenEngage Team"
            except Exception:
                # If anything goes wrong, use a default
                current_settings["sender_name"] = "OpenEngage Team"

    # Check if settings for this organization already exist
    found = False

    # First try to match by organization_url if it exists in the settings
    if "organization_url" in current_settings and current_settings["organization_url"]:
        for i, s in enumerate(settings_list):
            if s.get("organization_url") == current_settings["organization_url"]:
                # Update existing settings
                settings_list[i] = current_settings
                found = True
                break

    # If not found by organization_url, try to match by sender_name
    if not found:
        for i, s in enumerate(settings_list):
            if s.get("sender_name") == sender_name:
                # Update existing settings
                settings_list[i] = current_settings
                found = True
                break

    # If not found, append new settings
    if not found:
        settings_list.append(current_settings)

    # Save updated settings list to file
    with open('data/communication_settings.json', 'w') as f:
        json.dump(settings_list, f, indent=4)

def load_node_email(node_id):
    """Load email content for a specific node"""
    email_path = f'data/node_emails/{node_id}.json'

    if os.path.exists(email_path):
        with open(email_path, 'r') as f:
            content = json.load(f)
            return content.get("content", None)
    return None

def save_node_email(node_id, email_content):
    """Save email content for a specific node"""
    # Create emails directory if it doesn't exist
    os.makedirs('data/node_emails', exist_ok=True)

    # Save with sequential node number
    email_path = f'data/node_emails/{node_id}.json'

    # Save email content along with node mapping
    content_to_save = {
        "content": email_content,
        "node_id": node_id,
        "generated_at": datetime.now().isoformat()
    }

    with open(email_path, 'w') as f:
        json.dump(content_to_save, f, indent=2)

def ensure_directories():
    """Ensure all required directories exist"""
    directories = [
        "data/node_emails",
        "data/campaigns",
        "data/collaterals",
        "data/templates",
        "data/logs",
        "data/competitive_analysis",
        "data/trigger_points",
        "data/custom_keywords"
    ]

    # Ensure the data directory exists for the competitor crew log file
    os.makedirs("data", exist_ok=True)

    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def log_node_click(node_id, session_id="unknown"):
    """Log node clicks to a file"""
    log_dir = "data/logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, "click_logs.json")

    # Load existing logs
    logs = []
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            logs = json.load(f)

    # Add new log entry
    logs.append({
        "node_id": node_id,
        "timestamp": datetime.now().isoformat(),
        "session_id": session_id
    })

    # Save updated logs
    with open(log_file, 'w') as f:
        json.dump(logs, f, indent=2)

def get_all_products(organization_url=None, filter_by_org=False):
    """Get all products, optionally filtered by organization URL

    Args:
        organization_url: URL of the organization to filter by
        filter_by_org: Whether to filter products by organization URL

    Returns:
        List of product dictionaries
    """
    try:
        with open('data/product_details.json', 'r') as f:
            products_list = json.load(f)
            if not isinstance(products_list, list):
                products_list = [products_list]  # Convert old format to list

            # Filter by organization URL if requested
            if filter_by_org and organization_url:
                # Check both Company_URL and organization URL in product data
                products_list = [p for p in products_list if p.get("Company_URL", "") == organization_url or
                                                            p.get("organization_url", "") == organization_url]

            return products_list
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def load_feature_toggles():
    """Load feature toggles from JSON file"""
    # Use absolute path resolution to ensure consistency between load and save
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'feature_toggles.json')
    try:
        with open(config_path, 'r') as f:
            loaded_toggles = json.load(f)
            print(f"Loaded toggles from {config_path}: {loaded_toggles}")
            return loaded_toggles
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading toggles from {config_path}: {str(e)}")
        # Default toggles if file doesn't exist or is invalid
        return {
            'product_setup': True,
            'template_generator': True,
            'template_verification': True,
            'journey_builder': True,
            'trigger_points': True,
            'mass_campaign': True,
            'analytics_dashboard': True,
            'competitive_analysis': True,
            'trend_analysis': True,
            'org_products_visibility': True,
            'hide_behaviour_generation': False
        }

def save_feature_toggles(toggles):
    """Save feature toggles to JSON file"""
    # Use absolute path resolution to ensure consistency with load_feature_toggles
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'feature_toggles.json')
    
    # Make sure the directory exists
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w') as f:
        json.dump(toggles, f, indent=2)
    
    print(f"Saved toggles to {config_path}: {toggles}")
    return True

def load_organization_data(organization_url=None):
    """
    Load organization data for a specific organization or all organizations.

    Args:
        organization_url (str, optional): URL of the organization to load.
            If None, returns all organizations as a dictionary.

    Returns:
        dict: Organization data for a specific organization or a dictionary of all organizations
    """
    org_data_path = os.path.join('data', 'organization_data.json')

    try:
        if os.path.exists(org_data_path):
            with open(org_data_path, 'r') as f:
                try:
                    # Try to load as multi-organization format (dict of dicts)
                    all_orgs = json.load(f)

                    # If it's the old format (single org dict), convert it
                    if not isinstance(all_orgs, dict) or not all(isinstance(all_orgs.get(k), dict) for k in all_orgs):
                        # This is the old format, convert it
                        old_org_data = all_orgs
                        # Use URL as key if available, otherwise use a default key
                        org_url = old_org_data.get('url') or 'default_organization'
                        all_orgs = {org_url: old_org_data}

                    # Return specific organization if requested
                    if organization_url:
                        # Try exact match first
                        if organization_url in all_orgs:
                            return all_orgs[organization_url]

                        # Try case-insensitive match
                        for url in all_orgs:
                            if url.lower() == organization_url.lower():
                                return all_orgs[url]

                        # If organization not found, return empty dict
                        return {}

                    # Return all organizations
                    return all_orgs
                except:
                    # Return empty dict on error
                    return {}
        else:
            # Return empty dict if file doesn't exist
            return {}
    except:
        # Return empty dict on error
        return {}

def save_organization_data(org_data, organization_url=None):
    """
    Save organization data for a specific organization.

    Args:
        org_data (dict): Organization data to save
        organization_url (str, optional): URL of the organization.
            If None, uses the 'url' field from org_data.

    Returns:
        bool: True if save successful, False otherwise
    """
    if not org_data:
        return False

    if not organization_url:
        organization_url = org_data.get('url')
        if not organization_url:
            return False

    try:
        # Ensure org_data has a 'saved' flag
        if 'saved' not in org_data:
            org_data['saved'] = True

        # Load existing data
        all_orgs = {}
        if os.path.exists('data/organization_data.json'):
            try:
                with open('data/organization_data.json', 'r') as f:
                    file_data = json.load(f)
                    if isinstance(file_data, dict):
                        all_orgs = file_data
                    else:
                        # Convert old format to new
                        all_orgs = {}
            except json.JSONDecodeError:
                # If file exists but is invalid, create backup
                if os.path.getsize('data/organization_data.json') > 0:
                    backup_file = f'data/organization_data.json.bak.{datetime.now().strftime("%Y%m%d%H%M%S")}'
                    try:
                        import shutil
                        shutil.copy('data/organization_data.json', backup_file)
                        print(f"Created backup of potentially corrupted organization_data.json as {backup_file}")
                    except Exception as backup_err:
                        print(f"Could not create backup: {str(backup_err)}")
                all_orgs = {}

        # Save this organization's data
        all_orgs[organization_url] = org_data

        # Write back to file
        with open('data/organization_data.json', 'w') as f:
            json.dump(all_orgs, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving organization data: {str(e)}")
        return False

def get_all_organizations():
    """
    Get a list of all organizations.

    Returns:
        list: List of organization dictionaries
    """
    all_orgs = load_organization_data()
    return [org for _, org in all_orgs.items()]

def load_brand_guidelines(organization_url=None):
    """
    Load brand guidelines for a specific organization or all organizations.

    Args:
        organization_url (str, optional): URL of the organization to load.
            If None, returns all organizations as a dictionary.

    Returns:
        dict: Brand guidelines for a specific organization or a dictionary of all organizations
    """
    guidelines_path = os.path.join('data', 'brand_guidelines.json')

    # Default brand guidelines
    default_guidelines = {
        "primary_color": "#default",
        "secondary_color": "#default",
        "accent_color": "#default",
        "neutral_color": "#default",
        "background_color": "#default",
        "text_color": "#default",
        "cta_type": "Button",
        "cta_size": "Medium",
        "button_style": "Rounded",
        "border_radius": "4px",
        "font": "Default system font",
        "font_size": "16px",
        "font_weight": "Normal",
        "organization_url": organization_url or ""
    }

    try:
        if os.path.exists(guidelines_path):
            with open(guidelines_path, 'r') as f:
                try:
                    # Try to load as multi-organization format (dict of dicts)
                    all_guidelines = json.load(f)

                    # If it's the old format (single org dict), convert it
                    if not isinstance(all_guidelines, dict) or not all(isinstance(all_guidelines.get(k), dict) for k in all_guidelines):
                        # This is the old format, convert it
                        old_guidelines = all_guidelines
                        # Use organization_url as key if available, otherwise use a default key
                        org_url = old_guidelines.get('organization_url') or 'default_organization'
                        all_guidelines = {org_url: old_guidelines}

                    # Return specific organization if requested
                    if organization_url:
                        # Try exact match first
                        if organization_url in all_guidelines:
                            # Ensure all default fields are present
                            guidelines = all_guidelines[organization_url]
                            for key, value in default_guidelines.items():
                                if key not in guidelines:
                                    guidelines[key] = value
                            return guidelines

                        # Try case-insensitive match
                        for url in all_guidelines:
                            if url.lower() == organization_url.lower():
                                # Ensure all default fields are present
                                guidelines = all_guidelines[url]
                                for key, value in default_guidelines.items():
                                    if key not in guidelines:
                                        guidelines[key] = value
                                return guidelines

                        # If organization not found, return default guidelines
                        return default_guidelines

                    # Return all organizations
                    return all_guidelines
                except:
                    # Return default guidelines on error
                    return {'default_organization': default_guidelines}
        else:
            # Return default guidelines if file doesn't exist
            return {'default_organization': default_guidelines}
    except:
        # Return default guidelines on error
        return {'default_organization': default_guidelines}

def save_brand_guidelines(guidelines, organization_url=None):
    """
    Save brand guidelines for a specific organization.

    Args:
        guidelines (dict): Brand guidelines to save
        organization_url (str, optional): URL of the organization.
            If None, uses the 'organization_url' field from guidelines.

    Returns:
        bool: True if save successful, False otherwise
    """
    guidelines_path = os.path.join('data', 'brand_guidelines.json')

    try:
        # Ensure guidelines is a dictionary
        if not isinstance(guidelines, dict):
            print(f"Error: guidelines is not a dictionary: {type(guidelines)}")
            return False

        # Get organization URL
        org_url = organization_url or guidelines.get('organization_url')
        if not org_url:
            # Can't save without URL
            print("Error: No organization URL provided")
            return False

        # Ensure URL is in the guidelines
        guidelines['organization_url'] = org_url

        # Load existing guidelines
        all_guidelines = load_brand_guidelines()

        # Debug output
        print(f"Saving brand guidelines for {org_url}")
        print(f"Loaded {len(all_guidelines)} existing organizations")

        # Update guidelines
        all_guidelines[org_url] = guidelines

        # Save back to file
        with open(guidelines_path, 'w') as f:
            json.dump(all_guidelines, f, indent=2)

        print(f"Successfully saved brand guidelines for {org_url}")
        return True
    except Exception as e:
        import traceback
        print(f"Error saving brand guidelines: {str(e)}")
        print(traceback.format_exc())
        return False

def save_custom_keywords(product_name, keywords):
    """Save custom keywords for a product

    Args:
        product_name (str): Name of the product
        keywords (list): List of keywords
    """
    # Create data directory if it doesn't exist
    os.makedirs('data/custom_keywords', exist_ok=True)

    # Load existing keywords
    all_keywords = load_custom_keywords()

    # If keywords is empty, remove the entry for this product
    if not keywords and product_name in all_keywords:
        del all_keywords[product_name]
    else:
        # Update keywords for this product
        all_keywords[product_name] = keywords

    # Save to file
    with open('data/custom_keywords/keywords.json', 'w') as f:
        json.dump(all_keywords, f, indent=4)

def load_custom_keywords():
    """Load custom keywords for all products

    Returns:
        dict: Dictionary mapping product names to keyword lists
    """
    try:
        with open('data/custom_keywords/keywords.json', 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

