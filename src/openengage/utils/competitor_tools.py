"""
Competitor analysis tools for OpenEngage.
This module provides tools for finding and analyzing competitors.
"""
import os
import json
import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew
from crewai_tools import SerperDevTool, ScrapeWebsiteTool
from openai import OpenAI

# Load environment variables
load_dotenv()

# Set up logging
log_file = 'data/competitor_tools.log'
os.makedirs('data', exist_ok=True)

# Configure logging to write to both file and console
logger = logging.getLogger('CompetitorTools')
logger.setLevel(logging.INFO)

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)

# Create console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# Add both handlers to logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

class CompanyCompetitorFinder:
    """Tool for finding competitors for a company"""

    def __init__(self):
        """Initialize the competitor finder tool"""
        logger.info("Initializing CompanyCompetitorFinder")
        self.search_tool = SerperDevTool(api_key=os.getenv("SERPER_API_KEY"))
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.competitor_researcher = self._create_competitor_researcher()
        logger.info("CompanyCompetitorFinder initialized successfully")

    def _create_competitor_researcher(self) -> Agent:
        """Create an agent that finds competitors"""
        return Agent(
            role="Competitor Research Specialist",
            goal="Find the top competitors for a given company and their website links",
            backstory="""An expert market analyst specializing in identifying direct competitors
            and their online presence. You focus on finding companies that offer similar products
            or services to the target company, paying special attention to the product type.""",
            tools=[self.search_tool],
            verbose=True,
            llm="gpt-4o-mini"
        )

    def find_competitors(self, company_name: str, product_name: str, product_type: str,
                         max_competitors: int = 5) -> List[Dict[str, str]]:
        """
        Find competitors for the given company based on product type.

        Args:
            company_name: Name of the company
            product_name: Name of the product
            product_type: Type of product
            max_competitors: Maximum number of competitors to return (1, 3, or 5)

        Returns:
            List of dictionaries with competitor name and URL
        """
        # Validate max_competitors
        if max_competitors not in [1, 3, 5]:
            max_competitors = 5

        logger.info(f"Finding {max_competitors} competitors for {company_name} ({product_type})")

        # Create a task to find competitors
        competitor_task = Task(
            description=f"Find upto {max_competitors} competitors for {company_name}, the company that makes {product_name}. "
                       f"Search for '{company_name} competitors' and identify the most relevant competing companies. "
                       f"For each competitor, ONLY provide their company name and their website URL in a simple format. "
                       f"Do not include descriptions, products, or any other information. "
                       f"Format the results as a numbered list with company name and URL only.",
            expected_output=f"A numbered list of {max_competitors} competitors with only their company names and website URLs",
            agent=self.competitor_researcher
        )

        # Create a crew with just the competitor researcher
        finder_crew = Crew(
            agents=[self.competitor_researcher],
            tasks=[competitor_task],
            verbose=True
        )

        # Run the crew
        logger.info(f"Starting competitor finder crew for {company_name}")
        result = finder_crew.kickoff()
        logger.info(f"Competitor finder crew completed for {company_name}")

        # Extract the competitor list from the result
        try:
            # Convert CrewOutput to string if needed
            result_str = str(result)

            # Extract company names and URLs using regex
            # This pattern looks for numbered list items with company name and URL
            names = re.findall(r'\d+\.\s+([A-Za-z0-9\s]+)\s+-\s+https?://', result_str)
            urls = re.findall(r'https?://[^\s\n]+', result_str)

            # Combine names and URLs
            competitors = []
            for i in range(min(len(names), len(urls), max_competitors)):
                competitors.append({"name": names[i].strip(), "url": urls[i].strip()})

            logger.info(f"Extracted {len(competitors)} competitors")
            return competitors

        except Exception as e:
            logger.error(f"Error extracting competitors: {str(e)}")
            try:
                logger.error(f"Raw result: {result_str[:500]}...")
            except:
                logger.error("Could not log raw result")
            return []


class CompetitorProductAnalyzer:
    """Tool for analyzing competitor products"""

    def __init__(self):
        """Initialize the product analyzer tool"""
        logger.info("Initializing CompetitorProductAnalyzer")
        self.search_tool = SerperDevTool(api_key=os.getenv("SERPER_API_KEY"))
        self.scrape_tool = ScrapeWebsiteTool()
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.product_analyzer = self._create_product_analyzer()
        logger.info("CompetitorProductAnalyzer initialized successfully")

    def _create_product_analyzer(self) -> Agent:
        """Create an agent that analyzes products"""
        return Agent(
            role="Product Analyzer",
            goal="Analyze competitor products and determine their relevance compared to a reference product",
            backstory="""A product comparison specialist with expertise in evaluating product features
            and determining competitive relevance. You excel at extracting key information from product
            pages and organizing it in a structured format.""",
            tools=[self.scrape_tool, self.search_tool],
            verbose=True,
            llm="gpt-4o-mini"
        )

    def analyze_competitor_product(self, competitor_url: str, our_product: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze a competitor product and compare it to our product.

        Args:
            competitor_url: URL of the competitor's website
            our_product: Dictionary containing our product details

        Returns:
            Dictionary with competitor product details and comparison
        """
        logger.info(f"Analyzing competitor product: {competitor_url}")

        # Extract our product details
        product_name = our_product.get("Product_Name", "")
        company_name = our_product.get("Company_Name", "")
        product_type = our_product.get("Type_of_Product", "")
        product_summary = our_product.get("Product_Summary", "")
        product_features = our_product.get("Product_Features", [])

        # Create a task to analyze the competitor product
        analysis_task = Task(
            description=f"""Analyze the competitor website at {competitor_url} and find products that compete with {product_name} by {company_name}.

                       Our product is a {product_type} with the following summary:
                       {product_summary}

                       Our product features:
                       {', '.join(product_features)}

                       For this competitor:
                       1. Visit their website using the scrape tool
                       2. Identify products that compete directly with our {product_type}
                       3. For each competing product, extract:
                          - Product name
                          - Product description
                          - Key features
                          - Pricing information (if available)
                          - Unique selling points
                          - Product URL (if available)
                       4. Compare each product to our product and identify:
                          - Similarities
                          - What our product does better
                          - What their product does better

                       Return your analysis in this exact JSON format:
                       {{
                           "company_name": "Competitor Company Name",
                           "company_description": "Brief description of the company",
                           "products": [
                               {{
                                   "product_name": "Product Name",
                                   "product_description": "Product description",
                                   "features": ["Feature 1", "Feature 2", ...],
                                   "pricing": "Pricing information",
                                   "unique_selling_points": ["USP 1", "USP 2", ...],
                                   "product_url": "https://product-url.com",
                                   "comparison": {{
                                       "similarity": "Description of similarities",
                                       "our_advantages": "What our product does better",
                                       "their_advantages": "What their product does better"
                                   }}
                               }}
                           ]
                       }}

                       IMPORTANT: You must ONLY use the tools provided to analyze the website. DO NOT generate or suggest
                       information based on your own knowledge. If you cannot find certain information, use "Not available"
                       or an empty array as appropriate.
                       """,
            expected_output="A JSON object with detailed competitor product information and comparison",
            agent=self.product_analyzer
        )

        # Create a crew with just the product analyzer
        analyzer_crew = Crew(
            agents=[self.product_analyzer],
            tasks=[analysis_task],
            verbose=True
        )

        # Run the crew
        logger.info(f"Starting product analyzer crew for {competitor_url}")
        result = analyzer_crew.kickoff()
        logger.info(f"Product analyzer crew completed for {competitor_url}")

        # Extract the JSON object from the result
        try:
            # Convert CrewOutput to string if needed
            result_str = str(result)

            # Try to parse as JSON
            json_match = re.search(r'\{.*\}', result_str, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                analysis = json.loads(json_str)
                logger.info(f"Successfully extracted analysis for {competitor_url}")
                return analysis
            else:
                logger.warning(f"No JSON object found in result for {competitor_url}")
                return {
                    "company_name": "Unknown",
                    "company_description": "Could not extract information",
                    "products": [],
                    "error": "Failed to parse analysis result"
                }

        except Exception as e:
            logger.error(f"Error extracting analysis for {competitor_url}: {str(e)}")
            try:
                logger.error(f"Raw result: {result_str[:500]}...")
            except:
                logger.error("Could not log raw result")
            return {
                "company_name": "Unknown",
                "company_description": "Error during analysis",
                "products": [],
                "error": str(e)
            }

    def analyze_competitors(self, competitors: List[Dict[str, str]], our_product: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze multiple competitors and their products.

        Args:
            competitors: List of dictionaries with competitor name and URL
            our_product: Dictionary containing our product details

        Returns:
            Dictionary with all competitor analyses
        """
        logger.info(f"Analyzing {len(competitors)} competitors")

        results = {}
        for competitor in competitors:
            name = competitor.get("name", "Unknown")
            url = competitor.get("url", "")

            if not url:
                logger.warning(f"Skipping competitor with no URL: {name}")
                continue

            logger.info(f"Analyzing competitor: {name} ({url})")
            analysis = self.analyze_competitor_product(url, our_product)

            # Store the result with the URL as the key
            results[url] = analysis

        logger.info(f"Completed analysis of {len(results)} competitors")
        return results
