"""
Standalone script to send WhatsApp messages from a CSV file.

Usage:
    python send_whatsapp_from_csv.py <csv_file_path>

The CSV file should have the following columns:
    - phone_number: Recipient's phone number
    - Template_ID: WhatsApp template ID
    - param_1: Value for {{1}} in the template (usually first name)
    - param_2: Value for {{2}} in the template (personalized message)
"""
import os
import sys
import time
import pandas as pd
from typing import Dict, Any, List, Optional

# Add parent directory to path to allow importing from core
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Try direct import first
    from core.gupshup_whatsapp_sender import send_test_whatsapp_message, get_esp_api_keys, get_sender_details
except ImportError:
    # Try package import
    from openengage.core.gupshup_whatsapp_sender import send_test_whatsapp_message, get_esp_api_keys, get_sender_details

def clean_phone_number(phone: str) -> str:
    """
    Clean phone number by removing non-numeric characters and ensuring it has country code.
    
    Args:
        phone: Phone number to clean
        
    Returns:
        Cleaned phone number
    """
    # Convert to string if not already
    phone = str(phone)
    
    # Remove all non-numeric characters
    phone = ''.join(filter(str.isdigit, phone))
    
    # Add default country code (91 for India) if not present
    if not phone.startswith('91') and len(phone) <= 10:
        phone = '91' + phone
    
    return phone

def send_whatsapp_messages_from_csv(csv_file_path: str, delay_seconds: float = 1.0) -> Dict[str, Any]:
    """
    Send WhatsApp messages from a CSV file one by one.
    
    Args:
        csv_file_path: Path to the CSV file
        delay_seconds: Delay between messages in seconds
        
    Returns:
        Dict with results of the send operation
    """
    print(f"Reading CSV file: {csv_file_path}")
    
    # Check if file exists
    if not os.path.exists(csv_file_path):
        return {
            "success": False,
            "error": f"CSV file not found: {csv_file_path}"
        }
    
    # Read CSV file
    try:
        campaign_df = pd.read_csv(csv_file_path)
    except Exception as e:
        return {
            "success": False,
            "error": f"Error reading CSV file: {str(e)}"
        }
    
    print(f"Loaded {len(campaign_df)} rows from CSV file")
    
    # Check if required columns exist
    required_columns = ['phone_number', 'Template_ID', 'param_1', 'param_2']
    missing_columns = [col for col in required_columns if col not in campaign_df.columns]
    
    if missing_columns:
        return {
            "success": False,
            "error": f"Missing required columns in CSV file: {', '.join(missing_columns)}"
        }
    
    # Clean phone numbers
    campaign_df['phone_number'] = campaign_df['phone_number'].apply(clean_phone_number)
    
    # Initialize results
    results = {
        "total_accepted": 0,
        "total_rejected": 0,
        "errors": [],
        "message_data": pd.DataFrame(columns=['Phone_Number', 'Template_ID', 'Status', 'Message_ID', 'Time_Send'])
    }
    
    # Get total number of messages to send
    total_messages = len(campaign_df)
    
    print(f"Preparing to send {total_messages} WhatsApp messages one by one...")
    
    # Process each message one by one
    for idx, row in campaign_df.iterrows():
        try:
            # Get message parameters
            phone_number = str(row['phone_number'])
            template_id = str(row['Template_ID'])
            param_1 = str(row['param_1']) if 'param_1' in row else ""
            param_2 = str(row['param_2']) if 'param_2' in row else ""
            
            print(f"[{idx+1}/{total_messages}] Sending message to {phone_number} using template {template_id}...")
            
            # Create variable values list
            variable_values = [param_1, param_2]
            
            # Send the message using gupshup_whatsapp_sender
            message_result = send_test_whatsapp_message(
                recipient_phone=phone_number,
                selected_template_id=template_id,
                variable_values=variable_values,
                selected_esp="Gupshup"  # Default to Gupshup
            )
            
            # Process result
            if message_result.get("success", False) or message_result.get("total_accepted", 0) > 0:
                results["total_accepted"] += 1
                print(f"  ✅ Message sent successfully!")
                
                # Add message data if available
                if "message_data" in message_result and message_result["message_data"] is not None:
                    message_data = message_result["message_data"].copy()
                    message_data['Phone_Number'] = phone_number
                    message_data['Template_ID'] = template_id
                    results["message_data"] = pd.concat([results["message_data"], message_data])
            else:
                results["total_rejected"] += 1
                error_msg = message_result.get("error", "Unknown error")
                results["errors"].append(f"Error sending to {phone_number}: {error_msg}")
                print(f"  ❌ Failed to send message: {error_msg}")
                
            # Add a delay between messages to avoid rate limiting
            if idx < total_messages - 1:  # No need to delay after the last message
                print(f"  Waiting {delay_seconds} seconds before sending next message...")
                time.sleep(delay_seconds)
            
        except Exception as e:
            # Handle any exceptions
            results["total_rejected"] += 1
            results["errors"].append(f"Error sending to {row['phone_number']}: {str(e)}")
            print(f"  ❌ Exception: {str(e)}")
    
    # Print summary
    print("\nSummary:")
    print(f"Total messages: {total_messages}")
    print(f"Successfully sent: {results['total_accepted']}")
    print(f"Failed: {results['total_rejected']}")
    
    if results["errors"]:
        print("\nErrors:")
        for error in results["errors"]:
            print(f"  - {error}")
    
    # Save results to CSV
    if not results["message_data"].empty:
        results_file = f"whatsapp_results_{time.strftime('%Y%m%d_%H%M%S')}.csv"
        results["message_data"].to_csv(results_file, index=False)
        print(f"\nResults saved to: {results_file}")
    
    return results

def main():
    """Main function to run the script."""
    # Check command line arguments
    if len(sys.argv) < 2:
        print(f"Usage: python {sys.argv[0]} <csv_file_path> [delay_seconds]")
        sys.exit(1)
    
    # Get CSV file path
    csv_file_path = sys.argv[1]
    
    # Get delay seconds (optional)
    delay_seconds = 1.0
    if len(sys.argv) > 2:
        try:
            delay_seconds = float(sys.argv[2])
        except ValueError:
            print(f"Invalid delay seconds: {sys.argv[2]}. Using default: 1.0")
    
    # Send WhatsApp messages
    send_whatsapp_messages_from_csv(csv_file_path, delay_seconds)

if __name__ == "__main__":
    main()
