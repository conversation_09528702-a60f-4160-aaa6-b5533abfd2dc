# from core.data_integration import DataIntegration
# di=DataIntegration()
# di.refresh_data(fetch_signup=False, fetch_buyers=False, fetch_leads=False, fetch_unsubscribers=False, fetch_brahma=True)
# import psycopg2 as pcg
# nmp_connection = pcg.connect("postgres://aiuser:<EMAIL>/newmarketingpages")
# nmp_cursor = nmp_connection.cursor()


# import pandas as pd
# df=pd.read_csv("/Users/<USER>/Desktop/openengage copy 4/Sample Data For Mass Generation/TargetAudience.csv")
# df