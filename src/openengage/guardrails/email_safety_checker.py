import os
import json
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from openai import OpenAI
from guardrails import Guard, OnFailAction
from guardrails.validator_base import (
    ValidationResult,
    Validator,
    register_validator,
    FailResult,
    PassResult
)
from pydantic import BaseModel

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Define a custom validator for NSFW content detection
@register_validator(name="nsfw_content_detector", data_type="string")
class NSFWContentDetector(Validator):
    """
    A custom validator that detects NSFW content in email bodies.
    Uses OpenAI's capabilities to analyze text for inappropriate content.
    """
    def __init__(self, threshold: float = 0.8, **kwargs):
        self.threshold = threshold
        super().__init__(**kwargs)

    def validate(
        self, value: str, metadata: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        try:
            print(f"Validating email content for NSFW elements...")
            
            # Use OpenAI to analyze the content
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a content moderation assistant. Your task is to identify if the provided text contains any NSFW (Not Safe For Work) content, including but not limited to: explicit sexual content, graphic violence, hate speech, illegal activities, or other inappropriate professional communication. Respond with a JSON object containing 'is_nsfw' (boolean), 'confidence' (float between 0 and 1), and 'categories' (list of detected inappropriate categories)."},
                    {"role": "user", "content": f"Analyze this email content for NSFW elements: {value}"}
                ],
                temperature=0.1
            )
            
            # Extract the response
            analysis_text = response.choices[0].message.content
            
            # Parse the JSON response
            try:
                # Try to extract JSON from the response
                if '{' in analysis_text and '}' in analysis_text:
                    json_str = analysis_text[analysis_text.find('{'):analysis_text.rfind('}')+1]
                    analysis = json.loads(json_str)
                else:
                    # Fallback if no JSON structure is found
                    is_nsfw = "nsfw" in analysis_text.lower() or "inappropriate" in analysis_text.lower()
                    analysis = {
                        "is_nsfw": is_nsfw,
                        "confidence": 0.9 if is_nsfw else 0.1,
                        "categories": ["detected from text"] if is_nsfw else []
                    }
            except json.JSONDecodeError:
                # Handle case where response isn't valid JSON
                is_nsfw = "nsfw" in analysis_text.lower() or "inappropriate" in analysis_text.lower()
                analysis = {
                    "is_nsfw": is_nsfw,
                    "confidence": 0.9 if is_nsfw else 0.1,
                    "categories": ["detected from text"] if is_nsfw else []
                }
            
            # Check if content is NSFW with confidence above threshold
            if analysis.get("is_nsfw", False) and analysis.get("confidence", 0) >= self.threshold:
                categories = analysis.get("categories", ["inappropriate content"])
                return FailResult(
                    error_message=f"Email contains NSFW content in categories: {', '.join(categories)}",
                    fix_value=None
                )
            
            return PassResult()
            
        except Exception as e:
            print(f"Error during NSFW validation: {str(e)}")
            # In case of error, fail safe by passing the content
            return PassResult()


# Create a Guard with the NSFW content detector
def create_email_safety_guard():
    """Create and return a Guard for email safety checking"""
    guard = Guard().use(
        NSFWContentDetector(
            threshold=0.7,  # Set threshold for NSFW detection
            on_fail=OnFailAction.EXCEPTION  # Throw exception on failure
        )
    )
    return guard


# Function to check if an email is safe
def check_email_safety(email_body: str) -> Dict[str, Any]:
    """
    Check if an email body is safe to send.
    
    Args:
        email_body: The content of the email to check
        
    Returns:
        Dictionary with safety information
    """
    guard = create_email_safety_guard()
    
    try:
        # Validate the email content
        validation_result = guard.validate(email_body)
        return {
            "is_safe": True,
            "message": "Email content is appropriate and safe to send",
            "details": {
                "validation_passed": True,
                "threshold": 0.7,
                "validation_method": "nsfw_content_detector"
            }
        }
    except Exception as e:
        return {
            "is_safe": False,
            "message": str(e),
            "details": {
                "validation_passed": False,
                "threshold": 0.7,
                "validation_method": "nsfw_content_detector"
            }
        }


# Test the email safety checker with sample emails
if __name__ == "__main__":
    # Safe email example
    safe_email = """
    Dear Marketing Team,
    
    I wanted to follow up on our discussion about the Q3 marketing campaign for our new product line. 
    As mentioned in our meeting, we need to finalize the creative assets by next Friday.
    
    Could you please share the latest mockups with the team? I'd also appreciate an update on the 
    social media strategy we discussed.
    
    Looking forward to your response.
    
    Best regards,
    John Smith
    Product Manager
    """
    
    # Potentially unsafe email example
    unsafe_email = """
    Hey team,
    
    Check out this a**hole did this 
    
    Cheers,
    Bob
    """
    
    # Test both emails
    print("\n=== Testing Safe Email ===")
    safe_result = check_email_safety(safe_email)
    print(json.dumps(safe_result, indent=2))
    
    print("\n=== Testing Unsafe Email ===")
    unsafe_result = check_email_safety(unsafe_email)
    print(json.dumps(unsafe_result, indent=2))
