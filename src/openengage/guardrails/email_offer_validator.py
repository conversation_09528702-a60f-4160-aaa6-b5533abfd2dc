import os
import json
import re
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from openai import OpenAI
from guardrails import Guard, OnFailAction
from guardrails.validator_base import (
    ValidationResult,
    Validator,
    register_validator,
    FailResult,
    PassResult
)

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Global variable to store loaded data
EMAIL_GOALS = None

# Function to load email goals data on demand
def load_email_goals():
    global EMAIL_GOALS
    if EMAIL_GOALS is None:
        try:
            with open('email_goals.json', 'r') as f:
                EMAIL_GOALS = json.load(f)
        except FileNotFoundError:
            EMAIL_GOALS = {}
    return EMAIL_GOALS

# Define a custom validator for offer validation
@register_validator(name="offer_validator", data_type="string")
class OfferValidator(Validator):
    """
    A custom validator that checks if an email correctly includes or excludes offers
    according to the specifications in email_goals.json.
    """
    def __init__(
        self, 
        stage: str,
        template_name: str,
        **kwargs
    ):
        self.stage = stage
        self.template_name = template_name
        self.template_data = self._get_template_data()
        super().__init__(**kwargs)
    
    def _get_template_data(self) -> Dict:
        """Retrieve the template data based on stage and template_name"""
        email_goals = load_email_goals()
        if self.stage not in email_goals:
            raise ValueError(f"Stage '{self.stage}' not found in email goals")
        
        email_goals = load_email_goals()
        for template in email_goals[self.stage]:
            if template["name"] == self.template_name:
                return template
        
        raise ValueError(f"Template '{self.template_name}' not found in '{self.stage}' stage")
    
    def validate(
        self, value: str, metadata: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        try:
            print(f"Validating email offers for template: {self.template_name}")
            
            # Get expected offer status and coupon
            should_have_offer = self.template_data["offer"].lower() == "yes"
            expected_coupon = self.template_data["offer_coupon"]
            
            # Use OpenAI to analyze the email content for offers and coupons
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"""You are an email validator assistant. 
                    Your task is to analyze the provided email content and determine:
                    
                    1. Does the email contain any special offers, discounts, promotions, or incentives?
                    2. If yes, what specific offers are mentioned?
                    3. Does the email contain any coupon or promo codes?
                    4. If yes, what are the exact coupon/promo codes mentioned?
                    
                    Respond with a JSON object containing:
                    - "contains_offer": Boolean indicating if the email contains any offers
                    - "offer_details": List of offers mentioned in the email (empty list if none)
                    - "contains_coupon": Boolean indicating if the email contains any coupon codes
                    - "coupon_codes": List of coupon codes mentioned in the email (empty list if none)
                    """},
                    {"role": "user", "content": f"Email content: {value}"}
                ],
                temperature=0.1
            )
            
            # Extract the response
            analysis_text = response.choices[0].message.content
            
            # Parse the JSON response
            try:
                # Try to extract JSON from the response
                if '{' in analysis_text and '}' in analysis_text:
                    json_str = analysis_text[analysis_text.find('{'):analysis_text.rfind('}')+1]
                    analysis = json.loads(json_str)
                else:
                    return FailResult(
                        error_message="Failed to parse validation results",
                    )
            except json.JSONDecodeError:
                return FailResult(
                    error_message="Failed to parse validation results",
                )
            
            # Check if the email correctly includes/excludes offers
            has_offer = analysis.get("contains_offer", False)
            has_coupon = analysis.get("contains_coupon", False)
            coupon_codes = analysis.get("coupon_codes", [])
            
            validation_issues = []
            
            # Check if offer inclusion/exclusion is correct
            if should_have_offer and not has_offer:
                validation_issues.append(f"Email should include an offer but none was found")
            elif not should_have_offer and has_offer:
                validation_issues.append(f"Email should not include any offers but found: {', '.join(analysis.get('offer_details', []))}")
            
            # Check if coupon is correct (only if offer should be included)
            if should_have_offer:
                if expected_coupon and not has_coupon:
                    validation_issues.append(f"Email should include coupon code '{expected_coupon}' but no coupon was found")
                elif expected_coupon and has_coupon:
                    # Check if the expected coupon is in the list of found coupons
                    if not any(expected_coupon in code for code in coupon_codes):
                        validation_issues.append(f"Email should include coupon code '{expected_coupon}' but found different coupon(s): {', '.join(coupon_codes)}")
                elif not expected_coupon and has_coupon:
                    validation_issues.append(f"Email should not include any coupon codes but found: {', '.join(coupon_codes)}")
            
            # Return validation result
            if validation_issues:
                return FailResult(
                    error_message=f"Email offer validation failed: {'; '.join(validation_issues)}",
                )
            
            return PassResult()
            
        except Exception as e:
            print(f"Error during offer validation: {str(e)}")
            return FailResult(
                error_message=f"Error during validation: {str(e)}",
            )


# Create a Guard with the offer validator
def create_offer_validation_guard(stage: str, template_name: str) -> Guard:
    """Create and return a Guard for email offer validation"""
    guard = Guard().use(
        OfferValidator(
            stage=stage,
            template_name=template_name,
            on_fail=OnFailAction.EXCEPTION
        )
    )
    return guard


# Function to validate email offers
def validate_email_offers(
    email_body: str, 
    stage: str,
    template_name: str
) -> Dict[str, Any]:
    """
    Validate if an email correctly includes or excludes offers according to specifications.
    
    Args:
        email_body: The content of the email to validate
        stage: The stage from email_goals.json (e.g., "New", "Product Viewed")
        template_name: The name of the template to validate against
        
    Returns:
        Dictionary with validation information
    """
    try:
        # Get template data for reference
        template_data = None
        email_goals = load_email_goals()
        for template in email_goals.get(stage, []):
            if template["name"] == template_name:
                template_data = template
                break
        
        if not template_data:
            return {
                "is_valid": False,
                "message": f"Template '{template_name}' not found in '{stage}' stage",
                "details": {
                    "validation_passed": False,
                    "validation_method": "offer_validator"
                }
            }
        
        # Create guard for offer validation
        guard = create_offer_validation_guard(stage, template_name)
        
        # Validate the email content
        validation_result = guard.validate(email_body)
        
        return {
            "is_valid": True,
            "message": "Email correctly includes/excludes offers according to specifications",
            "details": {
                "validation_passed": True,
                "stage": stage,
                "template_name": template_name,
                "should_have_offer": template_data["offer"].lower() == "yes",
                "expected_coupon": template_data["offer_coupon"],
                "validation_method": "offer_validator"
            }
        }
    except Exception as e:
        return {
            "is_valid": False,
            "message": str(e),
            "details": {
                "validation_passed": False,
                "stage": stage,
                "template_name": template_name,
                "validation_method": "offer_validator"
            }
        }


# Test the email offer validator with sample emails
if __name__ == "__main__":
    # Test 1: Email without offer (correct for PinnaclePlus template)
    no_offer_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """
    
    # Test 2: Email with offer (incorrect for PinnaclePlus template)
    incorrect_offer_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    Special Offer: Enroll now and get 25% off your first month! Use coupon code PINNACLE25 at checkout.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """
    
    # Test 3: Email with correct offer and coupon (for ProjectPro template)
    correct_offer_email = """
    Hi there,

    Are you ready to dive into the transformative world of Generative AI? At ProjectPro, we believe that the future belongs to those who embrace new technologies, and our Generative AI Accelerator Program is designed to equip you with the skills to lead that change.

    One of the standout features of our program is the Unlimited 1:1 Mentorship Sessions with Industry Experts. This personalized guidance ensures you not only learn the theoretical aspects but also gain practical insights directly from the pioneers in the field. With 220+ Hours of Hands-On Learning and 80+ Enterprise-Grade Projects, you will have the opportunity to work on real-world challenges and enhance your problem-solving skills.

    Special Limited-Time Offer: Sign up today and receive 25% off your enrollment fee! Use coupon code GENAI25 during checkout to claim this exclusive discount.

    Ready to take the leap? Explore more about the program and how it can reshape your career here: https://www.projectpro.io/accelerator-program/generative-ai-program

    Believe in your potential to excel in Generative AI!

    Best Regards,
    Team ProjectPro
    """
    
    # Test 4: Email with offer but wrong coupon (for ProjectPro template)
    wrong_coupon_email = """
    Hi there,

    Are you ready to dive into the transformative world of Generative AI? At ProjectPro, we believe that the future belongs to those who embrace new technologies, and our Generative AI Accelerator Program is designed to equip you with the skills to lead that change.

    One of the standout features of our program is the Unlimited 1:1 Mentorship Sessions with Industry Experts. This personalized guidance ensures you not only learn the theoretical aspects but also gain practical insights directly from the pioneers in the field. With 220+ Hours of Hands-On Learning and 80+ Enterprise-Grade Projects, you will have the opportunity to work on real-world challenges and enhance your problem-solving skills.

    Special Limited-Time Offer: Sign up today and receive 25% off your enrollment fee! Use coupon code GENAI50 during checkout to claim this exclusive discount.

    Ready to take the leap? Explore more about the program and how it can reshape your career here: https://www.projectpro.io/accelerator-program/generative-ai-program

    Believe in your potential to excel in Generative AI!

    Best Regards,
    Team ProjectPro
    """
    
    # Test 5: Email missing required offer (for Abandoned Cart template)
    missing_offer_email = """
    Hi there,

    We noticed you recently added the Pro Stand to your cart but didn't complete your purchase. The Pro Stand is designed to seamlessly integrate with your Pro Display XDR, providing a versatile setup to enhance your productivity.

    With features like adjustable height and tilt, you can customize your viewing experience. The Pro Stand allows for a total height adjustment of 120mm and a tilt range of -5° to +25°, ensuring that your display is always at the perfect angle for maximum comfort and efficiency.

    If you have any questions about the Pro Stand or need assistance with your purchase, our support team is here to help.

    Ready to take your workspace to the next level? Complete your purchase here: https://www.apple.com/in/shop/product/MX5N3Z/A/pro-stand

    Best Regards,
    Apple
    """
    
    # Run tests
    print("\n=== Testing Email Without Offer (Correct for PinnaclePlus) ===")
    no_offer_result = validate_email_offers(
        no_offer_email, 
        "New", 
        "PinnaclePlus - New - 1"
    )
    print(json.dumps(no_offer_result, indent=2))
    
    print("\n=== Testing Email With Incorrect Offer (For PinnaclePlus) ===")
    incorrect_offer_result = validate_email_offers(
        incorrect_offer_email, 
        "New", 
        "PinnaclePlus - New - 1"
    )
    print(json.dumps(incorrect_offer_result, indent=2))
    
    print("\n=== Testing Email With Correct Offer and Coupon (For ProjectPro) ===")
    correct_offer_result = validate_email_offers(
        correct_offer_email, 
        "New", 
        "ProjectPro- GenAI Acc Program - New -1"
    )
    print(json.dumps(correct_offer_result, indent=2))
    
    print("\n=== Testing Email With Offer But Wrong Coupon (For ProjectPro) ===")
    wrong_coupon_result = validate_email_offers(
        wrong_coupon_email, 
        "New", 
        "ProjectPro- GenAI Acc Program - New -1"
    )
    print(json.dumps(wrong_coupon_result, indent=2))
    
    print("\n=== Testing Email Missing Required Offer (For Abandoned Cart) ===")
    missing_offer_result = validate_email_offers(
        missing_offer_email, 
        "Abandoned Cart", 
        "AppleProStand - Abandoned Cart - 1"
    )
    print(json.dumps(missing_offer_result, indent=2))
