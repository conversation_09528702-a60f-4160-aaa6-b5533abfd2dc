import os
import json
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from openai import OpenAI
from guardrails import Guard, OnFailAction
from guardrails.validator_base import (
    ValidationResult,
    Validator,
    register_validator,
    FailResult,
    PassResult
)
from pydantic import BaseModel

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


# Global variable to store loaded data
USER_JOURNEY = None

# Function to load user journey data on demand
def load_user_journey():
    global USER_JOURNEY
    if USER_JOURNEY is None:
        try:
            with open('data/user_journey.json', 'r') as f:
                USER_JOURNEY = json.load(f)
        except FileNotFoundError:
            USER_JOURNEY = {}
    return USER_JOURNEY

# Define a custom validator for stage goal validation
@register_validator(name="stage_goal_validator", data_type="string")
class StageGoalValidator(Validator):
    """
    A custom validator that checks if an email satisfies the stage goal.
    Uses user_journey.json to determine the goal stage based on current stage.
    """
    def __init__(
        self,
        stage: str,
        product_name: str,
        **kwargs
    ):
        self.product_name = product_name
        self.stage = stage
        self.journey_data = self._get_journey_data()
        super().__init__(**kwargs)

    def _get_journey_data(self) -> Dict:
        """Retrieve the journey data based on current stage from user_journey.json"""
        user_journey = load_user_journey()
        for journey_stage in user_journey.get(self.product_name, []):
            if journey_stage["current_stage"] == self.stage:
                return journey_stage

        # If not found, return empty dict with default values
        return {"current_stage": self.stage, "description": "", "goal_stage": ""}

    def validate(
        self, value: str, metadata: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        try:
            # Get stage goal from user_journey.json
            journey_description = self.journey_data.get('description', '')
            journey_goal_stage = self.journey_data.get('goal_stage', '')

            print(f"Validating email against stage goal using user journey data")
            print(f"Current stage: {self.stage}")
            print(f"Description: {journey_description}")
            print(f"Goal stage: {journey_goal_stage}")

            # Use OpenAI to analyze if the email satisfies the stage goal
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"""You are an email marketing expert.
                    Your task is to analyze if the provided email content satisfies the specified stage goal. Don't be very strict.

                    Current Stage: {self.stage}
                    Current Stage Description: {journey_description}
                    Goal Stage: {journey_goal_stage}

                    Analyze the email content and determine:
                    1. Does the email effectively move the user from the current stage to the goal stage?
                    2. Does the email address the current stage description and work toward the goal stage?
                    3. What specific elements in the email support or fail to support this transition?
                    4. What improvements could be made to better facilitate this stage transition?

                    Respond with a JSON object containing:
                    - "satisfies_goal": Boolean indicating if the email effectively facilitates the stage transition
                    - "supporting_elements": List of elements in the email that support the stage transition
                    - "missing_elements": List of elements that should be included to better facilitate the transition
                    - "improvement_suggestions": List of suggestions to improve the email
                    - "confidence_score": Number between 0 and 1 indicating your confidence in the assessment
                    """},
                    {"role": "user", "content": f"Email content: {value}"}
                ],
                temperature=0.1
            )

            # Extract the response
            analysis_text = response.choices[0].message.content

            # Parse the JSON response
            try:
                # Try to extract JSON from the response
                if '{' in analysis_text and '}' in analysis_text:
                    json_str = analysis_text[analysis_text.find('{'):analysis_text.rfind('}')+1]
                    analysis = json.loads(json_str)
                else:
                    return FailResult(
                        error_message="Failed to parse validation results",
                    )
            except json.JSONDecodeError:
                return FailResult(
                    error_message="Failed to parse validation results",
                )

            # Check if the email satisfies the stage goal
            if not analysis.get("satisfies_goal", False) or analysis.get("confidence_score", 0) < 0.7:
                missing_elements = analysis.get("missing_elements", [])
                suggestions = analysis.get("improvement_suggestions", [])

                error_message = f"Email does not adequately satisfy the stage goal: {journey_goal_stage}"
                if missing_elements:
                    error_message += f"\n\nMissing elements: {', '.join(missing_elements)}"
                if suggestions:
                    error_message += f"\n\nSuggestions for improvement: {', '.join(suggestions)}"

                return FailResult(
                    error_message=error_message,
                )

            return PassResult()

        except Exception as e:
            print(f"Error during stage goal validation: {str(e)}")
            return FailResult(
                error_message=f"Error during validation: {str(e)}",
            )


# Define a custom validator for overall objective validation
@register_validator(name="objective_validator", data_type="string")
class ObjectiveValidator(Validator):
    """
    A custom validator that checks if an email satisfies the overall objective.
    Uses user_journey.json to determine the goal stage based on current stage.
    """
    def __init__(
        self,
        stage: str,
        product_name: str,
        **kwargs
    ):
        self.stage = stage
        self.product_name = product_name
        self.journey_data = self._get_journey_data()
        super().__init__(**kwargs)

    def _get_journey_data(self) -> Dict:
        """Retrieve the journey data based on current stage from user_journey.json"""
        user_journey = load_user_journey()
        for journey_stage in user_journey.get(self.product_name, []):
            if journey_stage["current_stage"] == self.stage:
                return journey_stage

        # If not found, return empty dict with default values
        return {"current_stage": self.stage, "description": "", "goal_stage": ""}

    def validate(
        self, value: str, metadata: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        try:
            # Get overall objective from user_journey.json
            journey_description = self.journey_data.get('description', '')
            journey_goal_stage = self.journey_data.get('goal_stage', '')

            print(f"Validating email against overall objective using user journey data")
            print(f"Current stage: {self.stage}")
            print(f"Description: {journey_description}")
            print(f"Goal stage: {journey_goal_stage}")

            # Use OpenAI to analyze if the email satisfies the overall objective
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"""You are an email marketing expert.
                    Your task is to analyze if the provided email content satisfies the specified overall objective. Don't be very strict.

                    Current Stage: {self.stage}
                    Current Stage Description: {journey_description}
                    Goal Stage: {journey_goal_stage}

                    Analyze the email content and determine:
                    1. Does the email effectively work toward moving the user from the current stage to the goal stage?
                    2. What specific elements in the email support or fail to support this journey progression?
                    3. Does the email include a clear call-to-action aligned with the goal stage?
                    4. What improvements could be made to better achieve the user journey progression?

                    Respond with a JSON object containing:
                    - "satisfies_objective": Boolean indicating if the email effectively supports the user journey progression
                    - "supporting_elements": List of elements in the email that support the journey progression
                    - "missing_elements": List of elements that should be included to better support the journey progression
                    - "has_clear_cta": Boolean indicating if the email has a clear call-to-action aligned with the goal stage
                    - "improvement_suggestions": List of suggestions to improve the email
                    - "confidence_score": Number between 0 and 1 indicating your confidence in the assessment
                    """},
                    {"role": "user", "content": f"Email content: {value}"}
                ],
                temperature=0.1
            )

            # Extract the response
            analysis_text = response.choices[0].message.content

            # Parse the JSON response
            try:
                # Try to extract JSON from the response
                if '{' in analysis_text and '}' in analysis_text:
                    json_str = analysis_text[analysis_text.find('{'):analysis_text.rfind('}')+1]
                    analysis = json.loads(json_str)
                else:
                    return FailResult(
                        error_message="Failed to parse validation results",
                    )
            except json.JSONDecodeError:
                return FailResult(
                    error_message="Failed to parse validation results",
                )

            # Check if the email satisfies the overall objective
            validation_issues = []

            if not analysis.get("satisfies_objective", False) or analysis.get("confidence_score", 0) < 0.7:
                validation_issues.append("Email does not adequately satisfy the overall objective")

            if not analysis.get("has_clear_cta", False):
                validation_issues.append("Email lacks a clear call-to-action aligned with the objective")

            if validation_issues:
                missing_elements = analysis.get("missing_elements", [])
                suggestions = analysis.get("improvement_suggestions", [])

                error_message = f"Email validation failed: {'; '.join(validation_issues)}"
                if missing_elements:
                    error_message += f"\n\nMissing elements: {', '.join(missing_elements)}"
                if suggestions:
                    error_message += f"\n\nSuggestions for improvement: {', '.join(suggestions)}"

                return FailResult(
                    error_message=error_message,
                )

            return PassResult()

        except Exception as e:
            print(f"Error during objective validation: {str(e)}")
            return FailResult(
                error_message=f"Error during validation: {str(e)}",
            )


# Create a Guard with both validators
def create_email_goal_guard(stage: str, product_name: str) -> Guard:
    """Create and return a Guard for email goal and objective validation"""
    guard = Guard().use_many(
        StageGoalValidator(
            stage=stage,
            product_name=product_name,
            on_fail=OnFailAction.EXCEPTION
        ),
        ObjectiveValidator(
            stage=stage,
            product_name=product_name,
            on_fail=OnFailAction.EXCEPTION
        )
    )
    return guard


# Function to validate email against goals and objectives
def validate_email_goals(
    email_body: str,
    stage: str,
    product_name: str
) -> Dict[str, Any]:
    """
    Validate if an email satisfies the stage goal and overall objective.
    Uses both email_goals.json (for backward compatibility) and user_journey.json.

    Args:
        email_body: The content of the email to validate
        stage: The stage from email_goals.json (e.g., "New", "Product Viewed")
        product_name: The name of the product to validate against

    Returns:
        Dictionary with validation information
    """
    try:

        # Get journey data from user_journey.json
        journey_data = None
        user_journey = load_user_journey()
        for journey_stage in user_journey.get(product_name, []):
            if journey_stage["current_stage"] == stage:
                journey_data = journey_stage
                break

        if not journey_data:
            return {
                "is_valid": False,
                "message": f"Product '{product_name}' not found in '{stage}' stage goals",
                "details": {
                    "validation_passed": False,
                    "validation_method": "goal_validators"
                }
            }

        # Create guard for goal validation
        guard = create_email_goal_guard(stage, product_name)

        # Validate the email content
        validation_result = guard.validate(email_body)

        # Prepare response with both email_goals.json and user_journey.json data
        response = {
            "is_valid": True,
            "message": "Email satisfies both the stage goal and overall objective",
            "details": {
                "validation_passed": True,
                "stage": stage,
                "product_name": product_name,
                "validation_method": "goal_validators"
            }
        }

        # Add user_journey.json data if available
        if journey_data:
            response["details"]["current_stage_description"] = journey_data.get("description", "")
            response["details"]["goal_stage"] = journey_data.get("goal_stage", "")

        return response
    except Exception as e:
        return {
            "is_valid": False,
            "message": str(e),
            "details": {
                "validation_passed": False,
                "stage": stage,
                "product_name": product_name,
                "validation_method": "goal_validators"
            }
        }


# Test the email goal validator with sample emails
if __name__ == "__main__":
    # Test 1: Email that satisfies both stage goal and overall objective
    good_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """

    # Test 2: Email that fails to satisfy the stage goal (lacks introduction of key features)
    poor_stage_goal_email = """
    Hi there,

    The GenAI Pinnacle Plus Program is now available for enrollment.

    Please visit our website to learn more: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """

    # Test 3: Email that fails to satisfy the overall objective (no clear call-to-action)
    poor_objective_email = """
    Hi there,

    We're excited to introduce our GenAI Pinnacle Plus Program designed to help you master Generative AI and Agentic AI with an advanced curriculum.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey.

    Best Regards,
    Team Analytics Vidhya
    """


    # Run tests
    print("\n=== Testing Good Email ===")
    good_result = validate_email_goals(
        good_email,
        "New Visitor",
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(good_result, indent=2))

    print("\n=== Testing Email with Poor Stage Goal Satisfaction ===")
    poor_stage_result = validate_email_goals(
        poor_stage_goal_email,
        "New Visitor",
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(poor_stage_result, indent=2))

    print("\n=== Testing Email with Poor Objective Satisfaction ===")
    poor_objective_result = validate_email_goals(
        poor_objective_email,
        "New Visitor",
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(poor_objective_result, indent=2))


