import os
import json
from typing import Dict, List, Optional, Any, Set
from dotenv import load_dotenv
from openai import OpenAI
from guardrails import Guard, OnFailAction
from guardrails.validator_base import (
    ValidationResult,
    Validator,
    register_validator,
    FailResult,
    PassResult
)

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Global variable to store loaded data
PRODUCT_DATA = None

# Function to load product details on demand
def load_product_data():
    global PRODUCT_DATA
    if PRODUCT_DATA is None:
        try:
            with open('data/product_details.json', 'r') as f:
                PRODUCT_DATA = json.load(f)
        except FileNotFoundError:
            PRODUCT_DATA = []
    return PRODUCT_DATA

# Define a custom validator for product feature validation
@register_validator(name="product_feature_validator", data_type="string")
class ProductFeatureValidator(Validator):
    """
    A custom validator that checks if the product features mentioned in an email
    are present in the Product_Features list from the template.
    """
    def __init__(
        self, 
        product_name: Optional[str] = None,
        **kwargs
    ):
        self.product_name = product_name
        self.product_features = self._get_product_features()
        super().__init__(**kwargs)
    
    def _get_product_features(self) -> List[str]:
        """Retrieve the product features based on product_name"""
        
        # If product_name is provided, get features from that specific product
        product_data = load_product_data()
        for product in product_data:
            if product["Product_Name"] == self.product_name:
                return product["Product_Features"]
        raise ValueError(f"Product '{self.product_name}' not found")
        
    
    def validate(
        self, value: str, metadata: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        try:
            print(f"Validating email features against product features list...")
            product_name = ""
            
            # If product_name is provided, get the product name
            product_data = load_product_data()
            for product in product_data:
                if product["Product_Name"] == self.product_name:
                    product_name = product["Product_Name"]
                    break
            
            # Use OpenAI to extract features from the email
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"""You are a product feature validator. 
                    Your task is to extract all product features mentioned in the email content and check if they match with the official product features.
                    
                    Official Product Features:
                    {json.dumps(self.product_features, indent=2)}
                    
                    For each feature mentioned in the email, determine if it matches or is similar to any of the official features.
                    A feature matches if it conveys the same benefit or capability, even if the wording is slightly different.
                    
                    Respond with a JSON object containing:
                    1. "extracted_features": List of all product features mentioned in the email
                    2. "matching_features": List of features that match official features
                    3. "non_matching_features": List of features mentioned that don't match any official features
                    4. "all_features_match": Boolean indicating if all mentioned features match official features
                    5. "missing_official_features": List of official features not mentioned in the email
                    """},
                    {"role": "user", "content": f"Product name: {product_name}\n\nEmail content: {value}"}
                ],
                temperature=0.1
            )
            
            # Extract the response
            analysis_text = response.choices[0].message.content
            
            # Parse the JSON response
            try:
                # Try to extract JSON from the response
                if '{' in analysis_text and '}' in analysis_text:
                    json_str = analysis_text[analysis_text.find('{'):analysis_text.rfind('}')+1]
                    analysis = json.loads(json_str)
                else:
                    return FailResult(
                        error_message="Failed to parse validation results",
                    )
            except json.JSONDecodeError:
                return FailResult(
                    error_message="Failed to parse validation results",
                )
            
            # Check if all mentioned features match official features
            if not analysis.get("all_features_match", False):
                non_matching_features = analysis.get("non_matching_features", [])
                return FailResult(
                    error_message=f"Email mentions features that are not in the official product features list: {', '.join(non_matching_features)}",
                    fix_value=None
                )
            
            return PassResult()
            
        except Exception as e:
            print(f"Error during feature validation: {str(e)}")
            return FailResult(
                error_message=f"Error during validation: {str(e)}",
            )


# Create a Guard with the product feature validator
def create_feature_validation_guard(product_name: Optional[str] = None) -> Guard:
    """Create and return a Guard for product feature validation"""
    guard = Guard().use(
        ProductFeatureValidator(
            product_name=product_name,
            on_fail=OnFailAction.EXCEPTION
        )
    )
    return guard


# Function to validate email features
def validate_email_features(
    email_body: str, 
    product_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Validate if the product features mentioned in an email are present in the official Product_Features list.
    
    Args:
        email_body: The content of the email to validate
        product_name: Optional name of the specific product to validate against
        
    Returns:
        Dictionary with validation information
    """
    try:
        # Create guard for feature validation
        guard = create_feature_validation_guard(product_name)
        
        # Get template data for reference
        product_data = None
        if product_name:
            product_data = load_product_data()
            for product in product_data:
                if product["Product_Name"] == product_name:
                    product_data = product
                    break
        
        if not product_data:
            return {
                "is_valid": False,
                "message": f"Product data not found for product '{product_name}'",
                "details": {
                    "validation_passed": False,
                    "validation_method": "product_feature_validator"
                }
            }
        
        # Validate the email content
        validation_result = guard.validate(email_body)
        
        return {
            "is_valid": True,
            "message": "All product features mentioned in the email are present in the official Product_Features list",
            "details": {
                "validation_passed": True,
                "product_name": product_name,
                "official_features": product_data["Product_Features"],
                "validation_method": "product_feature_validator"
            }
        }
    except Exception as e:
        return {
            "is_valid": False,
            "message": str(e),
            "details": {
                "validation_passed": False,
                "validation_method": "product_feature_validator"
            }
        }


# Test the email feature validator with sample emails
if __name__ == "__main__":
    # Test 1: Email with valid features (PinnaclePlus program)
    valid_features_email = """
    Hi there,

    I'm excited to tell you about our GenAI Pinnacle Plus Program that offers some amazing features:

    - 300+ Hours of Immersive Learning to master Generative AI
    - 1:1 Mentorship with Generative AI experts who will guide you
    - Advanced Curriculum with 200+ Hours of Learning
    - 50+ Industry-Aligned Projects to build your portfolio
    - 100% Placement Assistance to help you land your dream job

    With these features, you'll be well-equipped to excel in the AI industry.

    Best Regards,
    Team Analytics Vidhya
    """
    
    # Test 2: Email with invalid features (features not in the list)
    invalid_features_email = """
    Hi there,

    I'm excited to tell you about our GenAI Pinnacle Plus Program that offers some amazing features:

    - 300+ Hours of Immersive Learning to master Generative AI
    - 1:1 Mentorship with Generative AI experts who will guide you
    - Free laptop for all participants
    - Guaranteed job placement with 150K+ salary
    - International internship opportunities
    - 24/7 support from teaching assistants

    With these features, you'll be well-equipped to excel in the AI industry.

    Best Regards,
    Team Analytics Vidhya
    """
    
    # Test 3: Email with mixed features (some valid, some invalid)
    mixed_features_email = """
    Hi there,

    I'm excited to tell you about our GenAI Pinnacle Plus Program that offers some amazing features:

    - 300+ Hours of Immersive Learning to master Generative AI
    - 1:1 Mentorship with Generative AI experts who will guide you
    - Advanced Curriculum with 200+ Hours of Learning
    - Free laptop for all participants
    - Guaranteed job placement with 150K+ salary
    - 50+ Industry-Aligned Projects to build your portfolio

    With these features, you'll be well-equipped to excel in the AI industry.

    Best Regards,
    Team Analytics Vidhya
    """
    
    # Run tests
    print("\n=== Testing Email with Valid Features ===")
    valid_result = validate_email_features(
        valid_features_email, 
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(valid_result, indent=2))
    
    print("\n=== Testing Email with Invalid Features ===")
    invalid_result = validate_email_features(
        invalid_features_email, 
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(invalid_result, indent=2))
    
    print("\n=== Testing Email with Mixed Features ===")
    mixed_result = validate_email_features(
        mixed_features_email, 
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(mixed_result, indent=2))
