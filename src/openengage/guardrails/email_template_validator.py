import os
import json
import re
from typing import Dict, List, Optional, Any, Tu<PERSON>
from dotenv import load_dotenv
from openai import OpenAI
from guardrails import Guard, OnFailAction
from guardrails.validator_base import (
    ValidationResult,
    Validator,
    register_validator,
    FailResult,
    PassResult
)
from pydantic import BaseModel

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Global variables to store loaded data
PRODUCT_DATA = None
COMMUNICATION_SETTINGS = None

# Function to load product details on demand
def load_product_data():
    global PRODUCT_DATA
    if PRODUCT_DATA is None:
        try:
            with open('data/product_details.json', 'r') as f:
                PRODUCT_DATA = json.load(f)
        except FileNotFoundError:
            PRODUCT_DATA = []
    return PRODUCT_DATA

# Function to load communication settings on demand
def load_communication_settings():
    global COMMUNICATION_SETTINGS
    if COMMUNICATION_SETTINGS is None:
        try:
            with open('data/communication_settings.json', 'r') as f:
                settings = json.load(f)
                # Handle both list and dictionary formats
                if isinstance(settings, list):
                    COMMUNICATION_SETTINGS = settings
                else:
                    # Convert single object to list for backward compatibility
                    COMMUNICATION_SETTINGS = [settings]
        except FileNotFoundError:
            COMMUNICATION_SETTINGS = [{}]
    return COMMUNICATION_SETTINGS

# Define a custom validator for template matching
@register_validator(name="template_match_validator", data_type="string")
class TemplateMatchValidator(Validator):
    """
    A custom validator that checks if the email content matches the template specifications.
    Validates sender_name, product_url, and ensures no other brands are promoted.
    """
    def __init__(
        self,
        product_name: str,
        **kwargs
    ):
        self.product_name = product_name
        self.product_data = self._get_product_data()
        super().__init__(**kwargs)

    def _get_product_data(self) -> Dict:
        """Retrieve the template data based product_name"""

        product_data = load_product_data()
        for product in product_data:
            if product["Product_Name"] == self.product_name:
                return product

    def validate(
        self, value: str, metadata: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        try:
            print(f"Validating email against product: {self.product_name}")

            # Extract expected values from template
            # Get the first communication settings entry or use default
            comm_settings = COMMUNICATION_SETTINGS[0] if COMMUNICATION_SETTINGS and len(COMMUNICATION_SETTINGS) > 0 else {"sender_name": "OpenEngage Team"}
            expected_sender = comm_settings.get("sender_name", "OpenEngage Team")
            expected_product_url = self.product_data.get("Product_URL", "")
            expected_product_name = self.product_data.get("Product_Name", "")
            expected_company = self.product_data.get("Company_Name", "")

            # Use OpenAI to analyze the content
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"""You are an email validator assistant. Analyze the provided email content and extract the following information:
                    1. The sender name mentioned in the email
                    2. Any URLs/links mentioned in the email
                    3. Any brand names or company names mentioned in the email
                    4. Any product names mentioned in the email

                    Then determine:
                    - Does the sender name match '{expected_sender}'?
                    - Does the email contain the product URL '{expected_product_url}'?
                    - Does the email focus primarily on '{expected_product_name}' by '{expected_company}' or does it mention other competing brands/products?

                    Respond with a JSON object containing:
                    - "sender_name": The extracted sender name
                    - "urls": List of all URLs found
                    - "brands": List of all brand/company names found
                    - "products": List of all product names found
                    - "sender_match": Boolean indicating if sender matches expected
                    - "url_match": Boolean indicating if the expected URL is present
                    - "other_brands_mentioned": Boolean indicating if other brands besides '{expected_company}' are prominently featured
                    - "other_brand_urls": List of URLs that don't belong to '{expected_company}'
                    - "validation_issues": List of any validation issues found
                    """},
                    {"role": "user", "content": f"Analyze this email content: {value}"}
                ],
                temperature=0.1
            )

            # Extract the response
            analysis_text = response.choices[0].message.content

            # Parse the JSON response
            try:
                # Try to extract JSON from the response
                if '{' in analysis_text and '}' in analysis_text:
                    json_str = analysis_text[analysis_text.find('{'):analysis_text.rfind('}')+1]
                    analysis = json.loads(json_str)
                else:
                    # Fallback if no JSON structure is found
                    return FailResult(
                        error_message="Failed to parse validation results",
                    )
            except json.JSONDecodeError:
                return FailResult(
                    error_message="Failed to parse validation results",
                )

            # Check validation results
            validation_issues = analysis.get("validation_issues", [])

            # Check sender name match
            if not analysis.get("sender_match", False):
                validation_issues.append(f"Sender name doesn't match expected '{expected_sender}'")

            # Check product URL match
            if not analysis.get("url_match", False):
                validation_issues.append(f"Email doesn't contain the expected product URL '{expected_product_url}'")

            # Check for other brands
            if analysis.get("other_brands_mentioned", False):
                validation_issues.append(f"Email mentions other brands besides '{expected_company}'")

            # Check for other brand URLs
            other_brand_urls = analysis.get("other_brand_urls", [])
            if other_brand_urls:
                validation_issues.append(f"Email contains URLs from other brands: {', '.join(other_brand_urls)}")

            # Return validation result
            if validation_issues:
                return FailResult(
                    error_message=f"Email validation failed: {'; '.join(validation_issues)}",
                )

            return PassResult()

        except Exception as e:
            print(f"Error during template validation: {str(e)}")
            return FailResult(
                error_message=f"Error during validation: {str(e)}",
            )


# Create a Guard with the template match validator
def create_email_template_guard( product_name: str) -> Guard:
    """Create and return a Guard for email template validation"""
    guard = Guard().use(
        TemplateMatchValidator(
            product_name=product_name,
            on_fail=OnFailAction.EXCEPTION
        )
    )
    return guard


# Function to validate email against template
def validate_email_against_template(
    email_body: str,
    product_name: str
) -> Dict[str, Any]:
    """
    Validate if an email body matches the specified template.

    Args:
        email_body: The content of the email to validate
        product_name: The name of the template to validate against

    Returns:
        Dictionary with validation information
    """
    try:
        # Create guard for template validation
        guard = create_email_template_guard( product_name)

        # Get template data for reference
        product_data = None
        for product in PRODUCT_DATA:
            if product["Product_Name"] == product_name:
                product_data = product
                break

        if not product_data:
            return {
                "is_valid": False,
                "message": f"Product '{product_name}' not found in product data",
                "details": {
                    "validation_passed": False,
                    "validation_method": "template_match_validator"
                }
            }

        # Validate the email content
        validation_result = guard.validate(email_body)

        return {
            "is_valid": True,
            "message": "Email content matches the template specifications",
            "details": {
                "validation_passed": True,
                "product_name": product_name,
                "expected_sender": COMMUNICATION_SETTINGS[0].get("sender_name", "OpenEngage Team") if COMMUNICATION_SETTINGS and len(COMMUNICATION_SETTINGS) > 0 else "OpenEngage Team",
                "expected_product_url": product_data.get("Product_URL", ""),
                "validation_method": "template_match_validator"
            }
        }
    except Exception as e:
        return {
            "is_valid": False,
            "message": str(e),
            "details": {
                "validation_passed": False,
                "product_name": product_name,
                "validation_method": "template_match_validator"
            }
        }


# Test the email template validator with sample emails
if __name__ == "__main__":
    # Test 1: Valid email matching template
    valid_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """

    # Test 2: Invalid email with wrong sender
    invalid_sender_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team DataCamp
    """

    # Test 3: Invalid email with wrong URL
    invalid_url_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.coursera.org/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """

    # Test 4: Invalid email mentioning other brands
    invalid_other_brands_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Our program is much better than similar offerings from Coursera and Udacity. Unlike DataCamp's basic courses, we provide comprehensive training.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """

    # Run tests
    print("\n=== Testing Valid Email ===")
    valid_result = validate_email_against_template(
        valid_email,
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(valid_result, indent=2))

    print("\n=== Testing Email with Wrong Sender ===")
    invalid_sender_result = validate_email_against_template(
        invalid_sender_email,
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(invalid_sender_result, indent=2))

    print("\n=== Testing Email with Wrong URL ===")
    invalid_url_result = validate_email_against_template(
        invalid_url_email,
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(invalid_url_result, indent=2))

    print("\n=== Testing Email Mentioning Other Brands ===")
    invalid_brands_result = validate_email_against_template(
        invalid_other_brands_email,
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(invalid_brands_result, indent=2))
