import os
import json
import re
from typing import Dict, List, Optional, Any, Set, Tuple
from dotenv import load_dotenv
from openai import OpenAI
from guardrails import Guard, OnFailAction
from guardrails.validator_base import (
    ValidationResult,
    Validator,
    register_validator,
    FailResult,
    PassResult
)
from pydantic import BaseModel

# Import all validators and functions from individual scripts
from email_template_validator import (
    TemplateMatchValidator,
    create_email_template_guard,
    validate_email_against_template
)

from email_safety_checker import (
    NSFWContentDetector,
    create_email_safety_guard,
    check_email_safety
)

from email_feature_validator import (
    ProductFeatureValidator,
    create_feature_validation_guard,
    validate_email_features
)

# Commented out as requested
# from email_offer_validator import (
#     OfferValidator,
#     create_offer_validation_guard,
#     validate_email_offers
# )

# from email_goal_validator import (
#     StageGoalValidator,
#     ObjectiveValidator,
#     create_email_goal_guard,
#     validate_email_goals
# )

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Comprehensive email validation function
def validate_email(
    email_body: str,
    product_name: str,
    run_safety_check: bool = True,
    run_template_check: bool = True,
    run_feature_check: bool = True,
    run_offer_check: bool = False,  # Kept for backward compatibility but not used
    run_goal_check: bool = False
) -> Dict[str, Any]:
    """
    Comprehensive email validation using all available validators.

    Args:
        email_body: The content of the email to validate
        stage: The user stage (e.g., "New", "Product Viewed")
        template_name: The name of the template to validate against
        run_safety_check: Whether to run the safety check
        run_template_check: Whether to run the template check
        run_feature_check: Whether to run the feature check
        run_offer_check: Deprecated - kept for backward compatibility but not used
        run_goal_check: Whether to run the goal check (now uses user_journey.json)

    Returns:
        Dictionary with comprehensive validation information
    """
    results = {
        "overall_valid": True,
        "validation_results": {},
        "failed_validations": []
    }

    # Run safety check
    if run_safety_check:
        safety_result = check_email_safety(email_body)
        results["validation_results"]["safety"] = safety_result
        if not safety_result.get("is_safe", False):
            results["overall_valid"] = False
            results["failed_validations"].append("safety")

    # Run template check
    if run_template_check:
        template_result = validate_email_against_template(email_body, product_name)
        results["validation_results"]["template"] = template_result
        if not template_result.get("is_valid", False):
            results["overall_valid"] = False
            results["failed_validations"].append("template")

    # Run feature check
    if run_feature_check:
        feature_result = validate_email_features(email_body, product_name)
        results["validation_results"]["features"] = feature_result
        if not feature_result.get("is_valid", False):
            results["overall_valid"] = False
            results["failed_validations"].append("features")

    # Run goal check
    # if run_goal_check:
    #     goal_result = validate_email_goals(email_body, product_name)
    #     results["validation_results"]["goals"] = goal_result
    #     if not goal_result.get("is_valid", False):
    #         results["overall_valid"] = False
    #         results["failed_validations"].append("goals")

    # Add summary information
    if results["overall_valid"]:
        results["summary"] = "Email passed all validation checks"
    else:
        failed_checks = ", ".join(results["failed_validations"])
        results["summary"] = f"Email failed validation checks: {failed_checks}"

    return results


# Test the comprehensive email validator with sample emails
if __name__ == "__main__":
    # Test email that should pass all validations
    valid_email = """
    Hi there,

    Are you ready to step into the future of technology? The GenAI Pinnacle Plus Program is designed to help you master Generative AI and Agentic AI with an advanced curriculum that focuses on real-world applications.

    This program offers 300+ hours of immersive learning, including 200+ hours of advanced curriculum tailored for in-depth understanding. You will have the opportunity to work on 50+ industry-aligned projects, ensuring that you gain practical experience that employers value.

    With 1:1 mentorship from Generative AI experts and 75+ personalized mentorship sessions, you will receive guidance tailored to your learning journey. This ensures that you not only learn the theory but also how to apply it effectively in various scenarios.

    Embark on this journey with us and gain the confidence to excel in the AI field. Explore the GenAI Pinnacle Plus Program and see how it can transform your career: https://www.analyticsvidhya.com/pinnacleplus/

    Best Regards,
    Team Analytics Vidhya
    """

    # Test email that should fail some validations
    invalid_email = """
    Hi there,

    Check out our GenAI Pinnacle Plus Program!

    Special Offer: Enroll now and get 25% off your first month! Use coupon code PINNACLE25 at checkout.

    Visit our website: https://www.coursera.org/pinnacleplus/

    Best Regards,
    Team DataCamp
    """

    # Run comprehensive validation
    print("\n=== Testing Valid Email with All Validators ===")
    valid_result = validate_email(
        valid_email,
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(valid_result, indent=2))

    print("\n=== Testing Invalid Email with All Validators ===")
    invalid_result = validate_email(
        invalid_email,
        "GenAI Pinnacle Plus Program"
    )
    print(json.dumps(invalid_result, indent=2))

    # Test with selective validators
    print("\n=== Testing Email with Only Safety and Template Validators ===")
    selective_result = validate_email(
        valid_email,
        "GenAI Pinnacle Plus Program",
        run_feature_check=False,
        run_offer_check=False,
        run_goal_check=False
    )
    print(json.dumps(selective_result, indent=2))
