"""
Analytics module for OpenEngage marketing automation system.
"""
import pandas as pd
import altair as alt
import plotly.express as px
import plotly.graph_objects as go
import plotly.subplots as sp
from datetime import datetime
import os
import logging
from pathlib import Path
from config.pruning_config import get_pruning_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Brand colors
PRIMARY = '#8D06FE'
SECONDARY = '#02B9C9'
ACCENT = '#02C688'
TERTIARY = '#F39C12'
SUCCESS = '#27AE60'
WARNING = '#F39C12'
DANGER = '#E74C3C'

def process_engagement_data(df):
    """Process the uploaded CSV data for engagement metrics."""
    # Identify which time columns are available in the data
    available_time_columns = []

    # Map standard column names to possible alternatives
    column_mapping = {
        'Send_Time': ['Send_Time', 'Last_Send_Time'],
        'Open_Time': ['Open_Time', 'Last_Open_Time'],
        'Click_Time': ['Click_Time', 'Last_Click_Time']
    }

    # Create a mapping of actual column names in the dataframe
    actual_columns = {}
    for standard_col, alternatives in column_mapping.items():
        for alt_col in alternatives:
            if alt_col in df.columns:
                actual_columns[standard_col] = alt_col
                available_time_columns.append(alt_col)
                break

    # Convert available time columns to datetime
    for col in available_time_columns:
        df[col] = pd.to_datetime(df[col], errors='coerce')

    # Ensure we have a Send_Time column to calculate date
    if 'Send_Time' in actual_columns:
        send_time_col = actual_columns['Send_Time']
        df['date'] = df[send_time_col].dt.date

        # Calculate daily metrics
        total_sent = df.groupby('date')['user_email'].count()

        # Calculate opens if we have an open time column
        if 'Open_Time' in actual_columns:
            open_time_col = actual_columns['Open_Time']
            total_opened = df.groupby('date')[open_time_col].count()
        else:
            # If no open time column, use zeros
            total_opened = pd.Series(0, index=total_sent.index)

        # Calculate clicks if we have a click time column
        if 'Click_Time' in actual_columns:
            click_time_col = actual_columns['Click_Time']
            total_clicked = df.groupby('date')[click_time_col].count()
        else:
            # If no click time column, use zeros
            total_clicked = pd.Series(0, index=total_sent.index)

        metrics_df = pd.DataFrame({
            'total_sent': total_sent,
            'total_opened': total_opened,
            'total_clicked': total_clicked
        }).reset_index()

        # Calculate rates
        metrics_df['open_rate'] = (metrics_df['total_opened'] / metrics_df['total_sent']) * 100
        metrics_df['click_rate'] = (metrics_df['total_clicked'] / metrics_df['total_sent']) * 100

        return metrics_df
    else:
        # If we don't have a send time column, create a minimal metrics dataframe
        # with the current date and zeros for all metrics
        import datetime
        current_date = datetime.datetime.now().date()
        metrics_df = pd.DataFrame({
            'date': [current_date],
            'total_sent': [0],
            'total_opened': [0],
            'total_clicked': [0],
            'open_rate': [0],
            'click_rate': [0]
        })

        return metrics_df

def create_engagement_plot(metrics_df, time_unit='D', metric_type='rates'):
    """Create an interactive plot using Altair."""
    # Convert date to datetime if it's not already
    metrics_df['date'] = pd.to_datetime(metrics_df['date'])

    # Resample based on time unit
    if time_unit == 'W':
        resampled_df = metrics_df.set_index('date').resample('W').sum().reset_index()
    elif time_unit == 'M':
        resampled_df = metrics_df.set_index('date').resample('M').sum().reset_index()
    else:  # Daily
        resampled_df = metrics_df.copy()

    # Recalculate rates after resampling
    resampled_df['open_rate'] = (resampled_df['total_opened'] / resampled_df['total_sent']) * 100
    resampled_df['click_rate'] = (resampled_df['total_clicked'] / resampled_df['total_sent']) * 100 * 100

    # Sort by date
    resampled_df = resampled_df.sort_values('date')

    if metric_type == 'rates':
        # Create base chart
        base = alt.Chart(resampled_df).encode(
            x=alt.X('date:T', title='Date'),
            y=alt.Y('rate:Q', title='Rate (%)', scale=alt.Scale(domain=[0, 100]))
        ).properties(
            width='container',
            height=400
        )

        # Open rate chart
        open_rate_chart = base.mark_line(
            point=True,
            color=PRIMARY,
            strokeWidth=2
        ).transform_calculate(
            rate='datum.open_rate'
        ).encode(
            y=alt.Y('open_rate:Q', title='Rate (%)'),
            tooltip=[
                alt.Tooltip('date:T', title='Date'),
                alt.Tooltip('open_rate:Q', title='Open Rate', format='.1f')
            ]
        ).transform_calculate(
            legend="'Open Rate'"
        )

        # Click rate chart
        click_rate_chart = base.mark_line(
            point=True,
            color=SECONDARY,
            strokeWidth=2,
            strokeDash=[6, 4],
            opacity=0.7
        ).transform_calculate(
            rate='datum.click_rate'
        ).encode(
            y=alt.Y('click_rate:Q', title='Rate (%)'),
            tooltip=[
                alt.Tooltip('date:T', title='Date'),
                alt.Tooltip('click_rate:Q', title='Click Rate', format='.1f')
            ]
        ).transform_calculate(
            legend="'Click Rate'"
        )

        # Create a selection for the legend
        selection = alt.selection_point(fields=['legend'], bind='legend')

        # Add legend to charts
        open_rate_with_legend = open_rate_chart.encode(
            opacity=alt.condition(selection, alt.value(1), alt.value(0.2))
        ).add_params(selection)

        click_rate_with_legend = click_rate_chart.encode(
            opacity=alt.condition(selection, alt.value(0.7), alt.value(0.2))
        ).add_params(selection)

        # Combine charts with legend
        chart = alt.layer(open_rate_with_legend, click_rate_with_legend).resolve_scale(
            y='shared'
        ).properties(
            title={
                'text': 'Email Engagement Metrics',
                'subtitle': 'Open Rate and Click Rate over time'
            }
        ).encode(
            color=alt.Color('legend:N', legend=alt.Legend(
                title='Metrics',
                orient='top-right',
                fillColor='white',
                strokeColor='lightgray',
                padding=10
            ))
        ).interactive()

    else:
        # Map metric types to columns and titles
        metric_map = {
            'sent': ('total_sent', 'Emails Sent', PRIMARY),
            'opens': ('total_opened', 'Email Opens', SECONDARY),
            'clicks': ('total_clicked', 'Email Clicks', ACCENT)
        }

        column, title, color = metric_map[metric_type]

        # Create the chart
        chart = alt.Chart(resampled_df).mark_line(
            point=True
        ).encode(
            x=alt.X('date:T', title='Date'),
            y=alt.Y(f'{column}:Q', title='Count'),
            color=alt.value(color),
            tooltip=[
                alt.Tooltip('date:T', title='Date'),
                alt.Tooltip(f'{column}:Q', title=title, format=',')
            ]
        ).properties(
            title=title,
            width='container',
            height=400
        ).interactive()

    return chart

def filter_data_by_date(df, start_date=None, end_date=None):
    """Filter data based on date range."""
    if start_date:
        df = df[df['date'] >= pd.to_datetime(start_date).date()]
    if end_date:
        df = df[df['date'] <= pd.to_datetime(end_date).date()]
    return df

def process_user_journey_data(df, product_name=None, journey_stages=None):
    """Process user journey data from the uploaded CSV.

    Args:
        df (pd.DataFrame): DataFrame containing user journey data with columns:
            user_email, Send_Time/Last_Send_Time, Open_Time/Last_Open_Time, Click_Time/Last_Click_Time, user_stage
        product_name (str, optional): Filter data by product name. Defaults to None.
        journey_stages (list, optional): List of all journey stages in order. Defaults to None.

    Returns:
        pd.DataFrame: Processed DataFrame with user stage counts and percentages
    """
    # Filter by product if specified and if the column exists
    if product_name:
        # Check all possible product column names
        product_columns = ['offering', 'product', 'Matched_Product', 'last_product_sent', 'Last_Product_Sent']
        filtered = False

        for col in product_columns:
            if col in df.columns:
                filtered_df = df[df[col] == product_name]
                if not filtered_df.empty:
                    filtered = True
                    break

        if not filtered:
            print(f"Warning: No data found for product '{product_name}'. Using all data.")
            filtered_df = df
    else:
        filtered_df = df

    # Count users in each stage if user_stage column exists
    if 'user_stage' in filtered_df.columns:
        stage_counts = filtered_df['user_stage'].value_counts().reset_index()
        stage_counts.columns = ['stage', 'count']
    else:
        # If no user_stage column, create a basic stage count with default stages
        if journey_stages is None:
            journey_stages = ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']

        # Create empty DataFrame with stage column
        stage_counts = pd.DataFrame({'stage': journey_stages, 'count': 0})

    # Calculate percentages
    total_users = stage_counts['count'].sum()
    stage_counts['percentage'] = (stage_counts['count'] / total_users * 100).round(1) if total_users > 0 else 0

    # If journey_stages is provided, ensure all stages are included in the result
    if journey_stages:
        # Create a set of existing stages for quick lookup
        existing_stages = set(stage_counts['stage'])

        # Add missing stages with zero counts
        for stage in journey_stages:
            if stage not in existing_stages:
                new_row = pd.DataFrame({
                    'stage': [stage],
                    'count': [0],
                    'percentage': [0.0]
                })
                stage_counts = pd.concat([stage_counts, new_row], ignore_index=True)

        # Sort the DataFrame according to the journey stages order
        stage_order = {stage: i for i, stage in enumerate(journey_stages)}
        stage_counts['order'] = stage_counts['stage'].map(stage_order)
        stage_counts = stage_counts.sort_values('order').drop('order', axis=1).reset_index(drop=True)

    return stage_counts


def create_user_journey_bar_chart(stage_counts):
    """Create a bar chart visualization of user journey stages using Plotly.

    Args:
        stage_counts (pd.DataFrame): DataFrame with columns 'stage', 'count', and 'percentage'

    Returns:
        plotly.graph_objects.Figure: Interactive bar chart
    """
    # Create bar chart
    fig = px.bar(
        stage_counts,
        x='stage',
        y='count',
        text='percentage',
        color='count',
        color_continuous_scale=px.colors.sequential.Viridis,
        labels={'stage': 'User Journey Stage', 'count': 'Number of Users', 'percentage': 'Percentage'},
        title='User Journey Stage Distribution'
    )

    # Customize layout
    fig.update_traces(
        texttemplate='%{text}%',
        textposition='outside',
        hovertemplate='<b>%{x}</b><br>Users: %{y}<br>Percentage: %{text}%'
    )

    fig.update_layout(
        xaxis_title='User Journey Stage',
        yaxis_title='Number of Users',
        coloraxis_showscale=False,
        hoverlabel=dict(bgcolor='white'),
        margin=dict(t=50, b=50, l=50, r=50)
    )

    return fig


def create_user_journey_funnel_chart(stage_counts, journey_stages, product_name=None):
    """Create an enhanced funnel chart visualization of user journey stages using Plotly.

    Args:
        stage_counts (pd.DataFrame): DataFrame with columns 'stage', 'count', and 'percentage'
        journey_stages (list): List of stages in the correct journey order
        product_name (str, optional): Name of the product for the title. Defaults to None.

    Returns:
        plotly.graph_objects.Figure: Interactive funnel chart with cumulative values
    """
    # Create a mapping of stages to their counts
    stage_to_count = dict(zip(stage_counts['stage'], stage_counts['count']))

    # Order the stages according to the journey flow
    ordered_stages = []
    ordered_counts = []
    original_counts = []

    # Use all journey stages, even if they don't have data
    for stage in journey_stages:
        ordered_stages.append(stage)
        # Use the count if available, otherwise 0
        count = stage_to_count.get(stage, 0)
        original_counts.append(count)

    # If no stages match, use the stages from stage_counts in their original order
    if not ordered_stages and not stage_counts.empty:
        ordered_stages = stage_counts['stage'].tolist()
        original_counts = stage_counts['count'].tolist()

    # Calculate cumulative counts (each stage includes sum of itself and all subsequent stages)
    # For a funnel where x1 < x2 < x3 < x4, we want:
    # Stage 1: x1 + x2 + x3 + x4
    # Stage 2: x2 + x3 + x4
    # Stage 3: x3 + x4
    # Stage 4: x4
    cumulative_counts = []
    for i in range(len(original_counts)):
        cumulative_counts.append(sum(original_counts[i:]))

    ordered_counts = cumulative_counts

    # Calculate conversion rates between stages based on cumulative counts
    # For a funnel where cumulative counts are:
    # Stage 1: x1 + x2 + x3 + x4
    # Stage 2: x2 + x3 + x4
    # Stage 3: x3 + x4
    # Stage 4: x4
    # The conversion rates will be:
    # Stage 1 to 2: (x2+x3+x4)/(x1+x2+x3+x4)
    # Stage 2 to 3: (x3+x4)/(x2+x3+x4)
    # Stage 3 to 4: x4/(x3+x4)
    conversion_rates = []
    for i in range(len(cumulative_counts) - 1):
        if cumulative_counts[i] > 0:
            # Calculate conversion rate using cumulative counts
            rate = (cumulative_counts[i+1] / cumulative_counts[i]) * 100
            conversion_rates.append(f"{rate:.1f}%")
        else:
            conversion_rates.append("N/A")

    # Create custom hover text
    hover_text = []
    for i, (stage, cum_count, orig_count) in enumerate(zip(ordered_stages, cumulative_counts, original_counts)):
        text = f"<b>{stage}</b><br>"
        text += f"Individual Count: {orig_count}<br>"
        text += f"Cumulative Count: {cum_count}<br>"

        # Add conversion rate explanation
        if i < len(ordered_stages) - 1:
            next_cum = cumulative_counts[i+1]
            text += f"Conversion to next stage: {conversion_rates[i]}<br>"
            text += f"({next_cum}/{cum_count})"
        hover_text.append(text)

    # Create enhanced funnel chart
    fig = go.Figure()

    # Add funnel chart with cumulative values
    fig.add_trace(go.Funnel(
        name="User Journey",
        y=ordered_stages,
        x=ordered_counts,
        textposition="inside",
        textinfo="value",  # Only show the value, not the percentage
        opacity=0.8,
        hovertext=hover_text,
        hoverinfo="text",
        marker={
            "color": [
                PRIMARY,
                SECONDARY,
                ACCENT,
                TERTIARY,
                "#3498DB",
                "#9B59B6",
                "#1ABC9C",
                "#F1C40F"
            ][:len(ordered_stages)],
            "line": {"width": 2, "color": "white"}
        },
        connector={
            "line": {
                "color": "royalblue",
                "dash": "solid",
                "width": 3
            }
        }
        # Keep vertical orientation for better readability
    ))

    # Add conversion rate annotations between stages
    annotations = []

    # Create a list of stage positions for annotations
    stage_positions = []
    for i in range(len(ordered_stages)):
        # Calculate the y-position for each stage
        stage_positions.append(i / (len(ordered_stages) - 1) if len(ordered_stages) > 1 else 0.5)

    # Reverse the order of annotations to flip the percentages
    for i in range(len(ordered_stages) - 1):
        # Get the actual data for the current stage
        next_cum = cumulative_counts[i+1]
        curr_cum = cumulative_counts[i]

        # Calculate the conversion rate
        rate_text = conversion_rates[i]

        # Get the position for this annotation (between stages)
        # Use the reversed index to flip the order
        reversed_i = len(ordered_stages) - 2 - i

        # Position between the stages in the reversed order
        y_pos = (stage_positions[reversed_i] + stage_positions[reversed_i + 1]) / 2

        # Create annotation with conversion rate and formula
        annotations.append(dict(
            x=1.05,
            y=y_pos,
            xref='paper',
            yref='paper',
            text=f"→ {rate_text} →<br>({next_cum}/{curr_cum})",
            showarrow=False,
            font=dict(size=11, color="royalblue"),
            align="center"
        ))

    # Set title based on product name
    title = "User Journey Funnel"
    if product_name:
        title = f"{product_name} - User Journey Funnel"

    # Enhanced layout
    fig.update_layout(
        title={
            'text': title,
            'y': 0.95,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20, 'color': PRIMARY}
        },
        margin=dict(t=80, b=50, l=120, r=120),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        hoverlabel=dict(
            bgcolor="white",
            font_size=14,
            font_family="Arial"
        ),
        annotations=annotations
    )

    return fig


def create_user_journey_pie_chart(stage_counts):
    """Create a pie chart visualization of user journey stages using Plotly.

    Args:
        stage_counts (pd.DataFrame): DataFrame with columns 'stage', 'count', and 'percentage'

    Returns:
        plotly.graph_objects.Figure: Interactive pie chart
    """
    # Create pie chart
    fig = px.pie(
        stage_counts,
        values='count',
        names='stage',
        title='User Journey Stage Distribution',
        color_discrete_sequence=px.colors.qualitative.Bold,
        hover_data=['percentage']
    )

    # Customize layout
    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate='<b>%{label}</b><br>Users: %{value}<br>Percentage: %{customdata[0]}%'
    )

def create_campaign_prediction_visualization(prediction_data):
    """Create a visualization for campaign performance predictions.

    Args:
        prediction_data (dict): Dictionary containing prediction results

    Returns:
        plotly.graph_objects.Figure: Interactive visualization of predictions
    """
    # Extract prediction values
    predictions = prediction_data.get('predictions', {})
    training_metrics = prediction_data.get('training_metrics', {})
    campaign_name = prediction_data.get('campaign_name', 'Unknown Campaign')
    campaign_date = prediction_data.get('campaign_date', 'Unknown Date')
    campaign_size = prediction_data.get('campaign_size', 0)

    # Create figure with subplots
    fig = sp.make_subplots(
        rows=1,
        cols=3,
        subplot_titles=("Open Rate", "Click Rate", "Unsubscribe Rate"),
        specs=[[{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}]]
    )

    # Define metric display names and thresholds
    metrics = {
        'open_rate': {
            'name': 'Open Rate',
            'thresholds': {'low': 15, 'high': 25},
            'format': '.1f',
            'suffix': '%'
        },
        'click_rate': {
            'name': 'Click Rate',
            'thresholds': {'low': 2, 'high': 5},
            'format': '.1f',
            'suffix': '%'
        },
        'unsub_rate': {
            'name': 'Unsubscribe Rate',
            'thresholds': {'low': 0.1, 'high': 0.5},
            'format': '.2f',
            'suffix': '%'
        }
    }

    # Add indicators for each metric
    for i, (metric_key, metric_info) in enumerate(metrics.items(), 1):
        # Get prediction value
        value = predictions.get(metric_key, 0)

        # Determine color based on thresholds
        if metric_key == 'unsub_rate':
            # For unsubscribe rate, lower is better
            color = SUCCESS if value < metric_info['thresholds']['low'] else \
                   WARNING if value < metric_info['thresholds']['high'] else DANGER
        else:
            # For open and click rates, higher is better
            color = DANGER if value < metric_info['thresholds']['low'] else \
                   WARNING if value < metric_info['thresholds']['high'] else SUCCESS

        # Get baseline from training metrics if available
        baseline = None
        if metric_key in training_metrics:
            baseline = training_metrics[metric_key].get('baseline', None)
            if baseline is not None:
                baseline *= 100  # Convert to percentage

        # Add indicator
        fig.add_trace(
            go.Indicator(
                mode="gauge+number+delta",
                value=value,
                number={
                    'suffix': metric_info['suffix'],
                    'font': {'size': 24}
                },
                delta={
                    'reference': baseline,
                    'increasing': {'color': SUCCESS} if metric_key != 'unsub_rate' else {'color': DANGER},
                    'decreasing': {'color': DANGER} if metric_key != 'unsub_rate' else {'color': SUCCESS}
                },
                gauge={
                    'axis': {
                        'range': [None,
                                 metric_info['thresholds']['high'] * 2 if metric_key != 'unsub_rate'
                                 else metric_info['thresholds']['high'] * 4]
                    },
                    'bar': {'color': color},
                    'steps': [
                        {'range': [0, metric_info['thresholds']['low']],
                         'color': DANGER if metric_key != 'unsub_rate' else SUCCESS},
                        {'range': [metric_info['thresholds']['low'], metric_info['thresholds']['high']],
                         'color': WARNING},
                        {'range': [metric_info['thresholds']['high'],
                                  metric_info['thresholds']['high'] * 2 if metric_key != 'unsub_rate'
                                  else metric_info['thresholds']['high'] * 4],
                         'color': SUCCESS if metric_key != 'unsub_rate' else DANGER}
                    ],
                    'threshold': {
                        'line': {'color': 'black', 'width': 2},
                        'thickness': 0.75,
                        'value': baseline if baseline is not None else 0
                    }
                }
            ),
            row=1, col=i
        )

    # Update layout
    fig.update_layout(
        title={
            'text': f"Campaign Outcome Prediction<br><sup>{campaign_name} ({campaign_date}) - {campaign_size:,} recipients</sup>",
            'y': 0.95,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20, 'color': PRIMARY}
        },
        height=400,
        margin=dict(t=120, b=40, l=40, r=40),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        showlegend=False
    )

    return fig


def analyze_template_performance(performance_data):
    """
    Analyze template performance from the combined performance data and merge with template files.
    
    Args:
        performance_data (pd.DataFrame): DataFrame containing email performance data
        
    Returns:
        tuple: (pd.DataFrame with template performance metrics, dict with threshold values per stage)
    """
    # Dictionary to store threshold values for each stage
    threshold_values = {}
    
    # Add debug statement for tracing
    print("DEBUG: analyze_template_performance called")
    # Configure logging if not already configured
    logger = logging.getLogger(__name__)
    
    if performance_data.empty:
        logger.warning("No performance data provided for template analysis")
        return pd.DataFrame(), threshold_values
    
    # Check if Template_Name column exists, if not try to derive it
    if 'Template_Name' not in performance_data.columns:
        # Try to derive Template_Name from other columns
        if 'Subject' in performance_data.columns and 'HTML_Content' in performance_data.columns:
            # Use a combination of subject and first few chars of HTML content as template identifier
            performance_data['Template_Name'] = performance_data['Subject'].astype(str)
        elif 'Subject' in performance_data.columns:
            # Use subject as template identifier
            performance_data['Template_Name'] = performance_data['Subject'].astype(str)
        elif 'Mail_Content' in performance_data.columns:
            # Try to use first line of mail content as template name
            performance_data['Mail_Content'].fillna("", inplace=True)
            performance_data['Template_Name'] = performance_data['Mail_Content'].astype(str).str.split('\n').str[0]
        else:
            logger.warning("Could not derive Template_Name from available columns")
            return pd.DataFrame(), threshold_values
    
    # Calculate performance metrics per template
    template_performance = []
    
    # Extract stage from user_stage column if available
    stage_extraction = False
    if 'user_stage' in performance_data.columns:
        stage_extraction = True
    
    for template_name, group in performance_data.groupby('Template_Name'):
        # Skip empty template names
        if pd.isna(template_name) or template_name == "":
            continue
            
        total_sent = len(group)
        
        # Check if required columns exist
        open_count = 0
        if 'Open_Time' in group.columns:
            open_count = group['Open_Time'].notna().sum()
            
        click_count = 0
        if 'Click_Time' in group.columns:
            click_count = group['Click_Time'].notna().sum()
            
        unsub_count = 0
        if 'Unsubscribe_Time' in group.columns:
            unsub_count = group['Unsubscribe_Time'].notna().sum()
        
        # Calculate rates
        open_rate = (open_count / total_sent) * 100 if total_sent > 0 else 0
        click_rate = (click_count / total_sent) * 100 if total_sent > 0 else 0
        click_to_open_rate = (click_count / open_count) * 100 if open_count > 0 else 0
        unsub_rate = (unsub_count / total_sent) * 100 if total_sent > 0 else 0
        
        # Find most common product associated with this template
        product = None
        if 'Matched_Product' in group.columns:
            product = group['Matched_Product'].mode().iloc[0] if not group['Matched_Product'].mode().empty else None
        elif 'last_product_sent' in group.columns:
            product = group['last_product_sent'].mode().iloc[0] if not group['last_product_sent'].mode().empty else None
        elif 'Last_Product_Sent' in group.columns:
            product = group['Last_Product_Sent'].mode().iloc[0] if not group['Last_Product_Sent'].mode().empty else None
        else:
            product = None
        
        # Extract stage information if available
        stage = None
        if stage_extraction:
            # Get the most common stage for this template
            raw_stage = group['user_stage'].mode().iloc[0] if not group['user_stage'].mode().empty else "unknown"
            
            # Transform the stage name to match template file naming convention (lowercase with spaces as underscores)
            stage = raw_stage.lower().replace(' ', '_') if isinstance(raw_stage, str) else "unknown"
        else:
            # Try to extract stage from filename pattern (some templates might be named like "abandoned_cart_reminder")
            parts = template_name.lower().split('_')
            if len(parts) >= 2:
                stage = "_".join(parts[0:2])  # Use first two parts as stage identifier
            else:
                stage = "unknown"
            
        # Add template performance data
        template_performance.append({
            'Template_Name': template_name,
            'Stage': stage,
            'Associated_Product': product,
            'Total_Sent': total_sent,
            'Open_Count': open_count,
            'Click_Count': click_count,
            'Unsubscribe_Count': unsub_count,
            'Open_Rate': open_rate,
            'Click_Rate': click_rate,
            'Click_to_Open_Rate': click_to_open_rate,
            'Unsubscribe_Rate': unsub_rate,
            'Pruning_Flag': 0  # Initialize all templates with Pruning Flag = 0
        })
    
    # Create DataFrame from list of dictionaries
    template_df = pd.DataFrame(template_performance)
    
    # Sort by total sent (descending)
    if not template_df.empty:
        template_df = template_df.sort_values('Total_Sent', ascending=False)
        
        # Assign pruning flags to templates
        total_pruned = 0
        # Dictionary to store threshold values per stage and product
        threshold_values = {}
        
        if not template_df.empty:
            # Get unique combinations of stage and product
            stage_product_groups = template_df.groupby(['Stage', 'Associated_Product'])
            stage_products = list(stage_product_groups.groups.keys())
            
            # Apply pruning logic per stage and product
            logger.info(f"Applying stage-and-product-based pruning logic to {len(stage_products)} stage-product combinations")
            
            for stage, product in stage_products:
                # Get templates for this stage and product combination
                stage_product_templates = template_df[(template_df['Stage'] == stage) & 
                                                     (template_df['Associated_Product'] == product)].copy()
                
                # Load pruning configuration
                pruning_config = get_pruning_config()
                min_template_count = pruning_config['min_template_count']
                
                # Only apply if we have enough templates in this stage-product combination
                if len(stage_product_templates) >= min_template_count:  # Need minimum templates to calculate meaningful percentiles
                    try:
                        # Calculate lower quartile (20th percentile) for click-to-open rate within this stage-product
                        # Only include templates that are not already pruned
                        stage_product_unpruned = stage_product_templates[stage_product_templates['Pruning_Flag'] == 0]
                        
                        # Load pruning configuration
                        pruning_config = get_pruning_config()
                        min_template_count = pruning_config['min_template_count']
                        percentile_threshold = pruning_config['percentile_threshold']
                        required_volume = pruning_config['required_volume_threshold']
                        
                        if len(stage_product_unpruned) >= min_template_count:  # At least minimum unpruned templates needed
                            lower_quartile_cto = stage_product_unpruned['Click_to_Open_Rate'].quantile(percentile_threshold, interpolation='linear')
                            
                            # Store threshold value as percentage for UI display
                            stage_product_key = f"{stage}:{product}"
                            if stage_product_key not in threshold_values:
                                threshold_values[stage_product_key] = {}
                            # The values are already percentages, so no need to multiply by 100
                            threshold_values[stage_product_key]['click_to_open_rate'] = lower_quartile_cto
                            print(f"DEBUG: Added CTO threshold for {stage}:{product}: {threshold_values[stage_product_key]['click_to_open_rate']:.2f}%")
                            
                            # Apply pruning logic based on click-to-open rate within this stage-product combination
                            # Find indices in the original DataFrame that match our condition
                            
                            # Special case: if threshold is 0, also prune templates with exactly 0 click-to-open rate
                            if lower_quartile_cto == 0:
                                print(f"DEBUG: Threshold for {stage}:{product} is 0, also pruning templates with 0 click-to-open rate")
                                indices_to_prune = template_df[
                                    (template_df['Stage'] == stage) & 
                                    (template_df['Associated_Product'] == product) & 
                                    (template_df['Click_to_Open_Rate'] <= 0) &  # Note: using <= to include exactly 0
                                    (template_df['Total_Sent'] >= required_volume) & 
                                    (template_df['Pruning_Flag'] == 0)
                                ].index
                            else:
                                # Regular case: prune templates below the threshold
                                indices_to_prune = template_df[
                                    (template_df['Stage'] == stage) & 
                                    (template_df['Associated_Product'] == product) & 
                                    (template_df['Click_to_Open_Rate'] < lower_quartile_cto) & 
                                    (template_df['Total_Sent'] >= required_volume) & 
                                    (template_df['Pruning_Flag'] == 0)
                                ].index
                            
                            # Update the pruning flag
                            template_df.loc[indices_to_prune, 'Pruning_Flag'] = 1
                            stage_pruned_cto = len(indices_to_prune)
                            
                            # Calculate the percentile threshold for open rate
                            lower_quartile_open = stage_product_unpruned['Open_Rate'].quantile(percentile_threshold, interpolation='linear')
                            
                            # Only continue if we have a valid threshold
                            if lower_quartile_open is not None:
                                # Store threshold value for open rate as percentage for UI display
                                if stage_product_key not in threshold_values:
                                    threshold_values[stage_product_key] = {}
                                # The values are already percentages, so no need to multiply by 100
                                threshold_values[stage_product_key]['open_rate'] = lower_quartile_open
                                print(f"DEBUG: Added Open Rate threshold for {stage}:{product}: {threshold_values[stage_product_key]['open_rate']:.2f}%")
                                
                                # Apply pruning logic based on open rate
                                # Special case: if threshold is 0, also prune templates with exactly 0 open rate
                                if lower_quartile_open == 0:
                                    print(f"DEBUG: Open Rate Threshold for {stage}:{product} is 0, also pruning templates with 0 open rate")
                                    indices_to_prune_open = template_df[
                                        (template_df['Stage'] == stage) & 
                                        (template_df['Associated_Product'] == product) & 
                                        (template_df['Open_Rate'] <= 0) &  # Note: using <= to include exactly 0
                                        (template_df['Total_Sent'] >= required_volume) & 
                                        (template_df['Pruning_Flag'] == 0)
                                    ].index
                                else:
                                    # Regular case: prune templates below the threshold
                                    indices_to_prune_open = template_df[
                                        (template_df['Stage'] == stage) & 
                                        (template_df['Associated_Product'] == product) & 
                                        (template_df['Open_Rate'] < lower_quartile_open) & 
                                        (template_df['Total_Sent'] >= required_volume) & 
                                        (template_df['Pruning_Flag'] == 0)
                                    ].index
                                
                                # Update pruning flag for these templates as well
                                template_df.loc[indices_to_prune_open, 'Pruning_Flag'] = 1
                                stage_pruned_open = len(indices_to_prune_open)
                                
                                # Log how many templates were pruned from this stage-product combination
                                logger.info(f"Stage-Product {stage}:{product}: Pruned {stage_pruned_cto} templates based on click-to-open rate and {stage_pruned_open} based on open rate")
                                total_pruned += stage_pruned_cto + stage_pruned_open
                    except Exception as e:
                        logger.warning(f"Error applying pruning logic for stage-product '{stage}:{product}': {str(e)}")
                
            logger.info(f"Applied stage-based pruning logic: {total_pruned} templates flagged for pruning across {len(stage_products)} stage-product combinations")
        
        # Convert Pruning_Flag to integer to ensure clean data
        template_df['Pruning_Flag'] = template_df['Pruning_Flag'].astype(int)
    
    # Save template performance data
    save_template_performance(template_df)
    
    # Update template files with performance data
    update_template_files_with_performance(template_df)
    
    # Debug statement before returning values
    print(f"DEBUG: analyze_template_performance returning - df shape: {template_df.shape}, thresholds: {list(threshold_values.keys())}")
    
    # Return both the template dataframe and threshold values
    return template_df, threshold_values


def save_template_performance(template_df):
    """
    Save template performance data to a CSV file.
    
    Args:
        template_df (pd.DataFrame): DataFrame with template performance metrics
    """
    # Configure logging if not already configured
    logger = logging.getLogger(__name__)
    
    if template_df.empty:
        logger.warning("No template performance data to save")
        return
    
    # Create directory if it doesn't exist
    template_dir = Path("data/mail_performance/templates")
    template_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate filename with year and month
    year_month = datetime.now().strftime("%Y%m")
    output_file = template_dir / f"template_performance_{year_month}.csv"
    
    # Save to CSV
    try:
        template_df.to_csv(output_file, index=False)
        logger.info(f"Template performance data saved to {output_file}")
    except Exception as e:
        logger.error(f"Error saving template performance data: {str(e)}")


def update_template_files_with_performance(template_df):
    """
    Update template JSON files in the data/templates directory with performance metrics.
    
    Args:
        template_df (pd.DataFrame): DataFrame with template performance metrics
    """
    import json
    import os
    import glob
    
    # Configure logging if not already configured
    logger = logging.getLogger(__name__)
    
    if template_df.empty:
        logger.warning("No template performance data to update template files")
        return
        
    # Create a dictionary to store performance data by template name for easier lookup
    performance_by_template = template_df.set_index('Template_Name').to_dict('index')
    
    # Get all template files in the data/templates directory
    template_files = glob.glob('data/templates/*.json')
    logger.info(f"Found {len(template_files)} template files in data/templates/")
    
    # Initialize counters for updated templates and skipped templates
    updated_count = 0
    skipped_count = 0
    
    # Process each template file
    for template_file in template_files:
        try:
            # Read the template file
            with open(template_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            
            # Templates can be a list of template objects or a single template object
            if not isinstance(templates, list):
                templates = [templates]
            
            # Track if any template in this file was updated
            file_updated = False
            
            # Process each template in the file
            for i, template in enumerate(templates):
                # Check if the template has a template_name field
                if 'template_name' in template:
                    template_name = template['template_name']
                    
                    # Check if we have performance data for this template
                    if template_name in performance_by_template:
                        # Extract performance metrics
                        perf_data = performance_by_template[template_name]
                        
                        # Create a performance object
                        performance = {
                            'total_sent': int(perf_data['Total_Sent']),
                            'open_count': int(perf_data['Open_Count']),
                            'click_count': int(perf_data['Click_Count']),
                            'unsubscribe_count': int(perf_data['Unsubscribe_Count']),
                            'open_rate': round(perf_data['Open_Rate'], 2),
                            'click_rate': round(perf_data['Click_Rate'], 2),
                            'click_to_open_rate': round(perf_data['Click_to_Open_Rate'], 2),
                            'unsubscribe_rate': round(perf_data['Unsubscribe_Rate'], 2),
                            'pruning_flag': int(perf_data['Pruning_Flag']),
                            'last_updated': datetime.now().isoformat()
                        }
                        
                        # Add or update performance data in the template
                        template['performance'] = performance
                        file_updated = True
                        updated_count += 1
                        logger.debug(f"Updated performance metrics for template: {template_name}")
                    else:
                        skipped_count += 1
                        logger.debug(f"No matching performance data found for template: {template_name}")
                else:
                    skipped_count += 1
                    logger.debug(f"Template in {os.path.basename(template_file)} does not have a template_name field")
            
            # Only write to the file if at least one template was updated
            if file_updated:
                # Write updated templates back to the file
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(templates, f, indent=4, ensure_ascii=False)
                logger.info(f"Updated template file: {os.path.basename(template_file)}")
        
        except Exception as e:
            logger.error(f"Error updating template file {template_file}: {str(e)}")
    
    # Log the final count of updated and skipped templates
    logger.info(f"Template performance update complete. Updated: {updated_count}, Skipped: {skipped_count}")
    return updated_count


def update_template_performance(combined_performance_path=None):
    """
    Update template performance from combined performance data.
    
    Args:
        combined_performance_path (str, optional): Path to combined performance CSV file. 
                                                If not provided, will use the latest file.
    
    Returns:
        tuple: (DataFrame with updated template performance metrics, dict with threshold values)
    """
    print("DEBUG: update_template_performance called")
    # Configure logging if not already configured
    logger = logging.getLogger(__name__)
    
    # If no specific file is provided, find the latest one
    if combined_performance_path is None:
        import glob
        import os
        
        # Find combined performance files
        combined_files = glob.glob('data/mail_performance/combined/all_performance_*.csv')
        
        if not combined_files:
            logger.error("No combined performance files found")
            return pd.DataFrame(), {}
        
        # Get the latest file by modification time
        combined_performance_path = max(combined_files, key=os.path.getmtime)
        logger.info(f"Using latest combined performance file: {combined_performance_path}")
    
    # Load the combined performance data
    try:
        performance_data = pd.read_csv(combined_performance_path)
        logger.info(f"Loaded performance data with {len(performance_data)} records")
    except Exception as e:
        logger.error(f"Error loading combined performance data: {str(e)}")
        return pd.DataFrame(), {}
    
    # Run template performance analysis
    print("DEBUG: Before calling analyze_template_performance")
    result = analyze_template_performance(performance_data)
    print(f"DEBUG: analyze_template_performance returned result type: {type(result)}")
    
    # Explicitly unpack the values to help debug
    if isinstance(result, tuple) and len(result) == 2:
        template_df, threshold_values = result
        print(f"DEBUG: Successfully unpacked tuple with df shape: {template_df.shape}")
    else:
        print(f"DEBUG: Unexpected return format: {result}")
        # Handle incorrect return format
        if isinstance(result, pd.DataFrame):
            template_df = result
            threshold_values = {}
            print("DEBUG: Handling legacy return format (DataFrame only)")
        else:
            template_df = pd.DataFrame()
            threshold_values = {}
            print("DEBUG: Could not interpret return value, using empty defaults")
    
    print("DEBUG: Before final return from update_template_performance")
    return template_df, threshold_values


def create_template_performance_chart(template_df):
    """
    Create a visualization for template performance.
    
    Args:
        template_df (pd.DataFrame): DataFrame with template performance metrics
        
    Returns:
        plotly.graph_objects.Figure: Plotly figure with template performance visualization
    """
    # Configure logging if not already configured
    logger = logging.getLogger(__name__)
    
    if template_df.empty:
        # Return empty figure with message
        fig = go.Figure()
        fig.update_layout(
            title="No Template Performance Data Available",
            annotations=[
                dict(
                    text="No template performance data available",
                    showarrow=False,
                    xref="paper",
                    yref="paper",
                    x=0.5,
                    y=0.5
                )
            ]
        )
        return fig
    
    # Limit to top 10 templates by send volume for better visualization
    if len(template_df) > 10:
        top_templates = template_df.nlargest(10, 'Total_Sent')
    else:
        top_templates = template_df
    
    # Create subplot with two rows
    fig = sp.make_subplots(
        rows=2, cols=1,
        subplot_titles=(
            "Template Open & Click Rates",
            "Template Send Volumes"
        ),
        vertical_spacing=0.2,
        row_heights=[0.7, 0.3]
    )
    
    # Add bars for open rate
    fig.add_trace(
        go.Bar(
            name="Open Rate",
            x=top_templates['Template_Name'],
            y=top_templates['Open_Rate'],
            text=top_templates['Open_Rate'].round(1).astype(str) + '%',
            textposition='auto',
            marker_color=PRIMARY,
            opacity=0.8,
            hovertemplate='%{y:.1f}%<extra></extra>'
        ),
        row=1, col=1
    )
    
    # Add bars for click rate
    fig.add_trace(
        go.Bar(
            name="Click Rate",
            x=top_templates['Template_Name'],
            y=top_templates['Click_Rate'],
            text=top_templates['Click_Rate'].round(1).astype(str) + '%',
            textposition='auto',
            marker_color=SECONDARY,
            opacity=0.8,
            hovertemplate='%{y:.1f}%<extra></extra>'
        ),
        row=1, col=1
    )
    
    # Add bars for send volume
    fig.add_trace(
        go.Bar(
            name="Emails Sent",
            x=top_templates['Template_Name'],
            y=top_templates['Total_Sent'],
            text=top_templates['Total_Sent'],
            textposition='auto',
            marker_color=ACCENT,
            opacity=0.8,
            hovertemplate='%{y:,}<extra></extra>'
        ),
        row=2, col=1
    )
    
    # Update layout
    fig.update_layout(
        title={
            'text': "Email Template Performance",
            'y': 0.95,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20, 'color': PRIMARY}
        },
        barmode='group',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        height=800,
        margin=dict(t=100, b=50, l=50, r=50),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)'
    )
    
    # Update y-axis for first subplot to show percentages
    fig.update_yaxes(
        title_text="Rate (%)",
        range=[0, max(100, top_templates['Open_Rate'].max() * 1.1)],
        row=1, col=1
    )
    
    # Update y-axis for second subplot
    fig.update_yaxes(
        title_text="Emails Sent",
        row=2, col=1
    )
    
    # Update x-axis for better readability
    fig.update_xaxes(
        tickangle=45,
        tickmode='array',
        tickvals=list(range(len(top_templates))),
        ticktext=[f"{name[:30]}..." if len(name) > 30 else name for name in top_templates['Template_Name']]
    )
    
    return fig