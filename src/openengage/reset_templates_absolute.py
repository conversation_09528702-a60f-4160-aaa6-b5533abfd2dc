"""
<PERSON><PERSON><PERSON> to directly reset WhatsApp templates using absolute path.
"""
import os
import json

def reset_templates():
    """Reset the WhatsApp templates file directly using absolute path."""
    try:
        # Get the current working directory
        cwd = os.getcwd()
        print(f"Current working directory: {cwd}")
        
        # Define the absolute path to the templates file
        templates_file = os.path.abspath(os.path.join(cwd, '..', '..', 'data', 'whatsapp_templates.json'))
        print(f"Templates file path: {templates_file}")
        
        # Check if the file exists
        if os.path.exists(templates_file):
            print(f"File exists: {templates_file}")
        else:
            print(f"File does not exist: {templates_file}")
            return
        
        # Create an empty templates structure
        empty_templates = {
            "templates": [],
            "product_templates": {},
            "product_template_mappings": {}
        }
        
        # Write the empty templates to the file
        with open(templates_file, 'w') as f:
            json.dump(empty_templates, f, indent=2)
        
        print(f"Templates reset successfully!")
    except Exception as e:
        print(f"Error resetting templates: {str(e)}")

if __name__ == "__main__":
    reset_templates()
