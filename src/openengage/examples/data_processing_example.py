"""
Example of using the DataIntegration and UserDataProcessor classes together.

This example demonstrates how to use the DataIntegration class to fetch and prepare
data for processing by the UserDataProcessor class.
"""
import os
import sys
import logging
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from openengage.core.data_integration import DataIntegration
from openengage.core.user_data_processor import UserDataProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def progress_callback(message, progress):
    """Simple progress callback function."""
    logger.info(f"Progress {progress}%: {message}")

def main():
    """Main function to demonstrate the integration."""
    logger.info("Starting data processing example")
    
    # Initialize the DataIntegration class
    data_integration = DataIntegration()
    
    # Update all data files (comment out if you don't want to fetch new data)
    # This would connect to external data sources and update all files
    # data_integration.update_all_data(progress_callback=progress_callback)
    
    # Get file paths for processing
    file_paths = data_integration.get_file_paths()
    
    # Initialize the UserDataProcessor
    user_processor = UserDataProcessor()
    
    # Process the data
    result_df = user_processor.generate_user_behaviour_data(progress_callback=progress_callback)
    
    # Log the results
    if result_df is not None and not result_df.empty:
        logger.info(f"Successfully processed data for {len(result_df)} users")
        
        # Display sample of the results
        sample_size = min(5, len(result_df))
        logger.info(f"Sample of {sample_size} processed users:")
        for i, (_, row) in enumerate(result_df.head(sample_size).iterrows()):
            logger.info(f"User {i+1}: {row['user_email']}")
            logger.info(f"  Behavior: {row.get('user_behaviour', 'N/A')}")
            logger.info(f"  Products Purchased: {row.get('Purchased_product_list', [])}")
            logger.info(f"  Products as Lead: {row.get('Lead_product_list', [])}")
            logger.info(f"  Products Viewed: {row.get('Visited_product_list', [])}")
            logger.info("---")
    else:
        logger.warning("No data was processed")
    
    logger.info("Data processing example completed")

if __name__ == "__main__":
    main()
