"""
Configuration file for template pruning parameters in OpenEngage.
"""
import os
import json
from pathlib import Path

# Config file path
CONFIG_DIR = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) / "data" / "config"
CONFIG_FILE = CONFIG_DIR / "pruning_config.json"

# Default configuration for template pruning
DEFAULT_PRUNING_CONFIG = {
    # Minimum number of emails sent before applying pruning logic
    "required_volume_threshold": 10000,
    
    # Percentile value for threshold calculation (20% = 0.20)
    "percentile_threshold": 0.20,
    
    # Minimum number of templates required per stage-product combination
    "min_template_count": 3
}

# Descriptions for UI tooltips
PRUNING_CONFIG_DESCRIPTIONS = {
    "required_volume_threshold": "Minimum number of emails that must be sent using a template before it can be considered for pruning. Templates with fewer sends are excluded from pruning decisions.",
    
    "percentile_threshold": "The percentile value used to determine the performance threshold. Templates performing below this percentile (based on open/click rates) will be flagged for pruning.",
    
    "min_template_count": "Minimum number of templates required in a stage-product combination before pruning logic can be applied. This ensures diversity is maintained."
}

def ensure_config_dir():
    """Ensure the config directory exists"""
    os.makedirs(CONFIG_DIR, exist_ok=True)

def get_pruning_descriptions():
    """Returns the descriptions for pruning config parameters
    
    Returns:
        dict: A dictionary with descriptions for each configuration parameter
    """
    return PRUNING_CONFIG_DESCRIPTIONS

def get_pruning_config():
    """
    Returns the current pruning configuration settings.
    Loads from configuration file if it exists, otherwise returns default.
    
    Returns:
        dict: Current pruning configuration settings
    """
    try:
        if CONFIG_FILE.exists():
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
            # Ensure all required keys are present
            for key in DEFAULT_PRUNING_CONFIG:
                if key not in config:
                    config[key] = DEFAULT_PRUNING_CONFIG[key]
            return config
        else:
            return DEFAULT_PRUNING_CONFIG
    except Exception:
        # If there's any error reading the file, return defaults
        return DEFAULT_PRUNING_CONFIG

def save_pruning_config(config):
    """
    Saves the pruning configuration settings to a JSON file.
    
    Args:
        config (dict): Pruning configuration to save
        
    Returns:
        bool: True if successful, False otherwise
    """
    ensure_config_dir()
    try:
        # Validate config contains all required keys
        for key in DEFAULT_PRUNING_CONFIG:
            if key not in config:
                config[key] = DEFAULT_PRUNING_CONFIG[key]
        
        # Write to file
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
        return True
    except Exception:
        return False

def get_pruning_descriptions():
    """
    Returns the descriptions for pruning configuration parameters.
    
    Returns:
        dict: Descriptions for each pruning configuration parameter
    """
    return PRUNING_CONFIG_DESCRIPTIONS
