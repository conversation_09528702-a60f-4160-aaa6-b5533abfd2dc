"""
<PERSON><PERSON><PERSON> to directly reset WhatsApp templates.
"""
import os
import json

def reset_templates():
    """Reset the WhatsApp templates file directly."""
    try:
        # Define the path to the templates file
        templates_file = os.path.join('data', 'whatsapp_templates.json')
        
        # Create an empty templates structure
        empty_templates = {
            "templates": [],
            "product_templates": {},
            "product_template_mappings": {}
        }
        
        # Write the empty templates to the file
        with open(templates_file, 'w') as f:
            json.dump(empty_templates, f, indent=2)
        
        print(f"Templates reset successfully! File: {os.path.abspath(templates_file)}")
    except Exception as e:
        print(f"Error resetting templates: {str(e)}")

if __name__ == "__main__":
    reset_templates()
