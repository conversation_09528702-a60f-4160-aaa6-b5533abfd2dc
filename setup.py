from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="openengage",
    version="0.1.0",
    author="OpenEngage Team",
    author_email="<EMAIL>",
    description="Open Source Marketing Automation Platform",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/OpenEngage/openengage",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Marketing/Sales",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    include_package_data=True,
    package_data={
        "openengage": ["config/*.yml"],
    },
)
