[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT) [![Contributor Covenant](https://img.shields.io/badge/Contributor%20Covenant-2.1-4baaaa.svg)](code_of_conduct.md)

# OpenEngage Marketing Assistant

A powerful marketing automation system that helps create personalized marketing campaigns using AI. Built with Streamlit and CrewAI.

## Project Structure

```
openengage/
├── config/             # Configuration files
│   └── prompts.yml    # AI prompt templates
├── data/              # Data storage
├── docker/            # Docker configuration
│   └── Dockerfile
├── docs/              # Documentation
├── src/               # Source code
│   └── openengage/
│       ├── __init__.py
│       ├── app.py     # Main Streamlit application
│       ├── agents.py  # AI agents definitions
│       └── crew_manager.py  # AI crew orchestration
├── tests/             # Test files
├── .env              # Environment variables (create from template)
├── docker-compose.yml
├── Makefile          # Development commands
├── requirements.txt
└── setup.py          # Package configuration
```

## Requirements

- Python 3.10 or higher (required for type hint syntax)
- OpenAI API key
- Docker (optional, for containerized deployment)

## Features

- **Product Analysis**: Automatically extracts and analyzes product details from URLs
- **Multi-Channel Communication**: Supports Email, WhatsApp, SMS, and Push Notifications
- **Personalized Campaigns**: Generates tailored marketing content based on user journey stages
- **User Journey Management**: Define and manage custom user journey stages
- **Communication Settings**: Configure sender details, tone, and UTM parameters

## Quick Start

### Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/OpenEngage/openengage.git
   cd openengage
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: .\venv\Scripts\activate
   ```

3. Install in development mode:
   ```bash
   pip install -e .
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your OpenAI API key
   ```

5. Run the application:
   ```bash
   streamlit run src/openengage/app.py
   ```

### Using Docker (Recommended for Production)

1. Build and run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

2. Access the application at http://localhost:8501

## Development Commands

Use the Makefile for common development tasks:

```bash
make install    # Install package in development mode
make test      # Run tests
make lint      # Run code quality checks
make clean     # Clean build artifacts
make build     # Build distribution package
```

## Docker Commands

```bash
make docker-build  # Build Docker image
make docker-run    # Run with Docker Compose
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

For detailed guidelines, see [CONTRIBUTING.md](CONTRIBUTING.md)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
