#!/usr/bin/env python3
"""
Organization Analyzer API

A simple FastAPI service that analyzes organization websites to extract:
- Domain (business domain like EdTech, Healthcare, etc.)
- WhatWeDo (main purpose/activities)
- AboutUs (detailed description)
- Class (specific business category)

Usage:
    python organization_analyzer_api.py

API Endpoints:
    POST /analyze - Analyze organization by URL
    GET /health - Health check

Example:
    curl -X POST "http://localhost:8000/analyze" \
         -H "Content-Type: application/json" \
         -d '{"url": "https://www.analyticsvidhya.com"}'
"""

import os
import json
import re
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import requests
from bs4 import BeautifulSoup
from openai import OpenAI
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import uvicorn

# Load environment variables
load_dotenv()

class AnalyzeRequest(BaseModel):
    """Request model for organization analysis"""
    url: str = Field(..., description="Organization website URL")
    
class AnalyzeResponse(BaseModel):
    """Response model for organization analysis"""
    url: str = Field(..., description="Analyzed URL")
    domain: str = Field(..., description="Business domain (e.g., EdTech, Healthcare)")
    what_we_do: str = Field(..., description="What the organization does")
    about_us: str = Field(..., description="About the organization")
    business_class: str = Field(..., description="Business class category")
    success: bool = Field(..., description="Whether analysis was successful")
    from_cache: bool = Field(..., description="Whether data was loaded from local cache")

class WebScraper:
    """Simple web scraper for extracting website content"""
    
    def scrape(self, url: str) -> str:
        """Scrape content from a given URL"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
                
            # Get text content
            text = soup.get_text(separator='\n', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            content = '\n'.join(lines)
            
            return content
            
        except Exception as e:
            raise Exception(f"Error scraping URL: {str(e)}")

class OrganizationDataManager:
    """Manages organization data loading, saving, and analysis"""

    def __init__(self):
        self.data_file = "data/organization_data.json"
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        if not self.client.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")

        # Ensure data directory exists
        os.makedirs("data", exist_ok=True)

    def load_organization_data(self, organization_url: str) -> Optional[Dict[str, Any]]:
        """
        Load organization data from local file if it exists.

        Args:
            organization_url: URL of the organization to load

        Returns:
            Organization data if found, None otherwise
        """
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r') as f:
                    all_orgs = json.load(f)

                    # Handle both old and new format
                    if isinstance(all_orgs, dict) and organization_url in all_orgs:
                        org_data = all_orgs[organization_url]
                        # Ensure URL is in the data
                        org_data["url"] = organization_url
                        return org_data
            return None
        except Exception as e:
            print(f"Error loading organization data: {str(e)}")
            return None

    def save_organization_data(self, org_data: Dict[str, Any], organization_url: str) -> bool:
        """
        Save organization data to local file.

        Args:
            org_data: Organization data to save
            organization_url: URL of the organization

        Returns:
            True if save successful, False otherwise
        """
        try:
            # Ensure org_data has required fields
            org_data["url"] = organization_url
            org_data["saved"] = True

            # Load existing data
            all_orgs = {}
            if os.path.exists(self.data_file):
                try:
                    with open(self.data_file, 'r') as f:
                        file_data = json.load(f)
                        if isinstance(file_data, dict):
                            all_orgs = file_data
                except json.JSONDecodeError:
                    all_orgs = {}

            # Save this organization's data
            all_orgs[organization_url] = org_data

            # Write back to file
            with open(self.data_file, 'w') as f:
                json.dump(all_orgs, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving organization data: {str(e)}")
            return False
    
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """Analyze organization content and extract structured data"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an organization analyzer. Extract the following details from the provided content:
                        - Business Domain of the organization (Eg:- Edtech, Healthcare etc.)
                        - What We Do
                        - About Us
                        - Class (one of the following: EdTech, E-commerce, Insurance, Banking, Quick-Commerce, TravelTech, Creator Economy Platform, B2B). Food is considered in Quick Commerce and Example of Creator Economy Platform is Substack, Patreon etc.
                        
                        Return the information in a JSON format with these exact keys:
                        {
                            "Domain": "...",
                            "WhatWeDo": "...",
                            "AboutUs": "...",
                            "Class": "..."
                        }
                        
                        If any field is not found, use reasonable defaults based on available information.
                        Never return null or empty values."""
                    },
                    {
                        "role": "user",
                        "content": f"Extract structured organization details from the following content:\n{content}"
                    }
                ],
                temperature=0.9
            )
            
            # Parse JSON response
            json_str = response.choices[0].message.content.strip()
            json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
            
            result = json.loads(json_str)
            
            # Ensure all required fields exist with defaults
            default_output = {
                "Domain": "Unknown Domain",
                "WhatWeDo": "Information not available",
                "AboutUs": "Information not available",
                "Class": "Unknown Class"
            }
            
            for key in default_output:
                if key not in result or not result[key]:
                    result[key] = default_output[key]
            
            return result
            
        except Exception as e:
            return {
                "Domain": "Error",
                "WhatWeDo": "Error occurred",
                "AboutUs": f"Failed to analyze organization: {str(e)}",
                "Class": "Unknown Class"
            }

# Initialize FastAPI app
app = FastAPI(
    title="Organization Analyzer API",
    description="Analyze organization websites to extract business information",
    version="1.0.0"
)

# Initialize components
scraper = WebScraper()
data_manager = OrganizationDataManager()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "organization-analyzer-api"}

@app.get("/cached")
async def list_cached_organizations():
    """
    List all organizations currently cached in local storage

    Returns:
        List of cached organization URLs and basic info
    """
    try:
        if os.path.exists(data_manager.data_file):
            with open(data_manager.data_file, 'r') as f:
                all_orgs = json.load(f)

                cached_orgs = []
                for url, org_data in all_orgs.items():
                    cached_orgs.append({
                        "url": url,
                        "domain": org_data.get("Domain", "Unknown"),
                        "class": org_data.get("Class", "Unknown"),
                        "saved": org_data.get("saved", False)
                    })

                return {
                    "cached_organizations": cached_orgs,
                    "total_count": len(cached_orgs)
                }
        else:
            return {
                "cached_organizations": [],
                "total_count": 0
            }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to load cached organizations: {str(e)}"
        )

@app.post("/analyze", response_model=AnalyzeResponse)
async def analyze_organization(request: AnalyzeRequest):
    """
    Analyze an organization website and extract business information.
    First checks local cache, only scrapes and analyzes if data doesn't exist.

    Returns:
        - domain: Business domain (e.g., EdTech, Healthcare)
        - what_we_do: What the organization does
        - about_us: About the organization
        - business_class: Business class category
        - from_cache: Whether data was loaded from local cache
    """
    try:
        # Validate URL format
        url = request.url.strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # First, try to load from local cache
        cached_data = data_manager.load_organization_data(url)
        if cached_data:
            return AnalyzeResponse(
                url=url,
                domain=cached_data["Domain"],
                what_we_do=cached_data["WhatWeDo"],
                about_us=cached_data["AboutUs"],
                business_class=cached_data["Class"],
                success=True,
                from_cache=True
            )

        # No cached data found, scrape and analyze
        content = scraper.scrape(url)
        org_data = data_manager.analyze_content(content)

        # Save to cache for future requests
        data_manager.save_organization_data(org_data, url)

        return AnalyzeResponse(
            url=url,
            domain=org_data["Domain"],
            what_we_do=org_data["WhatWeDo"],
            about_us=org_data["AboutUs"],
            business_class=org_data["Class"],
            success=True,
            from_cache=False
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze organization: {str(e)}"
        )

if __name__ == "__main__":
    print("🚀 Starting Organization Analyzer API...")
    print("📋 Available endpoints:")
    print("   POST /analyze - Analyze organization by URL (checks cache first)")
    print("   GET /cached - List all cached organizations")
    print("   GET /health - Health check")
    print("   GET /docs - API documentation")
    print("\n💡 Example usage:")
    print('   curl -X POST "http://localhost:8000/analyze" \\')
    print('        -H "Content-Type: application/json" \\')
    print('        -d \'{"url": "https://www.analyticsvidhya.com"}\'')
    print("\n📁 Cache behavior:")
    print("   - First request: Scrapes website and saves to data/organization_data.json")
    print("   - Subsequent requests: Returns cached data instantly")
    print("   - Use GET /cached to see what's already analyzed")
    print("\n🌐 Starting server on http://localhost:8000")

    uvicorn.run(app, host="0.0.0.0", port=8000)
