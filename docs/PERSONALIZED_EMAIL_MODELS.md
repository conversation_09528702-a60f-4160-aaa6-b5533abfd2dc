# Personalized Email Generation - AI Models

This document describes the AI models available for generating fully personalized emails in OpenEngage.

## Overview

The personalized email generator supports multiple AI models from different providers, allowing users to choose the best model for their specific needs. Each model has different characteristics in terms of quality, speed, and cost.

## Supported AI Models

### OpenAI Models

| Model | Description | Best For |
|-------|-------------|----------|
| `gpt-4.1` | Latest GPT-4.1 model with enhanced capabilities | High-quality, complex personalization |
| `gpt-4.1-mini` | Faster, cost-effective version of GPT-4.1 | Balanced quality and speed |
| `gpt-4o` | GPT-4 Omni model with multimodal capabilities | Advanced content generation |
| `gpt-4o-mini` | Lightweight version of GPT-4o | Quick generation with good quality |
| `gpt-3.5-turbo` | Fast and cost-effective model | High-volume campaigns |

### Claude Models

| Model | Description | Best For |
|-------|-------------|----------|
| `claude-opus-4-20250514` | Claude Opus 4 - Most capable model | Highest intelligence, complex reasoning |
| `claude-sonnet-4-20250514` | <PERSON> 4 - High-performance model | Balanced performance and efficiency |
| `claude-3-7-sonnet-20250219` | Claude Sonnet 3.7 - Extended thinking | High performance with advanced reasoning |
| `claude-3-5-sonnet-20241022` | Claude Sonnet 3.5 v2 - Previous intelligent | High-quality content generation |
| `claude-3-5-haiku-20241022` | Claude Haiku 3.5 - Fastest model | Speed and efficiency |
| `claude-3-opus-20240229` | Claude Opus 3 - Complex tasks | Complex reasoning and creativity |
| `claude-3-haiku-20240307` | Claude Haiku 3 - Fast and compact | Quick, targeted performance |

### Fallback Options

| Model | Description | Best For |
|-------|-------------|----------|
| `crew-agent` | Internal crew-based generation | Consistent brand voice, offline usage |

## Configuration

### Environment Variables

Set the following environment variables to enable AI models:

```bash
# For OpenAI models
OPENAI_API_KEY=your_openai_api_key_here

# For Claude models
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### Model Selection

1. **Automatic Detection**: The system automatically detects available models based on configured API keys
2. **UI Selection**: Users can select their preferred model from the dropdown in the personalized email interface
3. **Fallback Chain**: If the selected model fails, the system automatically falls back to alternative models

## Features

### Metadata Tracking

For each generated email, the system tracks:
- **Model Used**: Which AI model generated the content
- **Prompt**: The full prompt sent to the AI model
- **Input Tokens**: Number of tokens in the input prompt
- **Output Tokens**: Number of tokens in the generated response
- **Generation Time**: Time taken to generate the email

### Quality Comparison

Different models may produce varying results:
- **Claude models** tend to produce more nuanced, human-like content
  - ✨ **Auto-cleaned**: Structural headings (HOOK, PITCH, TRUST, MOTIVATION) are automatically removed
- **GPT models** offer consistent quality with good customization
- **Crew agents** provide brand-consistent content with predefined styles

## Usage Examples

### High-Quality Campaigns
Use `claude-opus-4-20250514` or `claude-sonnet-4-20250514` or `gpt-4.1` for premium campaigns where quality is paramount.

### High-Volume Campaigns
Use `gpt-4.1-mini` or `claude-3-5-haiku-20241022` for large-scale campaigns requiring speed and cost efficiency.

### Advanced Reasoning Campaigns
Use `claude-3-7-sonnet-20250219` for campaigns requiring extended thinking and complex reasoning.

### Brand-Consistent Campaigns
Use `crew-agent` when you need consistent brand voice and don't require external AI services.

## Cost Optimization

- **Monitor token usage** through the metadata tracking
- **Choose appropriate models** based on campaign requirements
- **Use mini/haiku variants** for cost-sensitive campaigns
- **Leverage crew agents** for offline or cost-free generation

## Troubleshooting

### Model Not Available
- Verify API keys are correctly set in environment variables
- Check that the selected model is supported by your API plan
- Ensure network connectivity for API calls

### Generation Failures
- The system automatically falls back to alternative models
- Check error messages in the UI for specific issues
- Verify API quotas and rate limits

### Quality Issues
- Try different models to find the best fit for your content style
- Adjust communication settings to guide model behavior
- Use the prompt tracking to optimize input quality

## Best Practices

1. **Test different models** with sample data to find the best fit
2. **Monitor costs** using token tracking data
3. **Use appropriate models** for different campaign types
4. **Keep API keys secure** and rotate them regularly
5. **Have fallback options** configured for reliability

## Support

For issues with specific AI models:
- **OpenAI models**: Check OpenAI API status and documentation
- **Claude models**: Check Anthropic API status and documentation
- **Crew agents**: Check internal crew configuration and logs
