# Offer Details Feature for Mass Campaign Generator

This document provides instructions on how to use the new Offer Details feature in the Mass Campaign Generator module.

## Overview

The Offer Details feature allows you to include personalized special offers in your mass email campaigns. The system will generate a visually distinct offer section that will be included in the email content.

## How It Works

1. You upload your main user data CSV file as usual
2. You can optionally upload a second CSV file containing offer details with start and end times
3. The system applies the first valid offer (where current time is between start and end time) to all users
4. For each user, a personalized offer section is generated using GPT-4o-mini
5. The offer section is formatted as HTML and included in the email

## CSV File Format

The offer details CSV file should have the following columns:

- **offer_start_time**: Start time of the offer in ISO format (e.g., '2023-06-01T00:00:00')
- **offer_end_time**: End time of the offer in ISO format (e.g., '2023-06-30T23:59:59')
- **offer_details**: Description of the offer (e.g., '20% discount', 'Buy one get one free')

Example:
```csv
offer_start_time,offer_end_time,offer_details
2023-06-01T00:00:00,2023-06-03T23:59:59,20% discount on all products
2023-06-15T00:00:00,2023-06-22T23:59:59,Free shipping on orders over $50
```

All times should be in UTC format.

## Using the Feature

1. Prepare your main user data CSV file as usual
2. Prepare your offer details CSV file according to the format above
3. In the Mass Campaign Generator interface, upload both files
4. Click "Generate Mass Campaigns"
5. The system will process both files and include offer sections for matching users

## Sample Offer Data

You can download a sample offer data file by clicking the "Download Sample Offer CSV" button. This will provide you with a sample CSV file that includes offer details with start and end times based on the current date.

## Offer Section Appearance

The offer section will appear as a visually distinct section in the email with:

- A dashed border
- A light background color
- A "SPECIAL OFFER" label
- The personalized offer text generated by GPT-4o-mini

## Technical Details

- The offer section is generated using GPT-4o-mini
- The offer section is inserted into the HTML email before the closing body tag
- The offer section is styled with CSS to make it visually distinct
- The offer text is processed to include proper formatting, links, etc.

## Limitations

- Only one offer (the first valid one) will be applied to all users
- The offer is time-based and will only be applied if the current time is between the start and end times
- The offer section is generated based on the offer details, product data, and user data
- The offer section is not editable after generation
