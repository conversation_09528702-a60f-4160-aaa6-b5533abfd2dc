#!/usr/bin/env python3
"""
Organization Analyzer Script using OrganizationAnalyzerTool from agents.py

This script uses only the OrganizationAnalyzerTool from src/openengage/agents.py
to analyze 100 organizations and store Domain, WhatWeDo, AboutUs, Class in a CSV file.
"""

import os
import sys
import csv
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to Python path to import modules
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# Import the tools from agents.py
try:
    from src.openengage.agents import OrganizationAnalyzerTool, WebScrapingTool
    print("✅ Successfully imported OrganizationAnalyzerTool and WebScrapingTool")
except ImportError as e:
    print(f"❌ Error importing tools from agents.py: {e}")
    sys.exit(1)

class OrganizationAnalyzerScript:
    """Script to analyze 100 organizations using OrganizationAnalyzerTool."""
    
    def __init__(self):
        """Initialize the analyzer script."""
        self.org_analyzer = OrganizationAnalyzerTool()
        self.web_scraper = WebScrapingTool()
        self.results = []
        self.csv_filename = f"organization_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 100 organization URLs for testing
        self.organization_urls = ["https://www.allstate.com",
                                    "https://www.motorola.com",
                                    "https://www.gsk.com",
                                    "https://www.wipro.com",
                                    "https://www.amdocs.com",
                                    "https://www.gds.ey.com",
                                    "https://www.adobe.com",
                                    "https://www.shell.com",
                                    "https://www.tothenew.com",
                                    "https://www.tnqtech.com",
                                    "https://www.gadgetsoftware.com",
                                    "https://www.pmam.com",
                                    "https://www.harbingergroup.com",
                                    "https://www.gyanai.com",
                                    "https://www.verisk.com",
                                    "https://www.accenture.com",
                                    "https://www.adityabirla.com",
                                    "https://www.westpharma.com",
                                    "https://www.bt.com",
                                    "https://www.ideas2it.com",
                                    "https://www.meritgroup.co.uk",
                                    "https://www.fibre2fashion.com",
                                    "https://www.jocata.com",
                                    "https://www.ntpc.co.in",
                                    "https://www.honeywell.com",
                                    "https://www.tenthplanet.in",
                                    "https://www.cloud.com",
                                    "https://www.soch.com",
                                    "https://www.opentext.com",
                                    "https://www.sony.com",
                                    "https://www.cxo.net",
                                    "https://www.cloud.org",
                                    "https://www.live.com",
                                    "https://www.hm.com",
                                    "https://www.jamaav.com",
                                    "https://www.itester.com",
                                    "https://www.ab-inbev.com",
                                    "https://www.imgc.com",
                                    "https://www.barracuda.com",
                                    "https://www.teikametrics.com",
                                    "https://www.servicenow.com",
                                    "https://www.merckgroup.com",
                                    "https://www.cba.com.au",
                                    "https://www.amadeus.com",
                                    "https://www.akamai.com",
                                    "https://www.unisys.com",
                                    "https://www.toasttab.com",
                                    "https://www.infor.com",
                                    "https://www.pfizer.com",
                                    "https://www.kickdrumtech.com",
                                    "https://www.iiitb.net",
                                    "https://www.merck.com",
                                    "https://www.target.com",
                                    "https://www.vinayakamissions.com",
                                    "https://www.syf.com",
                                    "https://www.facillima.com",
                                    "https://www.pattern.com",
                                    "https://www.cat.com",
                                    "https://www.chubb.com",
                                    "https://www.tranetechnologies.com",
                                    "https://www.ford.com",
                                    "https://www.innovizeai.com",
                                    "https://www.comline.uk.com",
                                    "https://www.colruytgroup.com",
                                    "https://www.ericsson.com",
                                    "https://www.analyticsvidhya.com",
                                    "https://www.cdmsmith.com",
                                    "https://www.bcg.com",
                                    "https://www.ece.iitr.ac.in",
                                    "https://www.tspolice.gov.in",
                                    "https://www.juicelabs.ai",
                                    "https://www.dell.com",
                                    "https://www.gruve.ai",
                                    "https://www.roche.com",
                                    "https://www.rakuten.com",
                                    "https://www.first-insight.com",
                                    "https://www.jeevlifeworks.com",
                                    "https://www.ethostech.com.au",
                                    "https://www.grofers.com",
                                    "https://www.zomato.com",
                                    "https://www.astrazeneca.com",
                                    "https://www.aexp.com",
                                    "https://www.paypal.com",
                                    "https://www.ramarson.com",
                                    "https://www.inceptivetechnologies.com",
                                    "https://www.tyson.com",
                                    "https://www.thomsonreuters.com",
                                    "https://www.tata.com",
                                    "https://www.prescienceds.com",
                                    "https://www.brand-scapes.com",
                                    "https://www.mphasis.com",
                                    "https://www.accorian.com",
                                    "https://www.coforge.com",
                                    "https://www.syneoshealth.com",
                                    "https://www.student.tgm.ac.at",
                                    "https://www.analytx4t.com",
                                    "https://www.intelliswift.com",
                                    "https://www.gim.ac.in",
                                    "https://www.cisco.com"
        ]
    
    def scrape_website_content(self, url: str) -> str:
        """Scrape website content using WebScrapingTool."""
        try:
            content = self.web_scraper._execute(url)
            return content
        except Exception as e:
            print(f"  ❌ Error scraping {url}: {str(e)}")
            return ""
    
    def analyze_organization(self, content: str) -> tuple[Dict[str, Any], str]:
        """Analyze organization using OrganizationAnalyzerTool and return both result and raw response."""
        try:
            # Use the _run method to get the raw JSON string response
            raw_response = self.org_analyzer._run(content)

            # Use the _execute method to get the dictionary directly
            result = self.org_analyzer._execute(content)

            return result, raw_response
        except Exception as e:
            print(f"  ❌ Error analyzing organization: {str(e)}")
            error_result = {
                "Domain": "Error",
                "WhatWeDo": "Analysis failed",
                "AboutUs": f"Error: {str(e)}",
                "Class": "Unknown"
            }
            return error_result, f"Error: {str(e)}"
    
    def process_organization(self, url: str, index: int) -> Dict[str, Any]:
        """Process a single organization through scraping and analysis."""
        print(f"\n[{index+1}/100] Processing: {url}")

        result = {
            "index": index + 1,
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "raw_response": None
        }

        try:
            # Step 1: Scrape website content
            print(f"  🔍 Scraping website...")
            content = self.scrape_website_content(url)

            if not content or len(content.strip()) < 50:
                result["error"] = "Failed to scrape meaningful content"
                result.update({
                    "Domain": "Scraping Failed",
                    "WhatWeDo": "Could not extract content",
                    "AboutUs": "Website content unavailable",
                    "Class": "Unknown",
                    "raw_response": "No content to analyze"
                })
                return result

            # Step 2: Analyze organization using OrganizationAnalyzerTool
            print(f"  🏢 Analyzing organization...")
            org_data, raw_response = self.analyze_organization(content)

            # Update result with organization data and raw response
            result.update(org_data)
            result["raw_response"] = raw_response
            result["success"] = True
            print(f"  ✅ Completed successfully")
            print(f"     Domain: {org_data.get('Domain', 'N/A')}")
            print(f"     Class: {org_data.get('Class', 'N/A')}")

        except Exception as e:
            result["error"] = str(e)
            result.update({
                "Domain": "Error",
                "WhatWeDo": "Processing failed",
                "AboutUs": f"Error: {str(e)}",
                "Class": "Unknown",
                "raw_response": f"Error occurred: {str(e)}"
            })
            print(f"  ❌ Failed: {e}")

        return result
    
    def save_to_csv(self):
        """Save all results to a CSV file."""
        if not self.results:
            print("No results to save.")
            return

        # Define CSV headers - added Raw_Response column
        headers = [
            "Index", "URL", "Timestamp", "Success", "Error",
            "Domain", "WhatWeDo", "AboutUs", "Class", "Raw_Response"
        ]

        print(f"\n💾 Saving results to {self.csv_filename}...")

        with open(self.csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

            for result in self.results:
                # Map result keys to CSV headers
                csv_row = {
                    "Index": result.get("index", ""),
                    "URL": result.get("url", ""),
                    "Timestamp": result.get("timestamp", ""),
                    "Success": result.get("success", False),
                    "Error": result.get("error", ""),
                    "Domain": result.get("Domain", ""),
                    "WhatWeDo": result.get("WhatWeDo", ""),
                    "AboutUs": result.get("AboutUs", ""),
                    "Class": result.get("Class", ""),
                    "Raw_Response": result.get("raw_response", "")
                }
                writer.writerow(csv_row)

        print(f"✅ Results saved to {self.csv_filename}")

        # Also save raw responses to a separate JSON file for easier analysis
        raw_responses_filename = self.csv_filename.replace('.csv', '_raw_responses.json')
        print(f"💾 Saving raw responses to {raw_responses_filename}...")

        raw_responses_data = []
        for result in self.results:
            raw_responses_data.append({
                "index": result.get("index", ""),
                "url": result.get("url", ""),
                "timestamp": result.get("timestamp", ""),
                "success": result.get("success", False),
                "raw_response": result.get("raw_response", ""),
                "parsed_data": {
                    "Domain": result.get("Domain", ""),
                    "WhatWeDo": result.get("WhatWeDo", ""),
                    "AboutUs": result.get("AboutUs", ""),
                    "Class": result.get("Class", "")
                }
            })

        with open(raw_responses_filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(raw_responses_data, jsonfile, indent=2, ensure_ascii=False)

        print(f"✅ Raw responses saved to {raw_responses_filename}")
    
    def generate_summary_report(self):
        """Generate a summary report of the analysis."""
        if not self.results:
            return
        
        successful = [r for r in self.results if r.get("success", False)]
        failed = [r for r in self.results if not r.get("success", False)]
        
        print(f"\n📊 SUMMARY REPORT")
        print(f"=" * 50)
        print(f"Total Organizations Processed: {len(self.results)}")
        print(f"Successful Analyses: {len(successful)}")
        print(f"Failed Analyses: {len(failed)}")
        print(f"Success Rate: {len(successful)/len(self.results)*100:.1f}%")
        
        if successful:
            # Analyze business classes
            classes = [r.get("Class", "Unknown") for r in successful]
            class_counts = {}
            for cls in classes:
                class_counts[cls] = class_counts.get(cls, 0) + 1
            
            print(f"\n📈 Business Class Distribution:")
            for cls, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {cls}: {count} ({count/len(successful)*100:.1f}%)")
            
            # Analyze domains
            domains = [r.get("Domain", "Unknown") for r in successful]
            domain_counts = {}
            for domain in domains:
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            print(f"\n🏢 Top Business Domains:")
            for domain, count in sorted(domain_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"  {domain}: {count}")
        
        if failed:
            print(f"\n❌ Failed Organizations:")
            for result in failed[:5]:  # Show first 5 failures
                print(f"  {result.get('url', 'Unknown')}: {result.get('error', 'Unknown error')}")
            if len(failed) > 5:
                print(f"  ... and {len(failed) - 5} more")
    
    def run(self, test_mode: bool = False):
        """Run the complete analysis process."""
        if test_mode:
            print("🧪 Running in TEST MODE - Processing only 5 organizations")
            urls_to_process = self.organization_urls[:5]
            self.csv_filename = f"organization_analysis_SAMPLE_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            print("🚀 Starting Organization Analysis using OrganizationAnalyzerTool")
            urls_to_process = self.organization_urls
        
        print(f"📋 Processing {len(urls_to_process)} organizations...")
        print(f"💾 Results will be saved to: {self.csv_filename}")
        print(f"🛠️  Using: OrganizationAnalyzerTool from agents.py")
        
        start_time = time.time()
        
        for i, url in enumerate(urls_to_process):
            result = self.process_organization(url, i)
            self.results.append(result)
            
            # Add a small delay to be respectful to websites
            time.sleep(random.uniform(1, 3))
            
            # Save progress every 10 organizations (or every 2 in test mode)
            save_interval = 2 if test_mode else 10
            if (i + 1) % save_interval == 0:
                self.save_to_csv()
                total = len(urls_to_process)
                print(f"\n💾 Progress saved ({i + 1}/{total} completed)")
        
        # Final save and summary
        self.save_to_csv()
        self.generate_summary_report()
        
        end_time = time.time()
        duration = end_time - start_time
        print(f"\n⏱️  Total execution time: {duration/60:.1f} minutes")
        print(f"📁 Results saved to: {self.csv_filename}")


def main():
    """Main function to run the organization analyzer."""
    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable is required")
        print("Please set your OpenAI API key in the .env file")
        return
    
    # Check for test mode argument
    test_mode = len(sys.argv) > 1 and sys.argv[1].lower() in ['test', '--test', '-t']
    
    try:
        analyzer = OrganizationAnalyzerScript()
        analyzer.run(test_mode=test_mode)
    except KeyboardInterrupt:
        print("\n⏹️  Process interrupted by user")
        if hasattr(analyzer, 'results') and analyzer.results:
            analyzer.save_to_csv()
            print("💾 Partial results saved")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print("Organization Analyzer Script")
        print("Uses OrganizationAnalyzerTool from src/openengage/agents.py")
        print()
        print("Usage:")
        print("  python organization_analyzer_script.py          # Process all 100 organizations")
        print("  python organization_analyzer_script.py test     # Process only 5 organizations (test mode)")
        print("  python organization_analyzer_script.py --help   # Show this help")
        print()
        print("Output: CSV file with Domain, WhatWeDo, AboutUs, Class columns")
    else:
        main()
