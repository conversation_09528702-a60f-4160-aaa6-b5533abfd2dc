<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Vidhya Blog</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tracking.js"></script>
    <script src="js/openengage-connector.js"></script>
    <script src="js/journey-integration.js"></script>
    <script src="js/event-tracker.js"></script>
    <script src="js/direct-logger.js"></script>
    <script src="popup-adapter.js"></script>
    <script src="../popup.js?v=1.0.1"></script>
    <script src="../BrahmaScript.js"></script>

    <script>
    // Direct event logger (no separate server needed)
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Direct event logger initialized');
        
        // Track page view immediately
        logEvent('pageview', {
            title: document.title,
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        
        // Track all link clicks
        document.querySelectorAll('a').forEach(function(link) {
            link.addEventListener('click', function(e) {
                // Get the link text or URL
                const linkText = this.textContent.trim() || this.getAttribute('href');
                
                logEvent('click', {
                    text: linkText,
                    href: this.getAttribute('href'),
                    timestamp: new Date().toISOString()
                });
            });
        });
        
        // Function to log events directly to our server
        function logEvent(type, data) {
            const eventData = { type: type, data: data };
            console.log('Logging event:', eventData);
            
            fetch('/log_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(eventData)
            })
            .then(response => response.json())
            .then(result => console.log('Event logged:', result))
            .catch(error => console.error('Error logging event:', error));
        }
    });
    </script>
    
</head>
<body>
    <header>
        <div class="header-container container">
            <a href="index.html" class="logo">Analytics Vidhya</a>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="blog1.html">Data Science</a></li>
                    <li><a href="blog2.html">Machine Learning</a></li>
                    <li><a href="blog3.html">Deep Learning</a></li>
                    <li><a href="blog4.html">NLP</a></li>
                    <li><a href="blog5.html">AI Tools</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <section class="hero">
            <h1>Your Path to Data Science Excellence</h1>
            <p>Expert tutorials, learning resources, and industry insights for data science professionals and enthusiasts.</p>
        </section>

        <section class="blog-cards">
            <article class="blog-card">
                <img src="https://source.unsplash.com/random/600x400/?data" alt="Data Science" class="card-image">
                <div class="card-content">
                    <h2 class="card-title">Introduction to Data Science in 2025</h2>
                    <p class="card-excerpt">Discover the latest trends and technologies shaping the data science landscape in 2025 and how you can stay ahead of the curve.</p>
                    <a href="blog1.html" class="read-more">Read More</a>
                </div>
            </article>

            <article class="blog-card">
                <img src="https://source.unsplash.com/random/600x400/?ai" alt="Machine Learning" class="card-image">
                <div class="card-content">
                    <h2 class="card-title">Advanced Machine Learning Algorithms Explained</h2>
                    <p class="card-excerpt">A deep dive into the most powerful machine learning algorithms transforming industries today and practical implementation tips.</p>
                    <a href="blog2.html" class="read-more">Read More</a>
                </div>
            </article>

            <article class="blog-card">
                <img src="https://source.unsplash.com/random/600x400/?neural" alt="Deep Learning" class="card-image">
                <div class="card-content">
                    <h2 class="card-title">Deep Learning: Beyond Neural Networks</h2>
                    <p class="card-excerpt">Explore advanced deep learning architectures that go beyond traditional neural networks and their real-world applications.</p>
                    <a href="blog3.html" class="read-more">Read More</a>
                </div>
            </article>

            <article class="blog-card">
                <img src="https://source.unsplash.com/random/600x400/?language" alt="NLP" class="card-image">
                <div class="card-content">
                    <h2 class="card-title">Natural Language Processing Breakthroughs</h2>
                    <p class="card-excerpt">Learn about the latest advancements in NLP and how they're revolutionizing human-computer interaction across industries.</p>
                    <a href="blog4.html" class="read-more">Read More</a>
                </div>
            </article>

            <article class="blog-card">
                <img src="https://source.unsplash.com/random/600x400/?robot" alt="AI Tools" class="card-image">
                <div class="card-content">
                    <h2 class="card-title">Essential AI Tools for Data Scientists</h2>
                    <p class="card-excerpt">A comprehensive guide to the most powerful AI tools that every data scientist should master to enhance productivity.</p>
                    <a href="blog5.html" class="read-more">Read More</a>
                </div>
            </article>
        </section>

        <div class="connect-widget">
            <h3>Ready to advance your data science career?</h3>
            <p>Join our community of over 500,000 data professionals and get personalized learning recommendations.</p>
            <a href="https://www.analyticsvidhya.com/" target="_blank" class="connect-button">Connect with Analytics Vidhya</a>
        </div>
    </main>

    <footer>
        <div class="container">
            <p class="footer-text">&copy; 2025 Analytics Vidhya. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
