/**
 * Event Tracker for Demo Blog
 * Tracks page views and link clicks and sends them to the event server
 */

// Configuration
const EVENT_SERVER_URL = 'http://localhost:8001';
const DEBUG = true;

// Initialize event tracking
document.addEventListener('DOMContentLoaded', function() {
    console.log('Event tracker initialized');
    
    // Track the initial page view
    trackPageView();
    
    // Set up click tracking for all links
    setupLinkTracking();
});

/**
 * Track a page view event
 */
function trackPageView() {
    const eventData = {
        type: 'pageview',
        data: {
            title: document.title,
            url: window.location.href,
            timestamp: new Date().toISOString()
        }
    };
    
    sendEvent(eventData);
    logDebug('Page view tracked:', eventData.data.title);
}

/**
 * Set up tracking for all link clicks
 */
function setupLinkTracking() {
    // Find all links in the document
    const links = document.querySelectorAll('a');
    
    // Add click event listeners to each link
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            // Get the link text or URL
            const linkText = link.textContent.trim() || link.getAttribute('href');
            
            const eventData = {
                type: 'click',
                data: {
                    text: linkText,
                    href: link.getAttribute('href'),
                    timestamp: new Date().toISOString()
                }
            };
            
            sendEvent(eventData);
            logDebug('Link click tracked:', linkText);
            
            // Don't delay navigation for internal links
            const isInternal = 
                link.getAttribute('href').startsWith('#') || 
                link.getAttribute('target') === '_self';
                
            if (!isInternal) {
                // For external links, delay navigation slightly to ensure the event is sent
                e.preventDefault();
                setTimeout(() => {
                    window.location.href = link.getAttribute('href');
                }, 100);
            }
        });
    });
    
    logDebug(`Set up tracking for ${links.length} links`);
}

/**
 * Send event data to the event server
 * @param {Object} eventData - The event data to send
 */
function sendEvent(eventData) {
    fetch(EVENT_SERVER_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(eventData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logDebug('Event successfully recorded');
        } else {
            console.error('Failed to record event:', data.error);
        }
    })
    .catch(error => {
        console.error('Error sending event:', error);
    });
}

/**
 * Log debug messages if debug mode is enabled
 */
function logDebug(...args) {
    if (DEBUG) {
        console.log('[EventTracker]', ...args);
    }
}
