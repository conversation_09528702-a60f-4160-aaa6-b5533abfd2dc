/**
 * OpenEngage Demo Blog Tracking Script
 * This script tracks user activity on the demo blog and stores it for the Journey Builder
 */

// Initialize tracking data
let userActivity = {
    pageViews: [],
    totalTimeSpent: 0,
    scrollDepth: {},
    lastActive: Date.now()
};

// Track page views
function trackPageView(pageTitle, pageUrl) {
    const timestamp = new Date().toISOString();
    userActivity.pageViews.push({
        title: pageTitle,
        url: pageUrl,
        timestamp: timestamp
    });
    
    // Log the activity
    console.log(`Page viewed: ${pageTitle}`);
    
    // Save to local storage
    saveActivityData();
    
    // Send to OpenEngage if available
    sendToOpenEngage();
    
    // Also try to send data via the connector (in case the Journey Builder is already open)
    if (typeof window.sendActivityToJourneyBuilder === 'function') {
        console.log('Attempting to send activity data via connector');
        window.sendActivityToJourneyBuilder();
    }
}

// Track time spent on page
function trackTimeSpent() {
    const now = Date.now();
    const timeSpent = now - userActivity.lastActive;
    userActivity.totalTimeSpent += timeSpent;
    userActivity.lastActive = now;
    
    // Save every 5 seconds
    saveActivityData();
}

// Track scroll depth
function trackScrollDepth() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const docHeight = Math.max(
        document.body.scrollHeight,
        document.documentElement.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.offsetHeight,
        document.body.clientHeight,
        document.documentElement.clientHeight
    );
    
    const scrollPercent = Math.round((scrollTop / (docHeight - windowHeight)) * 100);
    
    // Track maximum scroll depth for current page
    const currentPage = window.location.pathname;
    userActivity.scrollDepth[currentPage] = Math.max(
        scrollPercent,
        userActivity.scrollDepth[currentPage] || 0
    );
    
    // Save on significant scroll
    if (scrollPercent % 25 === 0 && scrollPercent > 0) {
        saveActivityData();
    }
}

// Save activity data to local storage
function saveActivityData() {
    localStorage.setItem('oe_userActivity', JSON.stringify(userActivity));
}

// Load activity data from local storage
function loadActivityData() {
    const savedData = localStorage.getItem('oe_userActivity');
    if (savedData) {
        userActivity = JSON.parse(savedData);
        userActivity.lastActive = Date.now();
    }
}

// Generate a summary of user activity for Journey Builder
function generateActivitySummary() {
    let summary = "";
    
    // Add page views
    if (userActivity.pageViews.length > 0) {
        summary += "Visited pages:\n";
        userActivity.pageViews.forEach(page => {
            summary += `- ${page.title} (${new Date(page.timestamp).toLocaleString()})\n`;
        });
    }
    
    // Add time spent
    const minutes = Math.floor(userActivity.totalTimeSpent / 60000);
    const seconds = Math.floor((userActivity.totalTimeSpent % 60000) / 1000);
    summary += `\nTotal time spent: ${minutes} minutes, ${seconds} seconds\n`;
    
    // Add scroll depth
    if (Object.keys(userActivity.scrollDepth).length > 0) {
        summary += "\nReading depth:\n";
        for (const [page, depth] of Object.entries(userActivity.scrollDepth)) {
            const pageName = page.split("/").pop().replace(".html", "") || "home";
            summary += `- ${pageName}: ${depth}% scrolled\n`;
        }
    }
    
    return summary;
}

// Send data to OpenEngage (via journey builder)
function sendToOpenEngage() {
    const activitySummary = generateActivitySummary();
    
    // Set this in local storage for the Journey Builder to access
    localStorage.setItem('oe_activitySummary', activitySummary);
    
    // If we're on the OpenEngage platform page, update the Journey Builder input
    try {
        if (window.opener && window.opener.document) {
            const journeyTextarea = window.opener.document.querySelector('textarea[placeholder*="user\'s recent activity"]');
            if (journeyTextarea) {
                journeyTextarea.value = activitySummary;
                // Trigger input event to make Streamlit detect the change
                const event = new Event('input', { bubbles: true });
                journeyTextarea.dispatchEvent(event);
            }
        }
    } catch (e) {
        console.log("Could not communicate with OpenEngage:", e);
    }
}

// Initialize tracking
function initTracking() {
    // Load existing data
    loadActivityData();
    
    // Track the current page view
    trackPageView(document.title, window.location.href);
    
    // Set up event listeners
    document.addEventListener('scroll', trackScrollDepth);
    
    // Time tracking interval (every 5 seconds)
    setInterval(trackTimeSpent, 5000);
    
    // Track when user leaves page
    window.addEventListener('beforeunload', function() {
        trackTimeSpent();
        sendToOpenEngage();
    });
}

// Start tracking when DOM is loaded
document.addEventListener('DOMContentLoaded', initTracking);
