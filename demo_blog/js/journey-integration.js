/**
 * Journey Builder Integration
 * Direct integration between demo blog and Journey Builder
 */

// Constants
const JOURNEY_BUILDER_URL = 'http://localhost:8501';
const TEXTAREA_SELECTORS = [
    'textarea[placeholder*="Describe the user"]',
    'textarea[placeholder*="activity"]',
    'textarea[placeholder*="behavior"]',
    'textarea[placeholder*="recent"]'
];
const UPDATE_INTERVAL = 2000; // Check every 2 seconds

// State variables
let journeyBuilderWindow = null;
let initComplete = false;
let lastActivityHash = '';

// Initialize the integration
function initJourneyIntegration() {
    console.log('Journey Builder Integration initialized');
    
    // Set up periodic activity updates
    setInterval(updateJourneyBuilder, UPDATE_INTERVAL);
    
    // Listen for storage changes to detect activity updates
    window.addEventListener('storage', function(e) {
        if (e.key === 'oe_userActivity') {
            console.log('Activity data changed, updating Journey Builder');
            updateJourneyBuilder();
        }
    });
    
    // Expose public methods
    window.journeyIntegration = {
        openJourneyBuilder: openJourneyBuilder,
        updateJourneyBuilder: updateJourneyBuilder
    };
    
    initComplete = true;
}

// Open the Journey Builder in a new window
function openJourneyBuilder() {
    if (journeyBuilderWindow && !journeyBuilderWindow.closed) {
        journeyBuilderWindow.focus();
    } else {
        journeyBuilderWindow = window.open(JOURNEY_BUILDER_URL, 'JourneyBuilder', 'width=1200,height=800');
        
        // Check if window was blocked
        if (!journeyBuilderWindow) {
            alert('Popup blocked! Please allow popups for this site to use the Journey Builder integration.');
            return;
        }
    }
    
    // Try to update immediately, then set up polling to wait for Journey Builder to load
    setTimeout(updateJourneyBuilder, 1000);
}

// Update Journey Builder with latest activity data
function updateJourneyBuilder() {
    if (!journeyBuilderWindow || journeyBuilderWindow.closed) {
        return; // No open Journey Builder window
    }
    
    try {
        // Get activity data
        const activityData = getActivityData();
        
        // Skip if no data or same as last update
        const activityHash = JSON.stringify(activityData);
        if (!activityData || activityHash === lastActivityHash) {
            return;
        }
        
        // Format activity summary
        const activitySummary = formatActivitySummary(activityData);
        lastActivityHash = activityHash;
        
        // Find and update the textarea
        const textarea = findJourneyTextarea(journeyBuilderWindow.document);
        if (textarea) {
            console.log('Found Journey Builder textarea, updating with activity data');
            
            // Update the textarea
            textarea.value = activitySummary;
            
            // Trigger input event to make Streamlit detect the change
            const event = new Event('input', { bubbles: true });
            textarea.dispatchEvent(event);
            
            // Try to trigger the Generate Journey button
            triggerGenerateButton(journeyBuilderWindow.document);
        } else {
            console.log('Journey Builder textarea not found, will retry later');
        }
    } catch (error) {
        console.error('Error updating Journey Builder:', error);
    }
}

// Get user activity data from localStorage
function getActivityData() {
    try {
        const data = localStorage.getItem('oe_userActivity');
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error reading activity data:', error);
        return null;
    }
}

// Format activity data into a human-readable summary
function formatActivitySummary(activityData) {
    if (!activityData) {
        return "No activity data available";
    }
    
    let summary = "User activity on Analytics Vidhya blog:\n\n";
    
    // Add page views with timestamps
    if (activityData.pageViews && activityData.pageViews.length > 0) {
        summary += "Pages visited:\n";
        activityData.pageViews.forEach(page => {
            // Extract the blog number from URL if present
            let blogNumber = '';
            if (page.url.includes('blog')) {
                blogNumber = page.url.match(/blog(\d+)\.html/);
                if (blogNumber) {
                    blogNumber = ` (Blog #${blogNumber[1]})`;
                }
            }
            
            summary += `- ${page.title}${blogNumber} at ${new Date(page.timestamp).toLocaleString()}\n`;
        });
        summary += "\n";
    }
    
    // Add time spent
    if (activityData.totalTimeSpent) {
        const minutes = Math.floor(activityData.totalTimeSpent / 60000);
        const seconds = Math.floor((activityData.totalTimeSpent % 60000) / 1000);
        summary += `Total time spent browsing: ${minutes} minutes, ${seconds} seconds\n\n`;
    }
    
    // Add scroll depth information
    if (activityData.scrollDepth && Object.keys(activityData.scrollDepth).length > 0) {
        summary += "Content engagement (scroll depth):\n";
        for (const [page, depth] of Object.entries(activityData.scrollDepth)) {
            // Extract page name from path
            let pageName = page.split('/').pop().replace('.html', '') || 'home';
            
            // Map blog pages to their topics
            const blogTopics = {
                'blog1': 'Data Science',
                'blog2': 'Machine Learning',
                'blog3': 'Deep Learning',
                'blog4': 'NLP',
                'blog5': 'AI Tools'
            };
            
            if (blogTopics[pageName]) {
                pageName = `${blogTopics[pageName]} (${pageName})`;
            }
            
            // Add engagement level interpretation
            let engagementLevel = 'Low engagement';
            if (depth > 75) {
                engagementLevel = 'Very high engagement';
            } else if (depth > 50) {
                engagementLevel = 'High engagement';
            } else if (depth > 25) {
                engagementLevel = 'Medium engagement';
            }
            
            summary += `- ${pageName}: ${depth}% scrolled (${engagementLevel})\n`;
        }
        summary += "\n";
    }
    
    // Add product interest inference
    const productInterest = inferProductInterest(activityData);
    if (productInterest) {
        summary += productInterest;
    }
    
    return summary;
}

// Infer product interest based on pages viewed
function inferProductInterest(activityData) {
    if (!activityData.pageViews || activityData.pageViews.length === 0) {
        return "";
    }
    
    // Map blog topics to products
    const productMapping = {
        'Data Science': 'Blackbelt+',
        'Machine Learning': 'AgenticAI',
        'Deep Learning': 'Generative AI Pinnacle',
        'NLP': 'AgenticAI',
        'AI Tools': ['AgenticAI', 'Blackbelt+', 'Generative AI Pinnacle']
    };
    
    // Count visits to each topic
    const topicCounts = {
        'Data Science': 0,
        'Machine Learning': 0,
        'Deep Learning': 0,
        'NLP': 0,
        'AI Tools': 0
    };
    
    activityData.pageViews.forEach(page => {
        if (page.title.includes('Data Science')) topicCounts['Data Science']++;
        if (page.title.includes('Machine Learning')) topicCounts['Machine Learning']++;
        if (page.title.includes('Deep Learning')) topicCounts['Deep Learning']++;
        if (page.title.includes('NLP')) topicCounts['NLP']++;
        if (page.title.includes('AI Tools')) topicCounts['AI Tools']++;
    });
    
    // Find the most visited topic
    let maxCount = 0;
    let topTopic = '';
    for (const [topic, count] of Object.entries(topicCounts)) {
        if (count > maxCount) {
            maxCount = count;
            topTopic = topic;
        }
    }
    
    if (topTopic && maxCount > 0) {
        let productInterest = productMapping[topTopic];
        if (Array.isArray(productInterest)) {
            productInterest = productInterest[0]; // Just pick the first one
        }
        
        return `Based on browsing behavior, the user shows strong interest in ${topTopic} content, suggesting they might be a good fit for our ${productInterest} product offering.\n`;
    }
    
    return "";
}

// Find the Journey Builder textarea using multiple selectors
function findJourneyTextarea(doc) {
    // Try each selector in order
    for (const selector of TEXTAREA_SELECTORS) {
        const textarea = doc.querySelector(selector);
        if (textarea) {
            return textarea;
        }
    }
    
    // If not found, try looking at all textareas
    const allTextareas = doc.querySelectorAll('textarea');
    for (const textarea of allTextareas) {
        // Look for textareas with relevant keywords in placeholder or nearby labels
        if (textarea.placeholder && 
            (textarea.placeholder.toLowerCase().includes('activity') || 
             textarea.placeholder.toLowerCase().includes('behavior') ||
             textarea.placeholder.toLowerCase().includes('user'))) {
            return textarea;
        }
        
        // Check for nearby labels that might indicate this is the right textarea
        const nearbyText = getNearbyText(textarea);
        if (nearbyText.toLowerCase().includes('activity') || 
            nearbyText.toLowerCase().includes('behavior') ||
            nearbyText.toLowerCase().includes('journey')) {
            return textarea;
        }
    }
    
    // Not found
    return null;
}

// Get text content of elements near the given element
function getNearbyText(element) {
    let text = '';
    
    // Look at previous siblings
    let prev = element.previousElementSibling;
    while (prev && text.length < 1000) {
        text += ' ' + prev.textContent;
        prev = prev.previousElementSibling;
    }
    
    // Look at parent and its previous siblings
    let parent = element.parentElement;
    if (parent) {
        text += ' ' + parent.textContent;
        
        prev = parent.previousElementSibling;
        while (prev && text.length < 1000) {
            text += ' ' + prev.textContent;
            prev = prev.previousElementSibling;
        }
    }
    
    return text;
}

// Try to find and click the Generate Journey button
function triggerGenerateButton(doc) {
    // Look for buttons that might be the Generate Journey button
    const buttons = doc.querySelectorAll('button');
    for (const button of buttons) {
        const buttonText = button.textContent.toLowerCase();
        if ((buttonText.includes('generate') && 
             (buttonText.includes('journey') || buttonText.includes('personalized'))) ||
            buttonText.includes('create journey')) {
            
            console.log('Found Generate Journey button, clicking it');
            setTimeout(() => {
                try {
                    button.click();
                } catch (error) {
                    console.error('Error clicking Generate Journey button:', error);
                }
            }, 500); // Small delay to ensure the textarea value is set
            
            return true;
        }
    }
    
    return false;
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initJourneyIntegration);
