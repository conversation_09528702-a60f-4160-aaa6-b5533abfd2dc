/**
 * OpenEngage Connector
 * Connects the demo blog to the OpenEngage Journey Builder
 */

// OpenEngage connection settings
const openEngageSettings = {
    appUrl: 'http://localhost:8501', // Default Streamlit port
    journeyBuilderSelector: 'textarea[placeholder*="Describe the user"]', // More specific CSS selector for the Journey Builder input field
    pollInterval: 2000 // How often to check if OpenEngage is available (ms)
};

// Store reference to OpenEngage window if opened
let openEngageWindow = null;

// Open OpenEngage in a new window
function openJourneyBuilder() {
    // If window is already open, focus it
    if (openEngageWindow && !openEngageWindow.closed) {
        openEngageWindow.focus();
        sendActivityToJourneyBuilder();
        return;
    }
    
    // Open OpenEngage in a new window
    openEngageWindow = window.open(openEngageSettings.appUrl, 'OpenEngage', 'width=1200,height=800');
    
    // Start polling to check if Journey Builder is loaded
    setTimeout(checkAndSendActivity, 2000);
}

// Check if Journey Builder is loaded and send activity data
function checkAndSendActivity() {
    if (!openEngageWindow || openEngageWindow.closed) {
        console.log('OpenEngage window is closed');
        return;
    }
    
    try {
        // Try to access the Journey Builder textarea
        const journeyTextarea = findTextareaInWindow(openEngageWindow.document);
        
        if (journeyTextarea) {
            // Journey Builder is loaded, send the activity data
            console.log('Journey Builder loaded, sending activity data');
            
            // Generate activity summary from tracking.js data
            const activitySummary = generateActivitySummary();
            
            // Set the value in the textarea
            journeyTextarea.value = activitySummary;
            
            // Trigger input event to make Streamlit detect the change
            const event = new Event('input', { bubbles: true });
            journeyTextarea.dispatchEvent(event);
            
            // Optional: Focus on the textarea
            journeyTextarea.focus();
            
            console.log('Activity data sent to Journey Builder');
        } else {
            // Journey Builder not loaded yet, continue polling
            console.log('Journey Builder textarea not found yet, waiting...');
            setTimeout(checkAndSendActivity, openEngageSettings.pollInterval);
        }
    } catch (error) {
        // Error accessing the OpenEngage window (likely due to cross-origin restrictions)
        console.error('Error connecting to OpenEngage:', error);
        
        // Continue polling in case the restriction is temporary
        setTimeout(checkAndSendActivity, openEngageSettings.pollInterval);
    }
}

// Helper function to find the textarea in the OpenEngage window
function findTextareaInWindow(doc) {
    // Try specific selector
    let textarea = doc.querySelector('textarea[placeholder*="Describe the user"]');
    
    // If not found, try alternative selectors
    if (!textarea) {
        textarea = doc.querySelector('textarea[placeholder*="activity"]');
    }
    
    if (!textarea) {
        textarea = doc.querySelector('textarea[placeholder*="behavior"]');
    }
    
    // Try finding all textareas and looking for journey-related ones
    if (!textarea) {
        const allTextareas = doc.querySelectorAll('textarea');
        for (const ta of allTextareas) {
            if (ta.placeholder && (
                ta.placeholder.toLowerCase().includes('activity') || 
                ta.placeholder.toLowerCase().includes('behavior') ||
                ta.placeholder.toLowerCase().includes('user') ||
                ta.placeholder.toLowerCase().includes('journey')
            )) {
                textarea = ta;
                break;
            }
        }
    }
    
    // If still not found, try to find the Journey Builder section
    if (!textarea) {
        const journeyBuilderHeading = Array.from(doc.querySelectorAll('h2, h3')).find(h => 
            h.textContent.includes('Journey Builder')
        );
        
        if (journeyBuilderHeading) {
            // Look for textareas in the same section
            let currentElement = journeyBuilderHeading;
            while (currentElement && !textarea) {
                if (currentElement.tagName === 'TEXTAREA') {
                    textarea = currentElement;
                }
                currentElement = currentElement.nextElementSibling;
            }
        }
    }
    
    return textarea;
}

// Function to directly send activity to Journey Builder (when window is already open)
function sendActivityToJourneyBuilder() {
    try {
        if (!openEngageWindow || openEngageWindow.closed) {
            console.log('OpenEngage window is closed');
            return;
        }
        
        const journeyTextarea = findTextareaInWindow(openEngageWindow.document);
        if (journeyTextarea) {
            const activitySummary = generateActivitySummary();
            journeyTextarea.value = activitySummary;
            
            // Trigger input event
            const event = new Event('input', { bubbles: true });
            journeyTextarea.dispatchEvent(event);
            
            console.log('Activity data updated in Journey Builder');
            
            // Force Streamlit to refresh by clicking nearby buttons
            const buttons = openEngageWindow.document.querySelectorAll('button');
            for (const button of buttons) {
                if (button.textContent.includes('Generate') && 
                    (button.textContent.includes('Journey') || button.textContent.includes('Personalized'))) {
                    // Found the generate journey button, click it
                    // First ensure data is in the textarea
                    setTimeout(() => {
                        button.click();
                        console.log('Clicked on Generate Journey button');
                    }, 500);
                    break;
                }
            }
        } else {
            console.log('Journey Builder textarea not found');
        }
    } catch (error) {
        console.error('Error sending activity to Journey Builder:', error);
    }
}

// Generate a summary of user activity for Journey Builder
function generateActivitySummary() {
    // Load activity data from localStorage
    const savedData = localStorage.getItem('oe_userActivity');
    if (!savedData) {
        return "User visited the Analytics Vidhya blog but no detailed activity was recorded.";
    }
    
    const userActivity = JSON.parse(savedData);
    let summary = "User activity on Analytics Vidhya blog:\n\n";
    
    // Add page views with timestamps
    if (userActivity.pageViews && userActivity.pageViews.length > 0) {
        summary += "Pages visited:\n";
        userActivity.pageViews.forEach(page => {
            // Extract the blog number from URL if present
            let blogNumber = '';
            if (page.url.includes('blog')) {
                blogNumber = page.url.match(/blog(\d+)\.html/);
                if (blogNumber) {
                    blogNumber = ` (Blog #${blogNumber[1]})`;
                }
            }
            
            summary += `- ${page.title}${blogNumber} at ${new Date(page.timestamp).toLocaleString()}\n`;
        });
        summary += "\n";
    }
    
    // Add time spent
    if (userActivity.totalTimeSpent) {
        const minutes = Math.floor(userActivity.totalTimeSpent / 60000);
        const seconds = Math.floor((userActivity.totalTimeSpent % 60000) / 1000);
        summary += `Total time spent browsing: ${minutes} minutes, ${seconds} seconds\n\n`;
    }
    
    // Add scroll depth information
    if (userActivity.scrollDepth && Object.keys(userActivity.scrollDepth).length > 0) {
        summary += "Content engagement (scroll depth):\n";
        for (const [page, depth] of Object.entries(userActivity.scrollDepth)) {
            // Extract page name from path
            let pageName = page.split('/').pop().replace('.html', '') || 'home';
            
            // Map blog pages to their topics
            const blogTopics = {
                'blog1': 'Data Science',
                'blog2': 'Machine Learning',
                'blog3': 'Deep Learning',
                'blog4': 'NLP',
                'blog5': 'AI Tools'
            };
            
            if (blogTopics[pageName]) {
                pageName = `${blogTopics[pageName]} (${pageName})`;
            }
            
            // Add engagement level interpretation
            let engagementLevel = 'Low engagement';
            if (depth > 75) {
                engagementLevel = 'Very high engagement';
            } else if (depth > 50) {
                engagementLevel = 'High engagement';
            } else if (depth > 25) {
                engagementLevel = 'Medium engagement';
            }
            
            summary += `- ${pageName}: ${depth}% scrolled (${engagementLevel})\n`;
        }
        summary += "\n";
    }
    
    // Add product interest inference based on pages viewed
    const productInterest = inferProductInterest(userActivity);
    if (productInterest) {
        summary += productInterest;
    }
    
    return summary;
}

// Infer product interest based on pages viewed
function inferProductInterest(userActivity) {
    if (!userActivity.pageViews || userActivity.pageViews.length === 0) {
        return "";
    }
    
    // Map blog topics to products
    const productMapping = {
        'Data Science': 'Blackbelt+',
        'Machine Learning': 'AgenticAI',
        'Deep Learning': 'Generative AI Pinnacle',
        'NLP': 'AgenticAI',
        'AI Tools': ['AgenticAI', 'Blackbelt+', 'Generative AI Pinnacle']
    };
    
    // Count visits to each topic
    const topicCounts = {
        'Data Science': 0,
        'Machine Learning': 0,
        'Deep Learning': 0,
        'NLP': 0,
        'AI Tools': 0
    };
    
    userActivity.pageViews.forEach(page => {
        if (page.title.includes('Data Science')) topicCounts['Data Science']++;
        if (page.title.includes('Machine Learning')) topicCounts['Machine Learning']++;
        if (page.title.includes('Deep Learning')) topicCounts['Deep Learning']++;
        if (page.title.includes('NLP')) topicCounts['NLP']++;
        if (page.title.includes('AI Tools')) topicCounts['AI Tools']++;
    });
    
    // Find the most visited topic
    let maxCount = 0;
    let topTopic = '';
    for (const [topic, count] of Object.entries(topicCounts)) {
        if (count > maxCount) {
            maxCount = count;
            topTopic = topic;
        }
    }
    
    if (topTopic && maxCount > 0) {
        let productInterest = productMapping[topTopic];
        if (Array.isArray(productInterest)) {
            productInterest = productInterest[0]; // Just pick the first one
        }
        
        return `Based on browsing behavior, the user shows strong interest in ${topTopic} content, suggesting they might be a good fit for our ${productInterest} product offering.\n`;
    }
    
    return "";
}

// Add a Connect to Journey Builder button to the demo blog
function addJourneyBuilderButton() {
    // Create the button
    const button = document.createElement('button');
    button.textContent = 'Send Activity to Journey Builder';
    button.style.position = 'fixed';
    button.style.bottom = '20px';
    button.style.right = '20px';
    button.style.zIndex = '9999';
    button.style.padding = '10px 15px';
    button.style.background = '#2674ED';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '5px';
    button.style.cursor = 'pointer';
    button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    
    // Add hover effect
    button.onmouseover = function() {
        this.style.background = '#1a5dc8';
    };
    button.onmouseout = function() {
        this.style.background = '#2674ED';
    };
    
    // Add click handler
    button.onclick = function() {
        openJourneyBuilder();
    };
    
    // Add to document
    document.body.appendChild(button);
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add the Journey Builder button
    addJourneyBuilderButton();
    
    console.log('OpenEngage connector initialized');
    
    // Expose key functions globally for other scripts to use
    window.openJourneyBuilder = openJourneyBuilder;
    window.sendActivityToJourneyBuilder = sendActivityToJourneyBuilder;
    window.generateActivitySummary = generateActivitySummary;
});
