/**
 * Direct Logger for Demo Blog
 * Uses simple localStorage to track events, which can be extracted
 */

(function() {
    // Initialize logger
    console.log('Direct logger initialized');
    
    // Constants
    const EVENT_STORAGE_KEY = 'demo_blog_events';
    
    // Initialize event storage
    let events = loadEvents();
    
    // Log the page view immediately
    logEvent('pageview', {
        title: document.title,
        url: window.location.href,
        timestamp: new Date().toISOString()
    });
    
    // Set up click tracking for all links
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function(e) {
                // Get link text or URL
                const linkText = this.textContent.trim() || this.getAttribute('href');
                
                // Log the click event
                logEvent('click', {
                    text: linkText,
                    href: this.getAttribute('href'),
                    timestamp: new Date().toISOString()
                });
                
                // Add a small delay for external links to ensure event is saved
                const isExternalLink = 
                    this.getAttribute('href').startsWith('http') && 
                    !this.getAttribute('href').includes(window.location.hostname);
                    
                if (isExternalLink && !e.ctrlKey && !e.metaKey) {
                    e.preventDefault();
                    setTimeout(() => {
                        window.location.href = this.getAttribute('href');
                    }, 100);
                }
            });
        });
    });
    
    // Function to log an event
    function logEvent(type, data) {
        // Create event object
        const event = {
            type: type,
            data: data,
            timestamp: new Date().toISOString()
        };
        
        // Format message for easier extraction
        let message = '';
        if (type === 'pageview') {
            message = `The user has read blog titled "${data.title}"`;
        } else if (type === 'click') {
            message = `The user has clicked on the link "${data.text}"`;
        } else {
            message = `The user performed action: ${type}`;
        }
        
        // Add formatted message to event
        event.message = message;
        
        // Add to events array
        events.push(event);
        
        // Save events to localStorage
        saveEvents();
        
        // Log to console for debugging
        console.log('Event logged:', message);
        
        // Create a downloadable link in the DOM
        updateDownloadLink();
    }
    
    // Save events to localStorage
    function saveEvents() {
        try {
            localStorage.setItem(EVENT_STORAGE_KEY, JSON.stringify(events));
        } catch (e) {
            console.error('Error saving events:', e);
        }
    }
    
    // Load events from localStorage
    function loadEvents() {
        try {
            const storedEvents = localStorage.getItem(EVENT_STORAGE_KEY);
            return storedEvents ? JSON.parse(storedEvents) : [];
        } catch (e) {
            console.error('Error loading events:', e);
            return [];
        }
    }
    
    // Create or update the download link for the events log
    function updateDownloadLink() {
        // Remove any existing link
        const existingLink = document.getElementById('download-events-link');
        if (existingLink) {
            existingLink.remove();
        }
        
        // Only create link if there are events
        if (events.length === 0) {
            return;
        }
        
        // Format events as text
        let eventsText = "# User Events Log\n\n";
        events.forEach(event => {
            const timestamp = new Date(event.timestamp).toLocaleString();
            eventsText += `[${timestamp}] ${event.message}\n`;
        });
        
        // Create blob with events text
        const blob = new Blob([eventsText], {type: 'text/plain'});
        const url = URL.createObjectURL(blob);
        
        // Create download link
        const link = document.createElement('a');
        link.id = 'download-events-link';
        link.href = url;
        link.download = 'user_events.txt';
        link.innerText = 'Download Events Log';
        link.style.position = 'fixed';
        link.style.bottom = '70px';
        link.style.right = '20px';
        link.style.padding = '8px 16px';
        link.style.background = '#E91E63';
        link.style.color = 'white';
        link.style.borderRadius = '4px';
        link.style.textDecoration = 'none';
        link.style.zIndex = '9999';
        link.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
        
        // Add hover style
        link.onmouseover = function() {
            this.style.background = '#C2185B';
        };
        link.onmouseout = function() {
            this.style.background = '#E91E63';
        };
        
        // Add to document
        document.body.appendChild(link);
    }
    
    // Expose public methods
    window.directLogger = {
        logEvent: logEvent,
        getEvents: function() { return events; },
        clearEvents: function() {
            events = [];
            saveEvents();
            console.log('Events cleared');
            updateDownloadLink();
        }
    };
})();
