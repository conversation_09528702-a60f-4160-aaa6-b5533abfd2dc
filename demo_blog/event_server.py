"""
Event Tracking Server for Demo Blog
Records user events to a text file
"""
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
import os
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('event_server')

# Constants
EVENT_LOG_FILE = "user_events.txt"
HOST = "localhost"
PORT = 8001  # Using a different port than the main web server
ALLOWED_ORIGINS = ["http://localhost:8000"]  # Only accept requests from our demo blog

class EventHandler(BaseHTTPRequestHandler):
    """HTTP request handler for event tracking"""
    
    def do_OPTIONS(self):
        """Handle preflight CORS requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests with event data"""
        try:
            # Get content length
            content_length = int(self.headers['Content-Length'])
            
            # Read request body
            post_data = self.rfile.read(content_length)
            
            # Parse JSON data
            event_data = json.loads(post_data.decode('utf-8'))
            logger.info(f"Received event: {event_data}")
            
            # Get the origin of the request
            origin = self.headers.get('Origin', '')
            
            # Only process requests from allowed origins
            if origin in ALLOWED_ORIGINS or not ALLOWED_ORIGINS:
                # Set CORS headers
                self.send_response(200)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                
                # Process the event
                success = self.process_event(event_data)
                
                # Send response
                response = {"success": success}
                self.wfile.write(json.dumps(response).encode('utf-8'))
            else:
                logger.warning(f"Rejected request from unauthorized origin: {origin}")
                self.send_response(403)
                self.end_headers()
        
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            self.send_response(500)
            self.end_headers()
            self.wfile.write(json.dumps({"success": False, "error": str(e)}).encode('utf-8'))
    
    def process_event(self, event_data):
        """Process event data and append to the file"""
        try:
            # Ensure we have the required fields
            if 'type' not in event_data or 'data' not in event_data:
                logger.warning(f"Missing required fields in event data: {event_data}")
                return False
            
            # Format the event message based on type
            if event_data['type'] == 'pageview':
                message = f"The user has read blog titled \"{event_data['data'].get('title', 'Unknown')}\"" 
            elif event_data['type'] == 'click':
                message = f"The user has clicked on the link \"{event_data['data'].get('text', 'Unknown')}\""
            else:
                message = f"The user performed action: {event_data['type']} - {json.dumps(event_data['data'])}"
            
            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            
            # Append to file
            with open(EVENT_LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(log_entry)
            
            logger.info(f"Logged event: {log_entry.strip()}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing event: {e}")
            return False

def run_server():
    """Start the HTTP server"""
    server = HTTPServer((HOST, PORT), EventHandler)
    logger.info(f"Event tracking server running at http://{HOST}:{PORT}")
    logger.info(f"Events will be logged to {os.path.abspath(EVENT_LOG_FILE)}")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    finally:
        server.server_close()
        logger.info("Server closed")

if __name__ == "__main__":
    run_server()
