"""
Save Popup Lead Endpoint
Saves popup form submissions to popup_leads.csv
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import csv
import datetime
from pathlib import Path

app = Flask(__name__)
# Enable CORS for all routes
CORS(app, resources={r"/*": {"origins": "*"}})

# Configure logging
import logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('popup_lead_server')

# Define the path to the CSV file
POPUP_LEADS_CSV_PATH = Path(__file__).parent.parent / "Sample Data For Mass Generation" / "popup_leads.csv"
logger.info(f"CSV file path: {POPUP_LEADS_CSV_PATH}")

@app.route('/save_popup_lead', methods=['POST'])
def save_popup_lead():
    """Save popup form data to CSV file"""
    try:
        logger.info("Received form submission request")
        
        # Log raw request data for debugging
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request data: {request.data}")
        
        # Get data from request
        data = request.get_json()
        logger.info(f"Parsed JSON data: {data}")
        
        contact_info = data.get('contact_info', '')
        redirect_url = data.get('redirect_url', '')
        user_agent = request.headers.get('User-Agent', '')
        
        logger.info(f"Processing submission: {contact_info} -> {redirect_url}")
        
        # Check if CSV file exists and create it with headers if not
        file_exists = os.path.isfile(POPUP_LEADS_CSV_PATH)
        logger.debug(f"CSV file exists: {file_exists}")
        
        # Create directory if it doesn't exist
        os.makedirs(POPUP_LEADS_CSV_PATH.parent, exist_ok=True)
        logger.debug(f"Ensured directory exists: {POPUP_LEADS_CSV_PATH.parent}")
        
        # Prepare the data row
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        row = [timestamp, contact_info, redirect_url, user_agent]
        
        # Open file in append mode
        logger.debug(f"Opening file for writing: {POPUP_LEADS_CSV_PATH}")
        with open(POPUP_LEADS_CSV_PATH, mode='a', newline='') as file:
            writer = csv.writer(file)
            
            # Write headers if file was just created
            if not file_exists:
                logger.debug("Writing CSV headers")
                writer.writerow(['timestamp', 'contact_info', 'target_url', 'user_agent'])
            
            # Write the data row
            logger.debug(f"Writing data row: {row}")
            writer.writerow(row)
        
        logger.info("Lead saved successfully")
        return jsonify({"success": True, "message": "Lead saved successfully", "redirect": redirect_url})
    
    except Exception as e:
        logger.error(f"Error saving lead: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

if __name__ == '__main__':
    try:
        # Ensure CSV file exists with headers
        if not os.path.isfile(POPUP_LEADS_CSV_PATH):
            logger.info(f"Creating CSV file: {POPUP_LEADS_CSV_PATH}")
            os.makedirs(POPUP_LEADS_CSV_PATH.parent, exist_ok=True)
            with open(POPUP_LEADS_CSV_PATH, mode='w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(['timestamp', 'contact_info', 'target_url', 'user_agent'])
        
        # Log app startup
        logger.info("Starting Flask app for popup lead saving")
        logger.info(f"Listening on port 5001")
        logger.info(f"CSV file path: {POPUP_LEADS_CSV_PATH}")
        
        # Run the Flask app in debug mode
        app.run(host='0.0.0.0', port=5001, debug=True)
    except Exception as e:
        logger.error(f"Error starting server: {str(e)}", exc_info=True)
