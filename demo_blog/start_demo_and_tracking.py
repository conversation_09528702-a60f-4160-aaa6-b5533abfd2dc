"""
Demo Blog with Event Tracking Launcher
Starts the demo blog web server, event tracking server, popup lead saving service, and Flask server for popup form submissions
"""
import os
import sys
import subprocess
import threading
import time
import webbrowser

def run_command(command, cwd=None):
    """Run a command in a new process"""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=True,
        cwd=cwd,
        text=True
    )
    return process

def start_demo_blog_server():
    """Start the demo blog web server"""
    print("Starting demo blog server...")
    demo_blog_dir = os.path.dirname(os.path.abspath(__file__))
    server_process = run_command("python -m http.server 8000", cwd=demo_blog_dir)
    print(f"Demo blog server running at http://localhost:8000")
    return server_process

def start_event_server():
    """Start the event tracking server"""
    print("Starting event tracking server...")
    demo_blog_dir = os.path.dirname(os.path.abspath(__file__))
    server_process = run_command("python event_server.py", cwd=demo_blog_dir)
    print(f"Event tracking server running at http://localhost:8001")
    return server_process

def start_popup_lead_server():
    """Start the popup lead saving service"""
    print("Starting popup lead saving service...")
    demo_blog_dir = os.path.dirname(os.path.abspath(__file__))
    server_process = run_command("python save_popup_lead.py", cwd=demo_blog_dir)
    print(f"Popup lead saving service running at http://localhost:5001")
    return server_process

def monitor_process(process, name):
    """Monitor a process and print its output"""
    while True:
        output = process.stdout.readline()
        if output:
            print(f"[{name}] {output.strip()}")
        
        error = process.stderr.readline()
        if error:
            print(f"[{name} ERROR] {error.strip()}")
            
        if output == '' and error == '' and process.poll() is not None:
            break

def main():
    """Main entry point"""
    try:
        # Create user_events.txt file if it doesn't exist
        demo_blog_dir = os.path.dirname(os.path.abspath(__file__))
        events_file = os.path.join(demo_blog_dir, "user_events.txt")
        if not os.path.exists(events_file):
            with open(events_file, 'w') as f:
                f.write(f"# User Events Log - Created on {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            print(f"Created events log file: {events_file}")
        
        # Start the demo blog server
        demo_server = start_demo_blog_server()
        
        # Start the event tracking server
        event_server = start_event_server()
        
        # Start the popup lead saving service
        popup_lead_server = start_popup_lead_server()
        
        # Create monitoring threads
        demo_thread = threading.Thread(target=monitor_process, args=(demo_server, "DEMO BLOG"))
        event_thread = threading.Thread(target=monitor_process, args=(event_server, "EVENT SERVER"))
        popup_lead_thread = threading.Thread(target=monitor_process, args=(popup_lead_server, "POPUP LEAD SERVER"))
        
        # Start threads
        demo_thread.daemon = True
        event_thread.daemon = True
        popup_lead_thread.daemon = True
        demo_thread.start()
        event_thread.start()
        popup_lead_thread.start()
        
        # Wait a moment before opening browser
        time.sleep(2)
        
        # Open the demo blog in a browser
        webbrowser.open("http://localhost:8000/index.html")
        
        print("\nAll servers are running!")
        print("Demo Blog: http://localhost:8000")
        print("Event Tracking Server: http://localhost:8001")
        print("Popup Lead Server: http://localhost:5001")
        print(f"Events are being logged to: {os.path.abspath('user_events.txt')}")
        print("\nPress Ctrl+C to stop all services")
        
        # Keep the main thread alive until Ctrl+C
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nShutting down all services...")
        # Cleanup will happen automatically when the script exits
        sys.exit(0)

if __name__ == "__main__":
    main()
