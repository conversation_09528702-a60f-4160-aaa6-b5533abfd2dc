/* 
 * OpenEngage Demo Blog Styles
 * Simple, clean styles for a professional-looking blog
 */

:root {
    --primary-color: #2674ED;
    --accent-color: #F9C823;
    --text-color: #333333;
    --light-gray: #f5f5f5;
    --medium-gray: #e0e0e0;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-gray);
    padding-top: 70px; /* Account for fixed header */
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

header {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--primary-color);
}

.hero {
    background-color: white;
    padding: 60px 0;
    text-align: center;
    margin-bottom: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.hero h1 {
    font-size: 36px;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.hero p {
    font-size: 18px;
    max-width: 800px;
    margin: 0 auto;
    color: #666;
}

.blog-content {
    background-color: white;
    border-radius: 8px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.blog-title {
    font-size: 32px;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.blog-meta {
    color: #666;
    margin-bottom: 30px;
    font-size: 14px;
}

.blog-meta span {
    margin-right: 20px;
}

.blog-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 30px;
}

.blog-text p {
    margin-bottom: 20px;
    font-size: 16px;
}

.blog-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.blog-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 20px;
}

.card-title {
    font-size: 20px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.card-excerpt {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.read-more {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s;
}

.read-more:hover {
    background-color: #1a5dc8;
}

footer {
    background-color: white;
    padding: 40px 0;
    text-align: center;
    margin-top: 60px;
    border-top: 1px solid var(--medium-gray);
}

.footer-text {
    font-size: 14px;
    color: #666;
}

.connect-widget {
    margin-top: 30px;
    padding: 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 8px;
    text-align: center;
}

.connect-widget h3 {
    margin-bottom: 15px;
}

.connect-button {
    display: inline-block;
    background-color: white;
    color: var(--primary-color);
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.connect-button:hover {
    background-color: var(--accent-color);
    color: white;
}

/* Responsive styles */
@media (max-width: 768px) {
    .blog-cards {
        grid-template-columns: 1fr;
    }
    
    .header-container {
        flex-direction: column;
        padding: 10px;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    nav ul li {
        margin-left: 10px;
    }
    
    .hero {
        padding: 40px 0;
    }
    
    .hero h1 {
        font-size: 28px;
    }
    
    .hero p {
        font-size: 16px;
    }
}
