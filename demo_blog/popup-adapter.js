/**
 * Popup Adapter for Demo Blog
 * This adapter ensures the popup works correctly when loaded from a subdirectory
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Popup adapter initialized');
    
    // Override the fetch request to point to the parent directory
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
        // Check if this is a popup-related fetch request
        if (url.includes('popupdata.json')) {
            console.log('Intercepting popup config fetch, redirecting to parent directory');
            // Redirect to parent directory with cache busting parameter
            const timestamp = new Date().getTime();
            return originalFetch(`../popupdata.json?nocache=${timestamp}`, options);
        }
        // Otherwise use the original fetch
        return originalFetch(url, options);
    };
    
    // Patch the createPopup function to use correct image paths with cache busting
    const originalCreatePopup = window.createPopup;
    window.createPopup = function(config) {
        // Log the received config
        console.log('Popup config received:', JSON.stringify(config));
        
        // Modify the background image path to point to parent directory with cache busting
        if (config.popup_background_image) {
            const timestamp = new Date().getTime();
            console.log('Original image path:', config.popup_background_image);
            config.popup_background_image = `../${config.popup_background_image}?nocache=${timestamp}`;
            console.log('Adjusted image path:', config.popup_background_image);
        }
        
        // Call the original function with our modified config
        return originalCreatePopup(config);
    };
    
    // Additional caching prevention
    // Force reload popup data periodically
    setInterval(function() {
        console.log('Refreshing popup configuration');
        // Fetch the latest popup data directly
        fetch(`../popupdata.json?nocache=${new Date().getTime()}`)
            .then(response => response.json())
            .then(config => {
                console.log('Refreshed popup config:', config);
                // If the popup is already created, update its background
                if (window.updatePopupBackground && config.popup_background_image) {
                    const imgPath = `../${config.popup_background_image}?nocache=${new Date().getTime()}`;
                    window.updatePopupBackground(imgPath);
                }
            })
            .catch(err => console.error('Error refreshing popup config:', err));
    }, 5000); // Check every 5 seconds
    
    // Add a method to update popup background dynamically
    window.updatePopupBackground = function(imagePath) {
        const popupElement = document.querySelector('.popup-wrapper');
        if (popupElement) {
            console.log('Updating popup background image to:', imagePath);
            popupElement.style.backgroundImage = `url('${imagePath}')`;
        }
    };
});
