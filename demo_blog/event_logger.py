"""
Event Logger for Demo Blog
Simple HTTP server that logs events directly to a file
"""
from http.server import HTTPServer, SimpleHTTPRequestHandler
import json
import os
import logging
from datetime import datetime
import urllib.parse
import threading

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger('event_logger')

# Constants
EVENT_LOG_FILE = "user_events.txt"
SERVER_HOST = "localhost"
SERVER_PORT = 8000  # Same port as the web server

class EventLoggerHandler(SimpleHTTPRequestHandler):
    """Handler for logging events and serving files"""
    
    def end_headers(self):
        """Add CORS headers to all responses"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        SimpleHTTPRequestHandler.end_headers(self)
    
    def do_OPTIONS(self):
        """Handle preflight CORS requests"""
        self.send_response(200)
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests for event logging"""
        if self.path == '/log_event':
            try:
                # Get content length
                content_length = int(self.headers['Content-Length'])
                
                # Read request body
                post_data = self.rfile.read(content_length)
                event_data = json.loads(post_data.decode('utf-8'))
                
                # Log the event
                self.log_event(event_data)
                
                # Send success response
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"success": True}).encode('utf-8'))
                
            except Exception as e:
                logger.error(f"Error processing event: {e}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"success": False, "error": str(e)}).encode('utf-8'))
        else:
            # If not a log event request, serve files as normal
            return SimpleHTTPRequestHandler.do_GET(self)
    
    def log_event(self, event_data):
        """Log event to the events file"""
        try:
            event_type = event_data.get('type', 'unknown')
            event_data_content = event_data.get('data', {})
            
            # Format the event message
            if event_type == 'pageview':
                title = event_data_content.get('title', 'Unknown page')
                message = f"The user has read blog titled \"{title}\""
            elif event_type == 'click':
                text = event_data_content.get('text', 'Unknown link')
                message = f"The user has clicked on the link \"{text}\""
            else:
                message = f"Unknown event: {event_type} - {json.dumps(event_data_content)}"
            
            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            
            # Write to file
            with open(EVENT_LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            # Log to console
            logger.info(f"Logged event: {message}")
            
        except Exception as e:
            logger.error(f"Error logging event: {e}")

def inject_tracking_code():
    """Inject tracking code into all HTML files to enable logging"""
    logger.info("Injecting tracking code into HTML files...")
    
    # Create tracking code
    tracking_code = """
    <script>
    // Direct event logger (no separate server needed)
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Direct event logger initialized');
        
        // Track page view immediately
        logEvent('pageview', {
            title: document.title,
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        
        // Track all link clicks
        document.querySelectorAll('a').forEach(function(link) {
            link.addEventListener('click', function(e) {
                // Get the link text or URL
                const linkText = this.textContent.trim() || this.getAttribute('href');
                
                logEvent('click', {
                    text: linkText,
                    href: this.getAttribute('href'),
                    timestamp: new Date().toISOString()
                });
            });
        });
        
        // Function to log events directly to our server
        function logEvent(type, data) {
            const eventData = { type: type, data: data };
            console.log('Logging event:', eventData);
            
            fetch('/log_event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(eventData)
            })
            .then(response => response.json())
            .then(result => console.log('Event logged:', result))
            .catch(error => console.error('Error logging event:', error));
        }
    });
    </script>
    """
    
    # Find all HTML files
    html_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    # Inject tracking code into each file
    for html_file in html_files:
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if tracking code is already injected
            if "Direct event logger initialized" in content:
                continue
            
            # Insert tracking code before </head>
            if '</head>' in content:
                new_content = content.replace('</head>', f'{tracking_code}\n</head>')
                
                # Write modified content back to file
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                    
                logger.info(f"Injected tracking code into {html_file}")
        except Exception as e:
            logger.error(f"Error injecting tracking code into {html_file}: {e}")

def run_server():
    """Start the HTTP server"""
    # Create event log file if it doesn't exist
    if not os.path.exists(EVENT_LOG_FILE):
        with open(EVENT_LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(f"# User Events Log - Created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        logger.info(f"Created event log file: {os.path.abspath(EVENT_LOG_FILE)}")
    
    # Inject tracking code into HTML files
    inject_tracking_code()
    
    # Start HTTP server
    server = HTTPServer((SERVER_HOST, SERVER_PORT), EventLoggerHandler)
    logger.info(f"Server running at http://{SERVER_HOST}:{SERVER_PORT}")
    logger.info(f"Events will be logged to {os.path.abspath(EVENT_LOG_FILE)}")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    finally:
        server.server_close()
        logger.info("Server closed")

if __name__ == "__main__":
    # Change to the script's directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    run_server()
