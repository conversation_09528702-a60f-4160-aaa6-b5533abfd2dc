# Default values for web.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-south-1.amazonaws.com/openengage-web
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

imagePullSecrets: [{ name: regcred }]
nameOverride: ""
fullnameOverride: ""


rollingUpdate:
  enabled: true
  maxSurge: "100%"
  maxUnavailable: "50%"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Authentication required"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header Referrer-Policy "no-referrer-when-downgrade";
      add_header X-Content-Type-Options nosniff;
      add_header X-Frame-Options "SAMEORIGIN";
      add_header X-XSS-Protection "1; mode=block";
      add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'; frame-ancestors https://*.analyticsvidhya.com" always;
      add_header Strict-Transport-Security 'max-age=63072000; includeSubDomains; preload';
      add_header Set-Cookie "HttpOnly; Secure";
      add_header Permissions-Policy "accelerometer=(), camera=(), geolocation=(), midi=(), sync-xhr=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()";
      

  hosts:
    - host: openengage.analyticsvidhya.com
      paths:
        - path: /
          pathType: Prefix
    - host: openengage.analyticsvidhya.com

      paths:
        - path: /
          pathType: Prefix

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 800m
    memory: 800Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
